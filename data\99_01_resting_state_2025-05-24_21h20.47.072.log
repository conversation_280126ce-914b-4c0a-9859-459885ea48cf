7.6150 	WARNING 	User requested fullscreen with size [1536  864], but screen is actually [1920, 1080]. Using actual size
8.2530 	EXP 	Created window1 = Window(allowGUI=True, allowStencil=True, autoLog=True, backendConf=UNKNOWN, backgroundFit=<method-wrapper '__getattribute__' of attributeSetter object at 0x000001C7DE834250>, backgroundImage=<method-wrapper '__getattribute__' of attributeSetter object at 0x000001C7DE8341F0>, bitsMode=UNKNOWN, blendMode='avg', bpc=(8, 8, 8), color=array([0, 0, 0]), colorSpace='rgb', depthBits=8, fullscr=<method-wrapper '__getattribute__' of attributeSetter object at 0x000001C7DE834040>, gamma=None, gammaErrorPolicy='raise', lms=UNKNOWN, monitor=<psychopy.monitors.calibTools.Monitor object at 0x000001C7DED771F0>, multiSample=False, name='window1', numSamples=2, pos=(0, 0), screen=0, size=array([1920, 1080]), stencilBits=8, stereo=False, title='PsychoPy', units='height', useFBO=True, useRetina=False, viewOri=0.0, viewPos=None, viewScale=None, waitBlanking=True, winType='pyglet')
8.2531 	EXP 	window1: mouseVisible = True
8.2531 	EXP 	window1: backgroundImage = ''
8.2531 	EXP 	window1: backgroundFit = 'none'
8.2549 	EXP 	window1: Attempting to measure frame rate of screen (0) ...
8.2549 	EXP 	window1: recordFrameIntervals = False
8.4187 	EXP 	window1: recordFrameIntervals = True
8.6030 	EXP 	Screen (0) actual frame rate measured at 59.75Hz
8.6031 	EXP 	window1: recordFrameIntervals = False
8.6036 	EXP 	window1: mouseVisible = False
11.0998 	EXP 	01_resting_state: status = STARTED
11.2397 	EXP 	window1: mouseVisible = True
11.3038 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
11.3040 	EXP 	window1: mouseVisible = True
11.3239 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.3240 	EXP 	window1: mouseVisible = True
11.3281 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.3283 	EXP 	window1: mouseVisible = True
11.3441 	EXP 	Created stroop_instr = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='stroop_instr', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.3443 	EXP 	window1: mouseVisible = True
11.3547 	EXP 	Created instrText_4 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-2.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_4', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.3548 	EXP 	window1: mouseVisible = True
11.3571 	EXP 	Created stim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.15, italic=False, languageStyle='LTR', name='stim', opacity=1.0, ori=0, pos=array([0., 0.]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
11.3573 	EXP 	window1: mouseVisible = True
11.3575 	EXP 	window1: mouseVisible = True
11.3579 	EXP 	window1: mouseVisible = True
11.3583 	EXP 	window1: mouseVisible = True
11.3628 	EXP 	window1: mouseVisible = True
11.3629 	EXP 	window1: mouseVisible = True
11.3634 	EXP 	window1: mouseVisible = True
11.3637 	EXP 	window1: mouseVisible = True
11.3837 	EXP 	window1: mouseVisible = True
11.3883 	EXP 	Created instrText_2 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-4.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_2', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.3885 	EXP 	window1: mouseVisible = True
11.3886 	EXP 	window1: mouseVisible = True
11.3892 	EXP 	window1: mouseVisible = True
11.3897 	EXP 	window1: mouseVisible = True
11.3937 	EXP 	window1: mouseVisible = True
11.3939 	EXP 	window1: mouseVisible = True
11.3944 	EXP 	window1: mouseVisible = True
11.3951 	EXP 	window1: mouseVisible = True
11.3989 	EXP 	window1: mouseVisible = True
11.3991 	EXP 	window1: mouseVisible = True
11.3995 	EXP 	window1: mouseVisible = True
11.3999 	EXP 	window1: mouseVisible = True
11.4044 	EXP 	window1: mouseVisible = True
11.4048 	EXP 	window1: mouseVisible = True
11.4054 	EXP 	window1: mouseVisible = True
11.4058 	EXP 	window1: mouseVisible = True
11.4094 	EXP 	window1: mouseVisible = True
11.4145 	EXP 	Created instrText_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-3.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_3', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.4146 	EXP 	window1: mouseVisible = True
11.4258 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.4261 	EXP 	window1: mouseVisible = True
11.4305 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
11.4307 	EXP 	window1: mouseVisible = True
11.4398 	EXP 	Created EndMessage = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='EndMessage', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='Thank you!', units='height', win=Window(...), wrapWidth=1)
0.0043 	EXP 	trial_counter: autoLog = True
0.0043 	EXP 	fbtxt: autoLog = True
0.0043 	EXP 	trial_counter_2: autoLog = True
0.0232 	EXP 	cross: autoDraw = True
15.0302 	EXP 	cross: autoDraw = False
15.0302 	EXP 	cross: autoDraw = False
15.0302 	EXP 	text_3: autoDraw = True
16.5654 	EXP 	window1: mouseVisible = True
16.5673 	EXP 	Created unnamed ShapeStim = ShapeStim(__class__=<class 'psychopy.visual.shape.ShapeStim'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=False, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0, 0, 0]), fillColorSpace='rgb', fillRGB=array([0, 0, 0]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=3, name='unnamed ShapeStim', opacity=0.35, ori=0.0, pos=array([0., 0.]), size=array([1., 1.]), units='height', vertices=ndarray(...), win=Window(...), windingRule=None)
16.5712 	EXP 	window1: mouseVisible = True
16.5752 	EXP 	Created unnamed Circle = Circle(__class__=<class 'psychopy.visual.circle.Circle'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=2, name='unnamed Circle', opacity=1.0, ori=0.0, pos=array([0., 0.]), size=array([1., 1.]), units='height', vertices=ndarray(...), win=Window(...))
16.5759 	EXP 	window1: mouseVisible = True
16.5780 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.05882353, 0.61568627, 0.84313725]), fillColorSpace='rgb', fillRGB=array([0.05882353, 0.61568627, 0.84313725]), interpolate=True, lineColor=array([0., 0., 0.]), lineColorSpace='rgb', lineRGB=array([0, 0, 0]), lineWidth=1.5, name='unnamed Rect', opacity=0.1, ori=0.0, pos=array([0., 0.]), size=array([1.77777778, 0.5       ]), units='height', vertices=ndarray(...), win=Window(...))
16.5781 	EXP 	window1: mouseVisible = True
16.5832 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([ 0.  , -0.35]), rgb=UNKNOWN, text='Inhale', units='height', win=Window(...), wrapWidth=1)
16.5833 	EXP 	window1: mouseVisible = True
16.5842 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([0. , 0.4]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
16.5927 	DATA 	Keypress: f1
16.5966 	EXP 	text_3: autoDraw = False
16.5966 	EXP 	unnamed TextStim: height = 0.03
16.5966 	EXP 	unnamed TextStim: height = 0.03
16.5966 	EXP 	unnamed TextStim: height = 0.03
16.5966 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.5966 	EXP 	unnamed TextStim: text = 'Inhale'
16.6127 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.6127 	EXP 	unnamed TextStim: text = 'Inhale'
16.6291 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.6291 	EXP 	unnamed TextStim: text = 'Inhale'
16.6455 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.6455 	EXP 	unnamed TextStim: text = 'Inhale'
16.6628 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.6628 	EXP 	unnamed TextStim: text = 'Inhale'
16.6793 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.6793 	EXP 	unnamed TextStim: text = 'Inhale'
16.6966 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.6966 	EXP 	unnamed TextStim: text = 'Inhale'
16.7128 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.7128 	EXP 	unnamed TextStim: text = 'Inhale'
16.7290 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.7290 	EXP 	unnamed TextStim: text = 'Inhale'
16.7455 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.7455 	EXP 	unnamed TextStim: text = 'Inhale'
16.7625 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.7625 	EXP 	unnamed TextStim: text = 'Inhale'
16.7790 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.7790 	EXP 	unnamed TextStim: text = 'Inhale'
16.7961 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.7961 	EXP 	unnamed TextStim: text = 'Inhale'
16.8132 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.8132 	EXP 	unnamed TextStim: text = 'Inhale'
16.8296 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.8296 	EXP 	unnamed TextStim: text = 'Inhale'
16.8466 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.8466 	EXP 	unnamed TextStim: text = 'Inhale'
16.8621 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.8621 	EXP 	unnamed TextStim: text = 'Inhale'
16.8786 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.8786 	EXP 	unnamed TextStim: text = 'Inhale'
16.8957 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.8957 	EXP 	unnamed TextStim: text = 'Inhale'
16.9128 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.9128 	EXP 	unnamed TextStim: text = 'Inhale'
16.9286 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.9286 	EXP 	unnamed TextStim: text = 'Inhale'
16.9452 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.9452 	EXP 	unnamed TextStim: text = 'Inhale'
16.9623 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.9623 	EXP 	unnamed TextStim: text = 'Inhale'
16.9785 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.9785 	EXP 	unnamed TextStim: text = 'Inhale'
16.9954 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
16.9954 	EXP 	unnamed TextStim: text = 'Inhale'
17.0123 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.0123 	EXP 	unnamed TextStim: text = 'Inhale'
17.0286 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.0286 	EXP 	unnamed TextStim: text = 'Inhale'
17.0453 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.0453 	EXP 	unnamed TextStim: text = 'Inhale'
17.0620 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.0620 	EXP 	unnamed TextStim: text = 'Inhale'
17.0784 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.0784 	EXP 	unnamed TextStim: text = 'Inhale'
17.0956 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.0956 	EXP 	unnamed TextStim: text = 'Inhale'
17.1124 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.1124 	EXP 	unnamed TextStim: text = 'Inhale'
17.1307 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.1307 	EXP 	unnamed TextStim: text = 'Inhale'
17.1458 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.1458 	EXP 	unnamed TextStim: text = 'Inhale'
17.1625 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.1625 	EXP 	unnamed TextStim: text = 'Inhale'
17.1794 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.1794 	EXP 	unnamed TextStim: text = 'Inhale'
17.1953 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.1953 	EXP 	unnamed TextStim: text = 'Inhale'
17.2123 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.2123 	EXP 	unnamed TextStim: text = 'Inhale'
17.2290 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.2290 	EXP 	unnamed TextStim: text = 'Inhale'
17.2452 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.2452 	EXP 	unnamed TextStim: text = 'Inhale'
17.2624 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.2624 	EXP 	unnamed TextStim: text = 'Inhale'
17.2787 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.2787 	EXP 	unnamed TextStim: text = 'Inhale'
17.2955 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.2955 	EXP 	unnamed TextStim: text = 'Inhale'
17.3118 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.3118 	EXP 	unnamed TextStim: text = 'Inhale'
17.3287 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.3287 	EXP 	unnamed TextStim: text = 'Inhale'
17.3453 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.3453 	EXP 	unnamed TextStim: text = 'Inhale'
17.3624 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.3624 	EXP 	unnamed TextStim: text = 'Inhale'
17.3785 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.3785 	EXP 	unnamed TextStim: text = 'Inhale'
17.3950 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.3950 	EXP 	unnamed TextStim: text = 'Inhale'
17.4127 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.4127 	EXP 	unnamed TextStim: text = 'Inhale'
17.4281 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.4281 	EXP 	unnamed TextStim: text = 'Inhale'
17.4446 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.4446 	EXP 	unnamed TextStim: text = 'Inhale'
17.4618 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.4618 	EXP 	unnamed TextStim: text = 'Inhale'
17.4780 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.4780 	EXP 	unnamed TextStim: text = 'Inhale'
17.4950 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.4950 	EXP 	unnamed TextStim: text = 'Inhale'
17.5120 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.5120 	EXP 	unnamed TextStim: text = 'Inhale'
17.5284 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.5284 	EXP 	unnamed TextStim: text = 'Inhale'
17.5448 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.5448 	EXP 	unnamed TextStim: text = 'Inhale'
17.5618 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.5618 	EXP 	unnamed TextStim: text = 'Inhale'
17.5780 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.5780 	EXP 	unnamed TextStim: text = 'Inhale'
17.5948 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
17.5948 	EXP 	unnamed TextStim: text = 'Inhale'
17.6118 	EXP 	unnamed TextStim: height = 0.03
17.6118 	EXP 	unnamed TextStim: height = 0.03
17.6118 	EXP 	unnamed TextStim: height = 0.03
17.6118 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.6118 	EXP 	unnamed TextStim: text = 'Inhale'
17.6281 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.6281 	EXP 	unnamed TextStim: text = 'Inhale'
17.6451 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.6451 	EXP 	unnamed TextStim: text = 'Inhale'
17.6620 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.6620 	EXP 	unnamed TextStim: text = 'Inhale'
17.6798 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.6798 	EXP 	unnamed TextStim: text = 'Inhale'
17.6952 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.6952 	EXP 	unnamed TextStim: text = 'Inhale'
17.7117 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.7117 	EXP 	unnamed TextStim: text = 'Inhale'
17.7280 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.7280 	EXP 	unnamed TextStim: text = 'Inhale'
17.7446 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.7446 	EXP 	unnamed TextStim: text = 'Inhale'
17.7617 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.7617 	EXP 	unnamed TextStim: text = 'Inhale'
17.7781 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.7781 	EXP 	unnamed TextStim: text = 'Inhale'
17.7948 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.7948 	EXP 	unnamed TextStim: text = 'Inhale'
17.8115 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.8115 	EXP 	unnamed TextStim: text = 'Inhale'
17.8280 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.8280 	EXP 	unnamed TextStim: text = 'Inhale'
17.8449 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.8449 	EXP 	unnamed TextStim: text = 'Inhale'
17.8619 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.8619 	EXP 	unnamed TextStim: text = 'Inhale'
17.8782 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.8782 	EXP 	unnamed TextStim: text = 'Inhale'
17.8947 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.8947 	EXP 	unnamed TextStim: text = 'Inhale'
17.9128 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.9128 	EXP 	unnamed TextStim: text = 'Inhale'
17.9282 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.9282 	EXP 	unnamed TextStim: text = 'Inhale'
17.9447 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.9447 	EXP 	unnamed TextStim: text = 'Inhale'
17.9615 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.9615 	EXP 	unnamed TextStim: text = 'Inhale'
17.9783 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.9783 	EXP 	unnamed TextStim: text = 'Inhale'
17.9952 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
17.9952 	EXP 	unnamed TextStim: text = 'Inhale'
18.0120 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.0120 	EXP 	unnamed TextStim: text = 'Inhale'
18.0281 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.0281 	EXP 	unnamed TextStim: text = 'Inhale'
18.0444 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.0444 	EXP 	unnamed TextStim: text = 'Inhale'
18.0612 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.0612 	EXP 	unnamed TextStim: text = 'Inhale'
18.0777 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.0777 	EXP 	unnamed TextStim: text = 'Inhale'
18.0945 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.0945 	EXP 	unnamed TextStim: text = 'Inhale'
18.1113 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.1113 	EXP 	unnamed TextStim: text = 'Inhale'
18.1279 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.1279 	EXP 	unnamed TextStim: text = 'Inhale'
18.1447 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.1447 	EXP 	unnamed TextStim: text = 'Inhale'
18.1613 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.1613 	EXP 	unnamed TextStim: text = 'Inhale'
18.1779 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.1779 	EXP 	unnamed TextStim: text = 'Inhale'
18.1949 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.1949 	EXP 	unnamed TextStim: text = 'Inhale'
18.2116 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.2116 	EXP 	unnamed TextStim: text = 'Inhale'
18.2284 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.2284 	EXP 	unnamed TextStim: text = 'Inhale'
18.2444 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.2444 	EXP 	unnamed TextStim: text = 'Inhale'
18.2614 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.2614 	EXP 	unnamed TextStim: text = 'Inhale'
18.2778 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.2778 	EXP 	unnamed TextStim: text = 'Inhale'
18.2944 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.2944 	EXP 	unnamed TextStim: text = 'Inhale'
18.3108 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.3108 	EXP 	unnamed TextStim: text = 'Inhale'
18.3270 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.3270 	EXP 	unnamed TextStim: text = 'Inhale'
18.3447 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.3447 	EXP 	unnamed TextStim: text = 'Inhale'
18.3614 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.3614 	EXP 	unnamed TextStim: text = 'Inhale'
18.3776 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.3776 	EXP 	unnamed TextStim: text = 'Inhale'
18.3943 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.3943 	EXP 	unnamed TextStim: text = 'Inhale'
18.4110 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.4110 	EXP 	unnamed TextStim: text = 'Inhale'
18.4277 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.4277 	EXP 	unnamed TextStim: text = 'Inhale'
18.4443 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.4443 	EXP 	unnamed TextStim: text = 'Inhale'
18.4611 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.4611 	EXP 	unnamed TextStim: text = 'Inhale'
18.4778 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.4778 	EXP 	unnamed TextStim: text = 'Inhale'
18.4945 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.4945 	EXP 	unnamed TextStim: text = 'Inhale'
18.5112 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.5112 	EXP 	unnamed TextStim: text = 'Inhale'
18.5280 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.5280 	EXP 	unnamed TextStim: text = 'Inhale'
18.5444 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.5444 	EXP 	unnamed TextStim: text = 'Inhale'
18.5610 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.5610 	EXP 	unnamed TextStim: text = 'Inhale'
18.5778 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.5778 	EXP 	unnamed TextStim: text = 'Inhale'
18.5941 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
18.5941 	EXP 	unnamed TextStim: text = 'Inhale'
18.6107 	EXP 	unnamed TextStim: height = 0.03
18.6107 	EXP 	unnamed TextStim: height = 0.03
18.6107 	EXP 	unnamed TextStim: height = 0.03
18.6107 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.6107 	EXP 	unnamed TextStim: text = 'Inhale'
18.6271 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.6271 	EXP 	unnamed TextStim: text = 'Inhale'
18.6440 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.6440 	EXP 	unnamed TextStim: text = 'Inhale'
18.6611 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.6611 	EXP 	unnamed TextStim: text = 'Inhale'
18.6777 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.6777 	EXP 	unnamed TextStim: text = 'Inhale'
18.6939 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.6939 	EXP 	unnamed TextStim: text = 'Inhale'
18.7116 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.7116 	EXP 	unnamed TextStim: text = 'Inhale'
18.7285 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.7285 	EXP 	unnamed TextStim: text = 'Inhale'
18.7451 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.7451 	EXP 	unnamed TextStim: text = 'Inhale'
18.7619 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.7619 	EXP 	unnamed TextStim: text = 'Inhale'
18.7799 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.7799 	EXP 	unnamed TextStim: text = 'Inhale'
18.7948 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.7948 	EXP 	unnamed TextStim: text = 'Inhale'
18.8111 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.8111 	EXP 	unnamed TextStim: text = 'Inhale'
18.8280 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.8280 	EXP 	unnamed TextStim: text = 'Inhale'
18.8441 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.8441 	EXP 	unnamed TextStim: text = 'Inhale'
18.8607 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.8607 	EXP 	unnamed TextStim: text = 'Inhale'
18.8772 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.8772 	EXP 	unnamed TextStim: text = 'Inhale'
18.8955 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.8955 	EXP 	unnamed TextStim: text = 'Inhale'
18.9114 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.9114 	EXP 	unnamed TextStim: text = 'Inhale'
18.9279 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.9279 	EXP 	unnamed TextStim: text = 'Inhale'
18.9450 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.9450 	EXP 	unnamed TextStim: text = 'Inhale'
18.9617 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.9617 	EXP 	unnamed TextStim: text = 'Inhale'
18.9782 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.9782 	EXP 	unnamed TextStim: text = 'Inhale'
18.9947 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
18.9947 	EXP 	unnamed TextStim: text = 'Inhale'
19.0116 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.0116 	EXP 	unnamed TextStim: text = 'Inhale'
19.0281 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.0281 	EXP 	unnamed TextStim: text = 'Inhale'
19.0457 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.0457 	EXP 	unnamed TextStim: text = 'Inhale'
19.0620 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.0620 	EXP 	unnamed TextStim: text = 'Inhale'
19.0778 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.0778 	EXP 	unnamed TextStim: text = 'Inhale'
19.0941 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.0941 	EXP 	unnamed TextStim: text = 'Inhale'
19.1111 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.1111 	EXP 	unnamed TextStim: text = 'Inhale'
19.1276 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.1276 	EXP 	unnamed TextStim: text = 'Inhale'
19.1443 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.1443 	EXP 	unnamed TextStim: text = 'Inhale'
19.1613 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.1613 	EXP 	unnamed TextStim: text = 'Inhale'
19.1778 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.1778 	EXP 	unnamed TextStim: text = 'Inhale'
19.1944 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.1944 	EXP 	unnamed TextStim: text = 'Inhale'
19.2122 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.2122 	EXP 	unnamed TextStim: text = 'Inhale'
19.2281 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.2281 	EXP 	unnamed TextStim: text = 'Inhale'
19.2447 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.2447 	EXP 	unnamed TextStim: text = 'Inhale'
19.2613 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.2613 	EXP 	unnamed TextStim: text = 'Inhale'
19.2778 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.2778 	EXP 	unnamed TextStim: text = 'Inhale'
19.2945 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.2945 	EXP 	unnamed TextStim: text = 'Inhale'
19.3111 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.3111 	EXP 	unnamed TextStim: text = 'Inhale'
19.3291 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.3291 	EXP 	unnamed TextStim: text = 'Inhale'
19.3445 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.3445 	EXP 	unnamed TextStim: text = 'Inhale'
19.3610 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.3610 	EXP 	unnamed TextStim: text = 'Inhale'
19.3777 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.3777 	EXP 	unnamed TextStim: text = 'Inhale'
19.3943 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.3943 	EXP 	unnamed TextStim: text = 'Inhale'
19.4110 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.4110 	EXP 	unnamed TextStim: text = 'Inhale'
19.4268 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.4268 	EXP 	unnamed TextStim: text = 'Inhale'
19.4444 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.4444 	EXP 	unnamed TextStim: text = 'Inhale'
19.4608 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.4608 	EXP 	unnamed TextStim: text = 'Inhale'
19.4775 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.4775 	EXP 	unnamed TextStim: text = 'Inhale'
19.4944 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.4944 	EXP 	unnamed TextStim: text = 'Inhale'
19.5112 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.5112 	EXP 	unnamed TextStim: text = 'Inhale'
19.5273 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.5273 	EXP 	unnamed TextStim: text = 'Inhale'
19.5445 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.5445 	EXP 	unnamed TextStim: text = 'Inhale'
19.5615 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.5615 	EXP 	unnamed TextStim: text = 'Inhale'
19.5774 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.5774 	EXP 	unnamed TextStim: text = 'Inhale'
19.5940 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
19.5940 	EXP 	unnamed TextStim: text = 'Inhale'
19.6108 	EXP 	unnamed TextStim: height = 0.03
19.6108 	EXP 	unnamed TextStim: height = 0.03
19.6108 	EXP 	unnamed TextStim: height = 0.03
19.6108 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.6108 	EXP 	unnamed TextStim: text = 'Inhale'
19.6273 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.6273 	EXP 	unnamed TextStim: text = 'Inhale'
19.6440 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.6440 	EXP 	unnamed TextStim: text = 'Inhale'
19.6611 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.6611 	EXP 	unnamed TextStim: text = 'Inhale'
19.6774 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.6774 	EXP 	unnamed TextStim: text = 'Inhale'
19.6941 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.6941 	EXP 	unnamed TextStim: text = 'Inhale'
19.7109 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.7109 	EXP 	unnamed TextStim: text = 'Inhale'
19.7273 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.7273 	EXP 	unnamed TextStim: text = 'Inhale'
19.7445 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.7445 	EXP 	unnamed TextStim: text = 'Inhale'
19.7610 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.7610 	EXP 	unnamed TextStim: text = 'Inhale'
19.7774 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.7774 	EXP 	unnamed TextStim: text = 'Inhale'
19.7937 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.7937 	EXP 	unnamed TextStim: text = 'Inhale'
19.8107 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.8107 	EXP 	unnamed TextStim: text = 'Inhale'
19.8273 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.8273 	EXP 	unnamed TextStim: text = 'Inhale'
19.8440 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.8440 	EXP 	unnamed TextStim: text = 'Inhale'
19.8609 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.8609 	EXP 	unnamed TextStim: text = 'Inhale'
19.8769 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.8769 	EXP 	unnamed TextStim: text = 'Inhale'
19.8941 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.8941 	EXP 	unnamed TextStim: text = 'Inhale'
19.9107 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.9107 	EXP 	unnamed TextStim: text = 'Inhale'
19.9305 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.9305 	EXP 	unnamed TextStim: text = 'Inhale'
19.9444 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.9444 	EXP 	unnamed TextStim: text = 'Inhale'
19.9632 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.9632 	EXP 	unnamed TextStim: text = 'Inhale'
19.9778 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.9778 	EXP 	unnamed TextStim: text = 'Inhale'
19.9944 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
19.9944 	EXP 	unnamed TextStim: text = 'Inhale'
20.0111 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.0111 	EXP 	unnamed TextStim: text = 'Inhale'
20.0278 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.0278 	EXP 	unnamed TextStim: text = 'Inhale'
20.0451 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.0451 	EXP 	unnamed TextStim: text = 'Inhale'
20.0603 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.0603 	EXP 	unnamed TextStim: text = 'Inhale'
20.0771 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.0771 	EXP 	unnamed TextStim: text = 'Inhale'
20.0939 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.0939 	EXP 	unnamed TextStim: text = 'Inhale'
20.1103 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.1103 	EXP 	unnamed TextStim: text = 'Inhale'
20.1267 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.1267 	EXP 	unnamed TextStim: text = 'Inhale'
20.1438 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.1438 	EXP 	unnamed TextStim: text = 'Inhale'
20.1611 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.1611 	EXP 	unnamed TextStim: text = 'Inhale'
20.1769 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.1769 	EXP 	unnamed TextStim: text = 'Inhale'
20.1940 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.1940 	EXP 	unnamed TextStim: text = 'Inhale'
20.2110 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.2110 	EXP 	unnamed TextStim: text = 'Inhale'
20.2273 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.2273 	EXP 	unnamed TextStim: text = 'Inhale'
20.2441 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.2441 	EXP 	unnamed TextStim: text = 'Inhale'
20.2608 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.2608 	EXP 	unnamed TextStim: text = 'Inhale'
20.2774 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.2774 	EXP 	unnamed TextStim: text = 'Inhale'
20.2948 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.2948 	EXP 	unnamed TextStim: text = 'Inhale'
20.3115 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.3115 	EXP 	unnamed TextStim: text = 'Inhale'
20.3268 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.3268 	EXP 	unnamed TextStim: text = 'Inhale'
20.3438 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.3438 	EXP 	unnamed TextStim: text = 'Inhale'
20.3604 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.3604 	EXP 	unnamed TextStim: text = 'Inhale'
20.3766 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.3766 	EXP 	unnamed TextStim: text = 'Inhale'
20.3935 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.3935 	EXP 	unnamed TextStim: text = 'Inhale'
20.4099 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.4099 	EXP 	unnamed TextStim: text = 'Inhale'
20.4266 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.4266 	EXP 	unnamed TextStim: text = 'Inhale'
20.4438 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.4438 	EXP 	unnamed TextStim: text = 'Inhale'
20.4602 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.4602 	EXP 	unnamed TextStim: text = 'Inhale'
20.4767 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.4767 	EXP 	unnamed TextStim: text = 'Inhale'
20.4937 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.4937 	EXP 	unnamed TextStim: text = 'Inhale'
20.5103 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.5103 	EXP 	unnamed TextStim: text = 'Inhale'
20.5269 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.5269 	EXP 	unnamed TextStim: text = 'Inhale'
20.5439 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.5439 	EXP 	unnamed TextStim: text = 'Inhale'
20.5606 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.5606 	EXP 	unnamed TextStim: text = 'Inhale'
20.5771 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.5771 	EXP 	unnamed TextStim: text = 'Inhale'
20.5939 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
20.5939 	EXP 	unnamed TextStim: text = 'Inhale'
20.6107 	EXP 	unnamed TextStim: height = 0.03
20.6107 	EXP 	unnamed TextStim: height = 0.03
20.6107 	EXP 	unnamed TextStim: height = 0.03
20.6107 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.6107 	EXP 	unnamed TextStim: text = 'Inhale'
20.6259 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.6259 	EXP 	unnamed TextStim: text = 'Inhale'
20.6435 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.6435 	EXP 	unnamed TextStim: text = 'Inhale'
20.6599 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.6599 	EXP 	unnamed TextStim: text = 'Inhale'
20.6763 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.6763 	EXP 	unnamed TextStim: text = 'Inhale'
20.6934 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.6934 	EXP 	unnamed TextStim: text = 'Inhale'
20.7089 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.7089 	EXP 	unnamed TextStim: text = 'Inhale'
20.7257 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.7257 	EXP 	unnamed TextStim: text = 'Inhale'
20.7427 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.7427 	EXP 	unnamed TextStim: text = 'Inhale'
20.7596 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.7596 	EXP 	unnamed TextStim: text = 'Inhale'
20.7758 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.7758 	EXP 	unnamed TextStim: text = 'Inhale'
20.7929 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.7929 	EXP 	unnamed TextStim: text = 'Inhale'
20.8093 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.8093 	EXP 	unnamed TextStim: text = 'Inhale'
20.8259 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.8259 	EXP 	unnamed TextStim: text = 'Inhale'
20.8428 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.8428 	EXP 	unnamed TextStim: text = 'Inhale'
20.8586 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.8586 	EXP 	unnamed TextStim: text = 'Inhale'
20.8761 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.8761 	EXP 	unnamed TextStim: text = 'Inhale'
20.8920 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.8920 	EXP 	unnamed TextStim: text = 'Inhale'
20.9097 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.9097 	EXP 	unnamed TextStim: text = 'Inhale'
20.9256 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.9256 	EXP 	unnamed TextStim: text = 'Inhale'
20.9424 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.9424 	EXP 	unnamed TextStim: text = 'Inhale'
20.9605 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.9605 	EXP 	unnamed TextStim: text = 'Inhale'
20.9758 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.9758 	EXP 	unnamed TextStim: text = 'Inhale'
20.9925 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.9925 	EXP 	unnamed TextStim: text = 'Inhale'
21.0090 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.0090 	EXP 	unnamed TextStim: text = 'Inhale'
21.0249 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.0249 	EXP 	unnamed TextStim: text = 'Inhale'
21.0426 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.0426 	EXP 	unnamed TextStim: text = 'Inhale'
21.0592 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.0592 	EXP 	unnamed TextStim: text = 'Inhale'
21.0757 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.0757 	EXP 	unnamed TextStim: text = 'Inhale'
21.0928 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.0928 	EXP 	unnamed TextStim: text = 'Inhale'
21.1095 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.1095 	EXP 	unnamed TextStim: height = 0.05
21.1095 	EXP 	unnamed TextStim: height = 0.05
21.1095 	EXP 	unnamed TextStim: height = 0.05
21.1095 	EXP 	unnamed TextStim: text = 'Hold'
21.1254 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.1254 	EXP 	unnamed TextStim: text = 'Hold'
21.1422 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.1422 	EXP 	unnamed TextStim: text = 'Hold'
21.1580 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.1580 	EXP 	unnamed TextStim: text = 'Hold'
21.1758 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.1758 	EXP 	unnamed TextStim: text = 'Hold'
21.1922 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.1922 	EXP 	unnamed TextStim: text = 'Hold'
21.2086 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.2086 	EXP 	unnamed TextStim: text = 'Hold'
21.2253 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.2253 	EXP 	unnamed TextStim: text = 'Hold'
21.2422 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.2422 	EXP 	unnamed TextStim: text = 'Hold'
21.2590 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.2590 	EXP 	unnamed TextStim: text = 'Hold'
21.2755 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.2755 	EXP 	unnamed TextStim: text = 'Hold'
21.2922 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.2922 	EXP 	unnamed TextStim: text = 'Hold'
21.3088 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.3088 	EXP 	unnamed TextStim: text = 'Hold'
21.3255 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.3255 	EXP 	unnamed TextStim: text = 'Hold'
21.3423 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.3423 	EXP 	unnamed TextStim: text = 'Hold'
21.3586 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.3586 	EXP 	unnamed TextStim: text = 'Hold'
21.3749 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.3749 	EXP 	unnamed TextStim: text = 'Hold'
21.3916 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.3916 	EXP 	unnamed TextStim: text = 'Hold'
21.4082 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.4082 	EXP 	unnamed TextStim: text = 'Hold'
21.4252 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.4252 	EXP 	unnamed TextStim: text = 'Hold'
21.4424 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.4424 	EXP 	unnamed TextStim: text = 'Hold'
21.4586 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.4586 	EXP 	unnamed TextStim: text = 'Hold'
21.4759 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.4759 	EXP 	unnamed TextStim: text = 'Hold'
21.4919 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.4919 	EXP 	unnamed TextStim: text = 'Hold'
21.5104 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.5104 	EXP 	unnamed TextStim: text = 'Hold'
21.5258 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.5258 	EXP 	unnamed TextStim: text = 'Hold'
21.5428 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.5428 	EXP 	unnamed TextStim: text = 'Hold'
21.5584 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.5584 	EXP 	unnamed TextStim: text = 'Hold'
21.5756 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.5756 	EXP 	unnamed TextStim: text = 'Hold'
21.5922 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.5922 	EXP 	unnamed TextStim: text = 'Hold'
21.6089 	EXP 	unnamed TextStim: height = 0.03
21.6089 	EXP 	unnamed TextStim: height = 0.03
21.6089 	EXP 	unnamed TextStim: height = 0.03
21.6089 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.6089 	EXP 	unnamed TextStim: height = 0.05
21.6089 	EXP 	unnamed TextStim: height = 0.05
21.6089 	EXP 	unnamed TextStim: height = 0.05
21.6089 	EXP 	unnamed TextStim: text = 'Exhale'
21.6247 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.6247 	EXP 	unnamed TextStim: text = 'Exhale'
21.6416 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.6416 	EXP 	unnamed TextStim: text = 'Exhale'
21.6582 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.6582 	EXP 	unnamed TextStim: text = 'Exhale'
21.6748 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.6748 	EXP 	unnamed TextStim: text = 'Exhale'
21.6926 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.6926 	EXP 	unnamed TextStim: text = 'Exhale'
21.7084 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.7084 	EXP 	unnamed TextStim: text = 'Exhale'
21.7249 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.7249 	EXP 	unnamed TextStim: text = 'Exhale'
21.7414 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.7414 	EXP 	unnamed TextStim: text = 'Exhale'
21.7584 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.7584 	EXP 	unnamed TextStim: text = 'Exhale'
21.7750 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.7750 	EXP 	unnamed TextStim: text = 'Exhale'
21.7915 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.7915 	EXP 	unnamed TextStim: text = 'Exhale'
21.8086 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.8086 	EXP 	unnamed TextStim: text = 'Exhale'
21.8251 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.8251 	EXP 	unnamed TextStim: text = 'Exhale'
21.8420 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.8420 	EXP 	unnamed TextStim: text = 'Exhale'
21.8589 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.8589 	EXP 	unnamed TextStim: text = 'Exhale'
21.8757 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.8757 	EXP 	unnamed TextStim: text = 'Exhale'
21.8925 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.8925 	EXP 	unnamed TextStim: text = 'Exhale'
21.9087 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.9087 	EXP 	unnamed TextStim: text = 'Exhale'
21.9273 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.9273 	EXP 	unnamed TextStim: text = 'Exhale'
21.9448 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.9448 	EXP 	unnamed TextStim: text = 'Exhale'
21.9599 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.9599 	EXP 	unnamed TextStim: text = 'Exhale'
21.9761 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.9761 	EXP 	unnamed TextStim: text = 'Exhale'
21.9923 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.9923 	EXP 	unnamed TextStim: text = 'Exhale'
22.0094 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.0094 	EXP 	unnamed TextStim: text = 'Exhale'
22.0257 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.0257 	EXP 	unnamed TextStim: text = 'Exhale'
22.0416 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.0416 	EXP 	unnamed TextStim: text = 'Exhale'
22.0588 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.0588 	EXP 	unnamed TextStim: text = 'Exhale'
22.0769 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.0769 	EXP 	unnamed TextStim: text = 'Exhale'
22.0926 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.0926 	EXP 	unnamed TextStim: text = 'Exhale'
22.1089 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.1089 	EXP 	unnamed TextStim: text = 'Exhale'
22.1253 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.1253 	EXP 	unnamed TextStim: text = 'Exhale'
22.1421 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.1421 	EXP 	unnamed TextStim: text = 'Exhale'
22.1587 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.1587 	EXP 	unnamed TextStim: text = 'Exhale'
22.1753 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.1753 	EXP 	unnamed TextStim: text = 'Exhale'
22.1924 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.1924 	EXP 	unnamed TextStim: text = 'Exhale'
22.2091 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.2091 	EXP 	unnamed TextStim: text = 'Exhale'
22.2255 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.2255 	EXP 	unnamed TextStim: text = 'Exhale'
22.2423 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.2423 	EXP 	unnamed TextStim: text = 'Exhale'
22.2589 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.2589 	EXP 	unnamed TextStim: text = 'Exhale'
22.2755 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.2755 	EXP 	unnamed TextStim: text = 'Exhale'
22.2923 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.2923 	EXP 	unnamed TextStim: text = 'Exhale'
22.3084 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.3084 	EXP 	unnamed TextStim: text = 'Exhale'
22.3251 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.3251 	EXP 	unnamed TextStim: text = 'Exhale'
22.3423 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.3423 	EXP 	unnamed TextStim: text = 'Exhale'
22.3585 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.3585 	EXP 	unnamed TextStim: text = 'Exhale'
22.3753 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.3753 	EXP 	unnamed TextStim: text = 'Exhale'
22.3922 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.3922 	EXP 	unnamed TextStim: text = 'Exhale'
22.4087 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.4087 	EXP 	unnamed TextStim: text = 'Exhale'
22.4252 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.4252 	EXP 	unnamed TextStim: text = 'Exhale'
22.4420 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.4420 	EXP 	unnamed TextStim: text = 'Exhale'
22.4589 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.4589 	EXP 	unnamed TextStim: text = 'Exhale'
22.4754 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.4754 	EXP 	unnamed TextStim: text = 'Exhale'
22.4924 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.4924 	EXP 	unnamed TextStim: text = 'Exhale'
22.5087 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.5087 	EXP 	unnamed TextStim: text = 'Exhale'
22.5253 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.5253 	EXP 	unnamed TextStim: text = 'Exhale'
22.5419 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.5419 	EXP 	unnamed TextStim: text = 'Exhale'
22.5573 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.5573 	EXP 	unnamed TextStim: text = 'Exhale'
22.5740 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.5740 	EXP 	unnamed TextStim: text = 'Exhale'
22.5908 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.5908 	EXP 	unnamed TextStim: text = 'Exhale'
22.6075 	EXP 	unnamed TextStim: height = 0.03
22.6075 	EXP 	unnamed TextStim: height = 0.03
22.6075 	EXP 	unnamed TextStim: height = 0.03
22.6075 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.6075 	EXP 	unnamed TextStim: text = 'Exhale'
22.6246 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.6246 	EXP 	unnamed TextStim: text = 'Exhale'
22.6412 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.6412 	EXP 	unnamed TextStim: text = 'Exhale'
22.6576 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.6576 	EXP 	unnamed TextStim: text = 'Exhale'
22.6741 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.6741 	EXP 	unnamed TextStim: text = 'Exhale'
22.6911 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.6911 	EXP 	unnamed TextStim: text = 'Exhale'
22.7077 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.7077 	EXP 	unnamed TextStim: text = 'Exhale'
22.7241 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.7241 	EXP 	unnamed TextStim: text = 'Exhale'
22.7404 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.7404 	EXP 	unnamed TextStim: text = 'Exhale'
22.7574 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.7574 	EXP 	unnamed TextStim: text = 'Exhale'
22.7736 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.7736 	EXP 	unnamed TextStim: text = 'Exhale'
22.7904 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.7904 	EXP 	unnamed TextStim: text = 'Exhale'
22.8071 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.8071 	EXP 	unnamed TextStim: text = 'Exhale'
22.8240 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.8240 	EXP 	unnamed TextStim: text = 'Exhale'
22.8408 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.8408 	EXP 	unnamed TextStim: text = 'Exhale'
22.8574 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.8574 	EXP 	unnamed TextStim: text = 'Exhale'
22.8742 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.8742 	EXP 	unnamed TextStim: text = 'Exhale'
22.8908 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.8908 	EXP 	unnamed TextStim: text = 'Exhale'
22.9071 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.9071 	EXP 	unnamed TextStim: text = 'Exhale'
22.9235 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.9235 	EXP 	unnamed TextStim: text = 'Exhale'
22.9408 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.9408 	EXP 	unnamed TextStim: text = 'Exhale'
22.9574 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.9574 	EXP 	unnamed TextStim: text = 'Exhale'
22.9742 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.9742 	EXP 	unnamed TextStim: text = 'Exhale'
22.9910 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.9910 	EXP 	unnamed TextStim: text = 'Exhale'
23.0078 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.0078 	EXP 	unnamed TextStim: text = 'Exhale'
23.0238 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.0238 	EXP 	unnamed TextStim: text = 'Exhale'
23.0404 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.0404 	EXP 	unnamed TextStim: text = 'Exhale'
23.0571 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.0571 	EXP 	unnamed TextStim: text = 'Exhale'
23.0738 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.0738 	EXP 	unnamed TextStim: text = 'Exhale'
23.0904 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.0904 	EXP 	unnamed TextStim: text = 'Exhale'
23.1072 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.1072 	EXP 	unnamed TextStim: text = 'Exhale'
23.1240 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.1240 	EXP 	unnamed TextStim: text = 'Exhale'
23.1407 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.1407 	EXP 	unnamed TextStim: text = 'Exhale'
23.1572 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.1572 	EXP 	unnamed TextStim: text = 'Exhale'
23.1739 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.1739 	EXP 	unnamed TextStim: text = 'Exhale'
23.1906 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.1906 	EXP 	unnamed TextStim: text = 'Exhale'
23.2073 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.2073 	EXP 	unnamed TextStim: text = 'Exhale'
23.2240 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.2240 	EXP 	unnamed TextStim: text = 'Exhale'
23.2408 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.2408 	EXP 	unnamed TextStim: text = 'Exhale'
23.2574 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.2574 	EXP 	unnamed TextStim: text = 'Exhale'
23.2740 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.2740 	EXP 	unnamed TextStim: text = 'Exhale'
23.2907 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.2907 	EXP 	unnamed TextStim: text = 'Exhale'
23.3071 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.3071 	EXP 	unnamed TextStim: text = 'Exhale'
23.3237 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.3237 	EXP 	unnamed TextStim: text = 'Exhale'
23.3404 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.3404 	EXP 	unnamed TextStim: text = 'Exhale'
23.3571 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.3571 	EXP 	unnamed TextStim: text = 'Exhale'
23.3737 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.3737 	EXP 	unnamed TextStim: text = 'Exhale'
23.3906 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.3906 	EXP 	unnamed TextStim: text = 'Exhale'
23.4073 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.4073 	EXP 	unnamed TextStim: text = 'Exhale'
23.4235 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.4235 	EXP 	unnamed TextStim: text = 'Exhale'
23.4404 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.4404 	EXP 	unnamed TextStim: text = 'Exhale'
23.4572 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.4572 	EXP 	unnamed TextStim: text = 'Exhale'
23.4735 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.4735 	EXP 	unnamed TextStim: text = 'Exhale'
23.4908 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.4908 	EXP 	unnamed TextStim: text = 'Exhale'
23.5073 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.5073 	EXP 	unnamed TextStim: text = 'Exhale'
23.5233 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.5233 	EXP 	unnamed TextStim: text = 'Exhale'
23.5403 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.5403 	EXP 	unnamed TextStim: text = 'Exhale'
23.5568 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.5568 	EXP 	unnamed TextStim: text = 'Exhale'
23.5733 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.5733 	EXP 	unnamed TextStim: text = 'Exhale'
23.5902 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.5902 	EXP 	unnamed TextStim: text = 'Exhale'
23.6066 	EXP 	unnamed TextStim: height = 0.03
23.6066 	EXP 	unnamed TextStim: height = 0.03
23.6066 	EXP 	unnamed TextStim: height = 0.03
23.6066 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.6066 	EXP 	unnamed TextStim: text = 'Exhale'
23.6232 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.6232 	EXP 	unnamed TextStim: text = 'Exhale'
23.6397 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.6397 	EXP 	unnamed TextStim: text = 'Exhale'
23.6572 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.6572 	EXP 	unnamed TextStim: text = 'Exhale'
23.6736 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.6736 	EXP 	unnamed TextStim: text = 'Exhale'
23.6901 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.6901 	EXP 	unnamed TextStim: text = 'Exhale'
23.7068 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.7068 	EXP 	unnamed TextStim: text = 'Exhale'
23.7235 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.7235 	EXP 	unnamed TextStim: text = 'Exhale'
23.7415 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.7415 	EXP 	unnamed TextStim: text = 'Exhale'
23.7566 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.7566 	EXP 	unnamed TextStim: text = 'Exhale'
23.7731 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.7731 	EXP 	unnamed TextStim: text = 'Exhale'
23.7898 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.7898 	EXP 	unnamed TextStim: text = 'Exhale'
23.8067 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.8067 	EXP 	unnamed TextStim: text = 'Exhale'
23.8233 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.8233 	EXP 	unnamed TextStim: text = 'Exhale'
23.8402 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.8402 	EXP 	unnamed TextStim: text = 'Exhale'
23.8566 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.8566 	EXP 	unnamed TextStim: text = 'Exhale'
23.8735 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.8735 	EXP 	unnamed TextStim: text = 'Exhale'
23.8901 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.8901 	EXP 	unnamed TextStim: text = 'Exhale'
23.9070 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.9070 	EXP 	unnamed TextStim: text = 'Exhale'
23.9261 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.9261 	EXP 	unnamed TextStim: text = 'Exhale'
23.9402 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.9402 	EXP 	unnamed TextStim: text = 'Exhale'
23.9569 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.9569 	EXP 	unnamed TextStim: text = 'Exhale'
23.9733 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.9733 	EXP 	unnamed TextStim: text = 'Exhale'
23.9898 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.9898 	EXP 	unnamed TextStim: text = 'Exhale'
24.0064 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.0064 	EXP 	unnamed TextStim: text = 'Exhale'
24.0231 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.0231 	EXP 	unnamed TextStim: text = 'Exhale'
24.0400 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.0400 	EXP 	unnamed TextStim: text = 'Exhale'
24.0566 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.0566 	EXP 	unnamed TextStim: text = 'Exhale'
24.0738 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.0738 	EXP 	unnamed TextStim: text = 'Exhale'
24.0898 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.0898 	EXP 	unnamed TextStim: text = 'Exhale'
24.1065 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.1065 	EXP 	unnamed TextStim: text = 'Exhale'
24.1233 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.1233 	EXP 	unnamed TextStim: text = 'Exhale'
24.1399 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.1399 	EXP 	unnamed TextStim: text = 'Exhale'
24.1568 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.1568 	EXP 	unnamed TextStim: text = 'Exhale'
24.1732 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.1732 	EXP 	unnamed TextStim: text = 'Exhale'
24.1894 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.1894 	EXP 	unnamed TextStim: text = 'Exhale'
24.2061 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.2061 	EXP 	unnamed TextStim: text = 'Exhale'
24.2231 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.2231 	EXP 	unnamed TextStim: text = 'Exhale'
24.2397 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.2397 	EXP 	unnamed TextStim: text = 'Exhale'
24.2560 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.2560 	EXP 	unnamed TextStim: text = 'Exhale'
24.2752 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.2752 	EXP 	unnamed TextStim: text = 'Exhale'
24.2893 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.2893 	EXP 	unnamed TextStim: text = 'Exhale'
24.3063 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.3063 	EXP 	unnamed TextStim: text = 'Exhale'
24.3230 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.3230 	EXP 	unnamed TextStim: text = 'Exhale'
24.3396 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.3396 	EXP 	unnamed TextStim: text = 'Exhale'
24.3561 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.3561 	EXP 	unnamed TextStim: text = 'Exhale'
24.3729 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.3729 	EXP 	unnamed TextStim: text = 'Exhale'
24.3890 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.3890 	EXP 	unnamed TextStim: text = 'Exhale'
24.4066 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.4066 	EXP 	unnamed TextStim: text = 'Exhale'
24.4237 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.4237 	EXP 	unnamed TextStim: text = 'Exhale'
24.4401 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.4401 	EXP 	unnamed TextStim: text = 'Exhale'
24.4573 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.4573 	EXP 	unnamed TextStim: text = 'Exhale'
24.4743 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.4743 	EXP 	unnamed TextStim: text = 'Exhale'
24.4903 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.4903 	EXP 	unnamed TextStim: text = 'Exhale'
24.5070 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.5070 	EXP 	unnamed TextStim: text = 'Exhale'
24.5237 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.5237 	EXP 	unnamed TextStim: text = 'Exhale'
24.5397 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.5397 	EXP 	unnamed TextStim: text = 'Exhale'
24.5566 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.5566 	EXP 	unnamed TextStim: text = 'Exhale'
24.5732 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.5732 	EXP 	unnamed TextStim: text = 'Exhale'
24.5899 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.5899 	EXP 	unnamed TextStim: text = 'Exhale'
24.6065 	EXP 	unnamed TextStim: height = 0.03
24.6065 	EXP 	unnamed TextStim: height = 0.03
24.6065 	EXP 	unnamed TextStim: height = 0.03
24.6065 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.6065 	EXP 	unnamed TextStim: text = 'Exhale'
24.6234 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.6234 	EXP 	unnamed TextStim: text = 'Exhale'
24.6397 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.6397 	EXP 	unnamed TextStim: text = 'Exhale'
24.6563 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.6563 	EXP 	unnamed TextStim: text = 'Exhale'
24.6724 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.6724 	EXP 	unnamed TextStim: text = 'Exhale'
24.6893 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.6893 	EXP 	unnamed TextStim: text = 'Exhale'
24.7058 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.7058 	EXP 	unnamed TextStim: text = 'Exhale'
24.7225 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.7225 	EXP 	unnamed TextStim: text = 'Exhale'
24.7389 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.7389 	EXP 	unnamed TextStim: text = 'Exhale'
24.7558 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.7558 	EXP 	unnamed TextStim: text = 'Exhale'
24.7726 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.7726 	EXP 	unnamed TextStim: text = 'Exhale'
24.7887 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.7887 	EXP 	unnamed TextStim: text = 'Exhale'
24.8058 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.8058 	EXP 	unnamed TextStim: text = 'Exhale'
24.8225 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.8225 	EXP 	unnamed TextStim: text = 'Exhale'
24.8388 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.8388 	EXP 	unnamed TextStim: text = 'Exhale'
24.8558 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.8558 	EXP 	unnamed TextStim: text = 'Exhale'
24.8734 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.8734 	EXP 	unnamed TextStim: text = 'Exhale'
24.8889 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.8889 	EXP 	unnamed TextStim: text = 'Exhale'
24.9058 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.9058 	EXP 	unnamed TextStim: text = 'Exhale'
24.9219 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.9219 	EXP 	unnamed TextStim: text = 'Exhale'
24.9385 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.9385 	EXP 	unnamed TextStim: text = 'Exhale'
24.9550 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.9550 	EXP 	unnamed TextStim: text = 'Exhale'
24.9721 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.9721 	EXP 	unnamed TextStim: text = 'Exhale'
24.9888 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.9888 	EXP 	unnamed TextStim: text = 'Exhale'
25.0053 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.0053 	EXP 	unnamed TextStim: text = 'Exhale'
25.0219 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.0219 	EXP 	unnamed TextStim: text = 'Exhale'
25.0387 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.0387 	EXP 	unnamed TextStim: text = 'Exhale'
25.0556 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.0556 	EXP 	unnamed TextStim: text = 'Exhale'
25.0719 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.0719 	EXP 	unnamed TextStim: text = 'Exhale'
25.0893 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.0893 	EXP 	unnamed TextStim: text = 'Exhale'
25.1056 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.1056 	EXP 	unnamed TextStim: text = 'Exhale'
25.1226 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.1226 	EXP 	unnamed TextStim: text = 'Exhale'
25.1387 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.1387 	EXP 	unnamed TextStim: text = 'Exhale'
25.1557 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.1557 	EXP 	unnamed TextStim: text = 'Exhale'
25.1722 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.1722 	EXP 	unnamed TextStim: text = 'Exhale'
25.1888 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.1888 	EXP 	unnamed TextStim: text = 'Exhale'
25.2052 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.2052 	EXP 	unnamed TextStim: text = 'Exhale'
25.2221 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.2221 	EXP 	unnamed TextStim: text = 'Exhale'
25.2387 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.2387 	EXP 	unnamed TextStim: text = 'Exhale'
25.2554 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.2554 	EXP 	unnamed TextStim: text = 'Exhale'
25.2723 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.2723 	EXP 	unnamed TextStim: text = 'Exhale'
25.2887 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.2887 	EXP 	unnamed TextStim: text = 'Exhale'
25.3053 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.3053 	EXP 	unnamed TextStim: text = 'Exhale'
25.3220 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.3220 	EXP 	unnamed TextStim: text = 'Exhale'
25.3387 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.3387 	EXP 	unnamed TextStim: text = 'Exhale'
25.3553 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.3553 	EXP 	unnamed TextStim: text = 'Exhale'
25.3723 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.3723 	EXP 	unnamed TextStim: text = 'Exhale'
25.3886 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.3886 	EXP 	unnamed TextStim: text = 'Exhale'
25.4053 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.4053 	EXP 	unnamed TextStim: text = 'Exhale'
25.4221 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.4221 	EXP 	unnamed TextStim: text = 'Exhale'
25.4385 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.4385 	EXP 	unnamed TextStim: text = 'Exhale'
25.4552 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.4552 	EXP 	unnamed TextStim: text = 'Exhale'
25.4726 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.4726 	EXP 	unnamed TextStim: text = 'Exhale'
25.4883 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.4883 	EXP 	unnamed TextStim: text = 'Exhale'
25.5053 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.5053 	EXP 	unnamed TextStim: text = 'Exhale'
25.5223 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.5223 	EXP 	unnamed TextStim: text = 'Exhale'
25.5387 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.5387 	EXP 	unnamed TextStim: text = 'Exhale'
25.5550 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.5550 	EXP 	unnamed TextStim: text = 'Exhale'
25.5719 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.5719 	EXP 	unnamed TextStim: text = 'Exhale'
25.5882 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.5882 	EXP 	unnamed TextStim: text = 'Exhale'
25.6049 	EXP 	unnamed TextStim: height = 0.03
25.6049 	EXP 	unnamed TextStim: height = 0.03
25.6049 	EXP 	unnamed TextStim: height = 0.03
25.6049 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.6049 	EXP 	unnamed TextStim: text = 'Exhale'
25.6219 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.6219 	EXP 	unnamed TextStim: text = 'Exhale'
25.6383 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.6383 	EXP 	unnamed TextStim: text = 'Exhale'
25.6550 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.6550 	EXP 	unnamed TextStim: text = 'Exhale'
25.6725 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.6725 	EXP 	unnamed TextStim: text = 'Exhale'
25.6883 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.6883 	EXP 	unnamed TextStim: text = 'Exhale'
25.7052 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.7052 	EXP 	unnamed TextStim: text = 'Exhale'
25.7220 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.7220 	EXP 	unnamed TextStim: text = 'Exhale'
25.7385 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.7385 	EXP 	unnamed TextStim: text = 'Exhale'
25.7551 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.7551 	EXP 	unnamed TextStim: text = 'Exhale'
25.7716 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.7716 	EXP 	unnamed TextStim: text = 'Exhale'
25.7881 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.7881 	EXP 	unnamed TextStim: text = 'Exhale'
25.8052 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.8052 	EXP 	unnamed TextStim: text = 'Exhale'
25.8212 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.8212 	EXP 	unnamed TextStim: text = 'Exhale'
25.8381 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.8381 	EXP 	unnamed TextStim: text = 'Exhale'
25.8548 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.8548 	EXP 	unnamed TextStim: text = 'Exhale'
25.8715 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.8715 	EXP 	unnamed TextStim: text = 'Exhale'
25.8880 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.8880 	EXP 	unnamed TextStim: text = 'Exhale'
25.9049 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.9049 	EXP 	unnamed TextStim: text = 'Exhale'
25.9225 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.9225 	EXP 	unnamed TextStim: text = 'Exhale'
25.9382 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.9382 	EXP 	unnamed TextStim: text = 'Exhale'
25.9548 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.9548 	EXP 	unnamed TextStim: text = 'Exhale'
25.9718 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.9718 	EXP 	unnamed TextStim: text = 'Exhale'
25.9882 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.9882 	EXP 	unnamed TextStim: text = 'Exhale'
26.0047 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.0047 	EXP 	unnamed TextStim: text = 'Exhale'
26.0214 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.0214 	EXP 	unnamed TextStim: text = 'Exhale'
26.0375 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.0375 	EXP 	unnamed TextStim: text = 'Exhale'
26.0545 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.0545 	EXP 	unnamed TextStim: text = 'Exhale'
26.0712 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.0712 	EXP 	unnamed TextStim: text = 'Exhale'
26.0878 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.0878 	EXP 	unnamed TextStim: text = 'Exhale'
26.1044 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.1044 	EXP 	unnamed TextStim: height = 0.05
26.1044 	EXP 	unnamed TextStim: height = 0.05
26.1044 	EXP 	unnamed TextStim: height = 0.05
26.1044 	EXP 	unnamed TextStim: text = 'Hold'
26.1216 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.1216 	EXP 	unnamed TextStim: text = 'Hold'
26.1379 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.1379 	EXP 	unnamed TextStim: text = 'Hold'
26.1543 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.1543 	EXP 	unnamed TextStim: text = 'Hold'
26.1713 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.1713 	EXP 	unnamed TextStim: text = 'Hold'
26.1878 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.1878 	EXP 	unnamed TextStim: text = 'Hold'
26.2046 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.2046 	EXP 	unnamed TextStim: text = 'Hold'
26.2218 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.2218 	EXP 	unnamed TextStim: text = 'Hold'
26.2379 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.2379 	EXP 	unnamed TextStim: text = 'Hold'
26.2546 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.2546 	EXP 	unnamed TextStim: text = 'Hold'
26.2710 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.2710 	EXP 	unnamed TextStim: text = 'Hold'
26.2877 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.2877 	EXP 	unnamed TextStim: text = 'Hold'
26.3051 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.3051 	EXP 	unnamed TextStim: text = 'Hold'
26.3213 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.3213 	EXP 	unnamed TextStim: text = 'Hold'
26.3377 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.3377 	EXP 	unnamed TextStim: text = 'Hold'
26.3543 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.3543 	EXP 	unnamed TextStim: text = 'Hold'
26.3714 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.3714 	EXP 	unnamed TextStim: text = 'Hold'
26.3876 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.3876 	EXP 	unnamed TextStim: text = 'Hold'
26.4042 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.4042 	EXP 	unnamed TextStim: text = 'Hold'
26.4207 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.4207 	EXP 	unnamed TextStim: text = 'Hold'
26.4377 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.4377 	EXP 	unnamed TextStim: text = 'Hold'
26.4544 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.4544 	EXP 	unnamed TextStim: text = 'Hold'
26.4731 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.4731 	EXP 	unnamed TextStim: text = 'Hold'
26.4883 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.4883 	EXP 	unnamed TextStim: text = 'Hold'
26.5044 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.5044 	EXP 	unnamed TextStim: text = 'Hold'
26.5208 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.5208 	EXP 	unnamed TextStim: text = 'Hold'
26.5377 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.5377 	EXP 	unnamed TextStim: text = 'Hold'
26.5542 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.5542 	EXP 	unnamed TextStim: text = 'Hold'
26.5707 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.5707 	EXP 	unnamed TextStim: text = 'Hold'
26.5877 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.5877 	EXP 	unnamed TextStim: text = 'Hold'
26.6041 	EXP 	unnamed TextStim: height = 0.03
26.6041 	EXP 	unnamed TextStim: height = 0.03
26.6041 	EXP 	unnamed TextStim: height = 0.03
26.6041 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.6041 	EXP 	unnamed TextStim: height = 0.05
26.6041 	EXP 	unnamed TextStim: height = 0.05
26.6041 	EXP 	unnamed TextStim: height = 0.05
26.6041 	EXP 	unnamed TextStim: text = 'Inhale'
26.6207 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.6207 	EXP 	unnamed TextStim: text = 'Inhale'
26.6377 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.6377 	EXP 	unnamed TextStim: text = 'Inhale'
26.6539 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.6539 	EXP 	unnamed TextStim: text = 'Inhale'
26.6711 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.6711 	EXP 	unnamed TextStim: text = 'Inhale'
26.6877 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.6877 	EXP 	unnamed TextStim: text = 'Inhale'
26.7046 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.7046 	EXP 	unnamed TextStim: text = 'Inhale'
26.7214 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.7214 	EXP 	unnamed TextStim: text = 'Inhale'
26.7379 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.7379 	EXP 	unnamed TextStim: text = 'Inhale'
26.7541 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.7541 	EXP 	unnamed TextStim: text = 'Inhale'
26.7711 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.7711 	EXP 	unnamed TextStim: text = 'Inhale'
26.7877 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.7877 	EXP 	unnamed TextStim: text = 'Inhale'
26.8041 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.8041 	EXP 	unnamed TextStim: text = 'Inhale'
26.8208 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.8208 	EXP 	unnamed TextStim: text = 'Inhale'
26.8376 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.8376 	EXP 	unnamed TextStim: text = 'Inhale'
26.8539 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.8539 	EXP 	unnamed TextStim: text = 'Inhale'
26.8705 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.8705 	EXP 	unnamed TextStim: text = 'Inhale'
26.8873 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.8873 	EXP 	unnamed TextStim: text = 'Inhale'
26.9039 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.9039 	EXP 	unnamed TextStim: text = 'Inhale'
26.9204 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.9204 	EXP 	unnamed TextStim: text = 'Inhale'
26.9370 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.9370 	EXP 	unnamed TextStim: text = 'Inhale'
26.9541 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.9541 	EXP 	unnamed TextStim: text = 'Inhale'
26.9705 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.9705 	EXP 	unnamed TextStim: text = 'Inhale'
26.9874 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.9874 	EXP 	unnamed TextStim: text = 'Inhale'
27.0041 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.0041 	EXP 	unnamed TextStim: text = 'Inhale'
27.0203 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.0203 	EXP 	unnamed TextStim: text = 'Inhale'
27.0370 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.0370 	EXP 	unnamed TextStim: text = 'Inhale'
27.0542 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.0542 	EXP 	unnamed TextStim: text = 'Inhale'
27.0707 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.0707 	EXP 	unnamed TextStim: text = 'Inhale'
27.0871 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.0871 	EXP 	unnamed TextStim: text = 'Inhale'
27.1038 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.1038 	EXP 	unnamed TextStim: text = 'Inhale'
27.1203 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.1203 	EXP 	unnamed TextStim: text = 'Inhale'
27.1368 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.1368 	EXP 	unnamed TextStim: text = 'Inhale'
27.1539 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.1539 	EXP 	unnamed TextStim: text = 'Inhale'
27.1705 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.1705 	EXP 	unnamed TextStim: text = 'Inhale'
27.1875 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.1875 	EXP 	unnamed TextStim: text = 'Inhale'
27.2038 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.2038 	EXP 	unnamed TextStim: text = 'Inhale'
27.2208 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.2208 	EXP 	unnamed TextStim: text = 'Inhale'
27.2375 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.2375 	EXP 	unnamed TextStim: text = 'Inhale'
27.2538 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.2538 	EXP 	unnamed TextStim: text = 'Inhale'
27.2706 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.2706 	EXP 	unnamed TextStim: text = 'Inhale'
27.2874 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.2874 	EXP 	unnamed TextStim: text = 'Inhale'
27.3036 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.3036 	EXP 	unnamed TextStim: text = 'Inhale'
27.3205 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.3205 	EXP 	unnamed TextStim: text = 'Inhale'
27.3370 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.3370 	EXP 	unnamed TextStim: text = 'Inhale'
27.3536 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.3536 	EXP 	unnamed TextStim: text = 'Inhale'
27.3701 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.3701 	EXP 	unnamed TextStim: text = 'Inhale'
27.3866 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.3866 	EXP 	unnamed TextStim: text = 'Inhale'
27.4035 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.4035 	EXP 	unnamed TextStim: text = 'Inhale'
27.4200 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.4200 	EXP 	unnamed TextStim: text = 'Inhale'
27.4368 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.4368 	EXP 	unnamed TextStim: text = 'Inhale'
27.4534 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.4534 	EXP 	unnamed TextStim: text = 'Inhale'
27.4702 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.4702 	EXP 	unnamed TextStim: text = 'Inhale'
27.4866 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.4866 	EXP 	unnamed TextStim: text = 'Inhale'
27.5032 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.5032 	EXP 	unnamed TextStim: text = 'Inhale'
27.5200 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.5200 	EXP 	unnamed TextStim: text = 'Inhale'
27.5374 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.5374 	EXP 	unnamed TextStim: text = 'Inhale'
27.5536 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.5536 	EXP 	unnamed TextStim: text = 'Inhale'
27.5704 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.5704 	EXP 	unnamed TextStim: text = 'Inhale'
27.5869 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.5869 	EXP 	unnamed TextStim: text = 'Inhale'
27.6033 	EXP 	unnamed TextStim: height = 0.03
27.6033 	EXP 	unnamed TextStim: height = 0.03
27.6033 	EXP 	unnamed TextStim: height = 0.03
27.6033 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.6033 	EXP 	unnamed TextStim: text = 'Inhale'
27.6202 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.6202 	EXP 	unnamed TextStim: text = 'Inhale'
27.6366 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.6366 	EXP 	unnamed TextStim: text = 'Inhale'
27.6533 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.6533 	EXP 	unnamed TextStim: text = 'Inhale'
27.6700 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.6700 	EXP 	unnamed TextStim: text = 'Inhale'
27.6867 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.6867 	EXP 	unnamed TextStim: text = 'Inhale'
27.7034 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.7034 	EXP 	unnamed TextStim: text = 'Inhale'
27.7199 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.7199 	EXP 	unnamed TextStim: text = 'Inhale'
27.7366 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.7366 	EXP 	unnamed TextStim: text = 'Inhale'
27.7529 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.7529 	EXP 	unnamed TextStim: text = 'Inhale'
27.7698 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.7698 	EXP 	unnamed TextStim: text = 'Inhale'
27.7868 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.7868 	EXP 	unnamed TextStim: text = 'Inhale'
27.8034 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.8034 	EXP 	unnamed TextStim: text = 'Inhale'
27.8202 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.8202 	EXP 	unnamed TextStim: text = 'Inhale'
27.8366 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.8366 	EXP 	unnamed TextStim: text = 'Inhale'
27.8534 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.8534 	EXP 	unnamed TextStim: text = 'Inhale'
27.8698 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.8698 	EXP 	unnamed TextStim: text = 'Inhale'
27.8865 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.8865 	EXP 	unnamed TextStim: text = 'Inhale'
27.9033 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.9033 	EXP 	unnamed TextStim: text = 'Inhale'
27.9206 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.9206 	EXP 	unnamed TextStim: text = 'Inhale'
27.9365 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.9365 	EXP 	unnamed TextStim: text = 'Inhale'
27.9526 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.9526 	EXP 	unnamed TextStim: text = 'Inhale'
27.9693 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.9693 	EXP 	unnamed TextStim: text = 'Inhale'
27.9861 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.9861 	EXP 	unnamed TextStim: text = 'Inhale'
28.0029 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.0029 	EXP 	unnamed TextStim: text = 'Inhale'
28.0194 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.0194 	EXP 	unnamed TextStim: text = 'Inhale'
28.0363 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.0363 	EXP 	unnamed TextStim: text = 'Inhale'
28.0533 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.0533 	EXP 	unnamed TextStim: text = 'Inhale'
28.0694 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.0694 	EXP 	unnamed TextStim: text = 'Inhale'
28.0867 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.0867 	EXP 	unnamed TextStim: text = 'Inhale'
28.1028 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.1028 	EXP 	unnamed TextStim: text = 'Inhale'
28.1211 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.1211 	EXP 	unnamed TextStim: text = 'Inhale'
28.1363 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.1363 	EXP 	unnamed TextStim: text = 'Inhale'
28.1528 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.1528 	EXP 	unnamed TextStim: text = 'Inhale'
28.1691 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.1691 	EXP 	unnamed TextStim: text = 'Inhale'
28.1857 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.1857 	EXP 	unnamed TextStim: text = 'Inhale'
28.2029 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.2029 	EXP 	unnamed TextStim: text = 'Inhale'
28.2203 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.2203 	EXP 	unnamed TextStim: text = 'Inhale'
28.2370 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.2370 	EXP 	unnamed TextStim: text = 'Inhale'
28.2538 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.2538 	EXP 	unnamed TextStim: text = 'Inhale'
28.2704 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.2704 	EXP 	unnamed TextStim: text = 'Inhale'
28.2869 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.2869 	EXP 	unnamed TextStim: text = 'Inhale'
28.3038 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.3038 	EXP 	unnamed TextStim: text = 'Inhale'
28.3203 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.3203 	EXP 	unnamed TextStim: text = 'Inhale'
28.3370 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.3370 	EXP 	unnamed TextStim: text = 'Inhale'
28.3524 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.3524 	EXP 	unnamed TextStim: text = 'Inhale'
28.3692 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.3692 	EXP 	unnamed TextStim: text = 'Inhale'
28.3861 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.3861 	EXP 	unnamed TextStim: text = 'Inhale'
28.4031 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.4031 	EXP 	unnamed TextStim: text = 'Inhale'
28.4194 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.4194 	EXP 	unnamed TextStim: text = 'Inhale'
28.4359 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.4359 	EXP 	unnamed TextStim: text = 'Inhale'
28.4528 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.4528 	EXP 	unnamed TextStim: text = 'Inhale'
28.4693 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.4693 	EXP 	unnamed TextStim: text = 'Inhale'
28.4860 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.4860 	EXP 	unnamed TextStim: text = 'Inhale'
28.5026 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.5026 	EXP 	unnamed TextStim: text = 'Inhale'
28.5191 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.5191 	EXP 	unnamed TextStim: text = 'Inhale'
28.5359 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.5359 	EXP 	unnamed TextStim: text = 'Inhale'
28.5527 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.5527 	EXP 	unnamed TextStim: text = 'Inhale'
28.5703 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.5703 	EXP 	unnamed TextStim: text = 'Inhale'
28.5863 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.5863 	EXP 	unnamed TextStim: text = 'Inhale'
28.6030 	EXP 	unnamed TextStim: height = 0.03
28.6030 	EXP 	unnamed TextStim: height = 0.03
28.6030 	EXP 	unnamed TextStim: height = 0.03
28.6030 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.6030 	EXP 	unnamed TextStim: text = 'Inhale'
28.6192 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.6192 	EXP 	unnamed TextStim: text = 'Inhale'
28.6357 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.6357 	EXP 	unnamed TextStim: text = 'Inhale'
28.6528 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.6528 	EXP 	unnamed TextStim: text = 'Inhale'
28.6705 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.6705 	EXP 	unnamed TextStim: text = 'Inhale'
28.6857 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.6857 	EXP 	unnamed TextStim: text = 'Inhale'
28.7025 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.7025 	EXP 	unnamed TextStim: text = 'Inhale'
28.7191 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.7191 	EXP 	unnamed TextStim: text = 'Inhale'
28.7359 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.7359 	EXP 	unnamed TextStim: text = 'Inhale'
28.7527 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.7527 	EXP 	unnamed TextStim: text = 'Inhale'
28.7692 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.7692 	EXP 	unnamed TextStim: text = 'Inhale'
28.7859 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.7859 	EXP 	unnamed TextStim: text = 'Inhale'
28.8027 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.8027 	EXP 	unnamed TextStim: text = 'Inhale'
28.8192 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.8192 	EXP 	unnamed TextStim: text = 'Inhale'
28.8357 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.8357 	EXP 	unnamed TextStim: text = 'Inhale'
28.8526 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.8526 	EXP 	unnamed TextStim: text = 'Inhale'
28.8692 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.8692 	EXP 	unnamed TextStim: text = 'Inhale'
28.8858 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.8858 	EXP 	unnamed TextStim: text = 'Inhale'
28.9023 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.9023 	EXP 	unnamed TextStim: text = 'Inhale'
28.9189 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.9189 	EXP 	unnamed TextStim: text = 'Inhale'
28.9353 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.9353 	EXP 	unnamed TextStim: text = 'Inhale'
28.9521 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.9521 	EXP 	unnamed TextStim: text = 'Inhale'
28.9690 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.9690 	EXP 	unnamed TextStim: text = 'Inhale'
28.9855 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.9855 	EXP 	unnamed TextStim: text = 'Inhale'
29.0021 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.0021 	EXP 	unnamed TextStim: text = 'Inhale'
29.0187 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.0187 	EXP 	unnamed TextStim: text = 'Inhale'
29.0356 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.0356 	EXP 	unnamed TextStim: text = 'Inhale'
29.0522 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.0522 	EXP 	unnamed TextStim: text = 'Inhale'
29.0688 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.0688 	EXP 	unnamed TextStim: text = 'Inhale'
29.0858 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.0858 	EXP 	unnamed TextStim: text = 'Inhale'
29.1021 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.1021 	EXP 	unnamed TextStim: text = 'Inhale'
29.1186 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.1186 	EXP 	unnamed TextStim: text = 'Inhale'
29.1352 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.1352 	EXP 	unnamed TextStim: text = 'Inhale'
29.1527 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.1527 	EXP 	unnamed TextStim: text = 'Inhale'
29.1686 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.1686 	EXP 	unnamed TextStim: text = 'Inhale'
29.1856 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.1856 	EXP 	unnamed TextStim: text = 'Inhale'
29.2025 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.2025 	EXP 	unnamed TextStim: text = 'Inhale'
29.2200 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.2200 	EXP 	unnamed TextStim: text = 'Inhale'
29.2353 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.2353 	EXP 	unnamed TextStim: text = 'Inhale'
29.2524 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.2524 	EXP 	unnamed TextStim: text = 'Inhale'
29.2682 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.2682 	EXP 	unnamed TextStim: text = 'Inhale'
29.2852 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.2852 	EXP 	unnamed TextStim: text = 'Inhale'
29.3021 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.3021 	EXP 	unnamed TextStim: text = 'Inhale'
29.3186 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.3186 	EXP 	unnamed TextStim: text = 'Inhale'
29.3353 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.3353 	EXP 	unnamed TextStim: text = 'Inhale'
29.3524 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.3524 	EXP 	unnamed TextStim: text = 'Inhale'
29.3685 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.3685 	EXP 	unnamed TextStim: text = 'Inhale'
29.3852 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.3852 	EXP 	unnamed TextStim: text = 'Inhale'
29.4026 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.4026 	EXP 	unnamed TextStim: text = 'Inhale'
29.4190 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.4190 	EXP 	unnamed TextStim: text = 'Inhale'
29.4351 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.4351 	EXP 	unnamed TextStim: text = 'Inhale'
29.4521 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.4521 	EXP 	unnamed TextStim: text = 'Inhale'
29.4686 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.4686 	EXP 	unnamed TextStim: text = 'Inhale'
29.4852 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.4852 	EXP 	unnamed TextStim: text = 'Inhale'
29.5021 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.5021 	EXP 	unnamed TextStim: text = 'Inhale'
29.5180 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.5180 	EXP 	unnamed TextStim: text = 'Inhale'
29.5350 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.5350 	EXP 	unnamed TextStim: text = 'Inhale'
29.5522 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.5522 	EXP 	unnamed TextStim: text = 'Inhale'
29.5690 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.5690 	EXP 	unnamed TextStim: text = 'Inhale'
29.5847 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.5847 	EXP 	unnamed TextStim: text = 'Inhale'
29.6018 	EXP 	unnamed TextStim: height = 0.03
29.6018 	EXP 	unnamed TextStim: height = 0.03
29.6018 	EXP 	unnamed TextStim: height = 0.03
29.6018 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.6018 	EXP 	unnamed TextStim: text = 'Inhale'
29.6186 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.6186 	EXP 	unnamed TextStim: text = 'Inhale'
29.6351 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.6351 	EXP 	unnamed TextStim: text = 'Inhale'
29.6519 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.6519 	EXP 	unnamed TextStim: text = 'Inhale'
29.6683 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.6683 	EXP 	unnamed TextStim: text = 'Inhale'
29.6850 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.6850 	EXP 	unnamed TextStim: text = 'Inhale'
29.7018 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.7018 	EXP 	unnamed TextStim: text = 'Inhale'
29.7182 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.7182 	EXP 	unnamed TextStim: text = 'Inhale'
29.7349 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.7349 	EXP 	unnamed TextStim: text = 'Inhale'
29.7520 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.7520 	EXP 	unnamed TextStim: text = 'Inhale'
29.7683 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.7683 	EXP 	unnamed TextStim: text = 'Inhale'
29.7852 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.7852 	EXP 	unnamed TextStim: text = 'Inhale'
29.8025 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.8025 	EXP 	unnamed TextStim: text = 'Inhale'
29.8182 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.8182 	EXP 	unnamed TextStim: text = 'Inhale'
29.8345 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.8345 	EXP 	unnamed TextStim: text = 'Inhale'
29.8521 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.8521 	EXP 	unnamed TextStim: text = 'Inhale'
29.8686 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.8686 	EXP 	unnamed TextStim: text = 'Inhale'
29.8852 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.8852 	EXP 	unnamed TextStim: text = 'Inhale'
29.9023 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.9023 	EXP 	unnamed TextStim: text = 'Inhale'
29.9203 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.9203 	EXP 	unnamed TextStim: text = 'Inhale'
29.9367 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.9367 	EXP 	unnamed TextStim: text = 'Inhale'
29.9528 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.9528 	EXP 	unnamed TextStim: text = 'Inhale'
29.9691 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.9691 	EXP 	unnamed TextStim: text = 'Inhale'
29.9854 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.9854 	EXP 	unnamed TextStim: text = 'Inhale'
30.0026 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.0026 	EXP 	unnamed TextStim: text = 'Inhale'
30.0189 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.0189 	EXP 	unnamed TextStim: text = 'Inhale'
30.0354 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.0354 	EXP 	unnamed TextStim: text = 'Inhale'
30.0529 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.0529 	EXP 	unnamed TextStim: text = 'Inhale'
30.0691 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.0691 	EXP 	unnamed TextStim: text = 'Inhale'
30.0858 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.0858 	EXP 	unnamed TextStim: text = 'Inhale'
30.1035 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.1035 	EXP 	unnamed TextStim: text = 'Inhale'
30.1189 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.1189 	EXP 	unnamed TextStim: text = 'Inhale'
30.1358 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.1358 	EXP 	unnamed TextStim: text = 'Inhale'
30.1523 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.1523 	EXP 	unnamed TextStim: text = 'Inhale'
30.1686 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.1686 	EXP 	unnamed TextStim: text = 'Inhale'
30.1852 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.1852 	EXP 	unnamed TextStim: text = 'Inhale'
30.2021 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.2021 	EXP 	unnamed TextStim: text = 'Inhale'
30.2188 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.2188 	EXP 	unnamed TextStim: text = 'Inhale'
30.2352 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.2352 	EXP 	unnamed TextStim: text = 'Inhale'
30.2521 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.2521 	EXP 	unnamed TextStim: text = 'Inhale'
30.2688 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.2688 	EXP 	unnamed TextStim: text = 'Inhale'
30.2854 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.2854 	EXP 	unnamed TextStim: text = 'Inhale'
30.3026 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.3026 	EXP 	unnamed TextStim: text = 'Inhale'
30.3189 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.3189 	EXP 	unnamed TextStim: text = 'Inhale'
30.3356 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.3356 	EXP 	unnamed TextStim: text = 'Inhale'
30.3529 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.3529 	EXP 	unnamed TextStim: text = 'Inhale'
30.3687 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.3687 	EXP 	unnamed TextStim: text = 'Inhale'
30.3851 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.3851 	EXP 	unnamed TextStim: text = 'Inhale'
30.4016 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.4016 	EXP 	unnamed TextStim: text = 'Inhale'
30.4182 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.4182 	EXP 	unnamed TextStim: text = 'Inhale'
30.4347 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.4347 	EXP 	unnamed TextStim: text = 'Inhale'
30.4521 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.4521 	EXP 	unnamed TextStim: text = 'Inhale'
30.4682 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.4682 	EXP 	unnamed TextStim: text = 'Inhale'
30.4847 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.4847 	EXP 	unnamed TextStim: text = 'Inhale'
30.5017 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.5017 	EXP 	unnamed TextStim: text = 'Inhale'
30.5182 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.5182 	EXP 	unnamed TextStim: text = 'Inhale'
30.5352 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.5352 	EXP 	unnamed TextStim: text = 'Inhale'
30.5516 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.5516 	EXP 	unnamed TextStim: text = 'Inhale'
30.5709 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.5709 	EXP 	unnamed TextStim: text = 'Inhale'
30.5856 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.5856 	EXP 	unnamed TextStim: text = 'Inhale'
30.6037 	EXP 	unnamed TextStim: height = 0.03
30.6037 	EXP 	unnamed TextStim: height = 0.03
30.6037 	EXP 	unnamed TextStim: height = 0.03
30.6037 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.6037 	EXP 	unnamed TextStim: text = 'Inhale'
30.6189 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.6189 	EXP 	unnamed TextStim: text = 'Inhale'
30.6349 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.6349 	EXP 	unnamed TextStim: text = 'Inhale'
30.6528 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.6528 	EXP 	unnamed TextStim: text = 'Inhale'
30.6684 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.6684 	EXP 	unnamed TextStim: text = 'Inhale'
30.6846 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.6846 	EXP 	unnamed TextStim: text = 'Inhale'
30.7018 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.7018 	EXP 	unnamed TextStim: text = 'Inhale'
30.7180 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.7180 	EXP 	unnamed TextStim: text = 'Inhale'
30.7341 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.7341 	EXP 	unnamed TextStim: text = 'Inhale'
30.7517 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.7517 	EXP 	unnamed TextStim: text = 'Inhale'
30.7681 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.7681 	EXP 	unnamed TextStim: text = 'Inhale'
30.7851 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.7851 	EXP 	unnamed TextStim: text = 'Inhale'
30.8021 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.8021 	EXP 	unnamed TextStim: text = 'Inhale'
30.8184 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.8184 	EXP 	unnamed TextStim: text = 'Inhale'
30.8352 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.8352 	EXP 	unnamed TextStim: text = 'Inhale'
30.8520 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.8520 	EXP 	unnamed TextStim: text = 'Inhale'
30.8679 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.8679 	EXP 	unnamed TextStim: text = 'Inhale'
30.8851 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.8851 	EXP 	unnamed TextStim: text = 'Inhale'
30.9027 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.9027 	EXP 	unnamed TextStim: text = 'Inhale'
30.9190 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.9190 	EXP 	unnamed TextStim: text = 'Inhale'
30.9346 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.9346 	EXP 	unnamed TextStim: text = 'Inhale'
30.9513 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.9513 	EXP 	unnamed TextStim: text = 'Inhale'
30.9682 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.9682 	EXP 	unnamed TextStim: text = 'Inhale'
30.9845 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.9845 	EXP 	unnamed TextStim: text = 'Inhale'
31.0015 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.0015 	EXP 	unnamed TextStim: text = 'Inhale'
31.0181 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.0181 	EXP 	unnamed TextStim: text = 'Inhale'
31.0357 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.0357 	EXP 	unnamed TextStim: text = 'Inhale'
31.0516 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.0516 	EXP 	unnamed TextStim: text = 'Inhale'
31.0681 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.0681 	EXP 	unnamed TextStim: text = 'Inhale'
31.0842 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.0842 	EXP 	unnamed TextStim: text = 'Inhale'
31.1019 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.1019 	EXP 	unnamed TextStim: height = 0.05
31.1019 	EXP 	unnamed TextStim: height = 0.05
31.1019 	EXP 	unnamed TextStim: height = 0.05
31.1019 	EXP 	unnamed TextStim: text = 'Hold'
31.1182 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.1182 	EXP 	unnamed TextStim: text = 'Hold'
31.1357 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.1357 	EXP 	unnamed TextStim: text = 'Hold'
31.1518 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.1518 	EXP 	unnamed TextStim: text = 'Hold'
31.1678 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.1678 	EXP 	unnamed TextStim: text = 'Hold'
31.1847 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.1847 	EXP 	unnamed TextStim: text = 'Hold'
31.2015 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.2015 	EXP 	unnamed TextStim: text = 'Hold'
31.2176 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.2176 	EXP 	unnamed TextStim: text = 'Hold'
31.2342 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.2342 	EXP 	unnamed TextStim: text = 'Hold'
31.2514 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.2514 	EXP 	unnamed TextStim: text = 'Hold'
31.2679 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.2679 	EXP 	unnamed TextStim: text = 'Hold'
31.2845 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.2845 	EXP 	unnamed TextStim: text = 'Hold'
31.3015 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.3015 	EXP 	unnamed TextStim: text = 'Hold'
31.3181 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.3181 	EXP 	unnamed TextStim: text = 'Hold'
31.3348 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.3348 	EXP 	unnamed TextStim: text = 'Hold'
31.3511 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.3511 	EXP 	unnamed TextStim: text = 'Hold'
31.3676 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.3676 	EXP 	unnamed TextStim: text = 'Hold'
31.3846 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.3846 	EXP 	unnamed TextStim: text = 'Hold'
31.4011 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.4011 	EXP 	unnamed TextStim: text = 'Hold'
31.4179 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.4179 	EXP 	unnamed TextStim: text = 'Hold'
31.4352 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.4352 	EXP 	unnamed TextStim: text = 'Hold'
31.4513 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.4513 	EXP 	unnamed TextStim: text = 'Hold'
31.4678 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.4678 	EXP 	unnamed TextStim: text = 'Hold'
31.4845 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.4845 	EXP 	unnamed TextStim: text = 'Hold'
31.5011 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.5011 	EXP 	unnamed TextStim: text = 'Hold'
31.5176 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.5176 	EXP 	unnamed TextStim: text = 'Hold'
31.5343 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.5343 	EXP 	unnamed TextStim: text = 'Hold'
31.5514 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.5514 	EXP 	unnamed TextStim: text = 'Hold'
31.5679 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.5679 	EXP 	unnamed TextStim: text = 'Hold'
31.5841 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.5841 	EXP 	unnamed TextStim: text = 'Hold'
33.6675 	EXP 	text_3: autoDraw = True
34.3831 	EXP 	window1: mouseVisible = True
34.3847 	EXP 	Created unnamed ShapeStim = ShapeStim(__class__=<class 'psychopy.visual.shape.ShapeStim'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=False, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0, 0, 0]), fillColorSpace='rgb', fillRGB=array([0, 0, 0]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=3, name='unnamed ShapeStim', opacity=0.35, ori=0.0, pos=array([0., 0.]), size=array([1., 1.]), units='height', vertices=ndarray(...), win=Window(...), windingRule=None)
34.3876 	EXP 	window1: mouseVisible = True
34.3927 	EXP 	Created unnamed Circle = Circle(__class__=<class 'psychopy.visual.circle.Circle'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=2, name='unnamed Circle', opacity=1.0, ori=0.0, pos=array([0., 0.]), size=array([1., 1.]), units='height', vertices=ndarray(...), win=Window(...))
34.3928 	EXP 	window1: mouseVisible = True
34.3951 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.05882353, 0.61568627, 0.84313725]), fillColorSpace='rgb', fillRGB=array([0.05882353, 0.61568627, 0.84313725]), interpolate=True, lineColor=array([0., 0., 0.]), lineColorSpace='rgb', lineRGB=array([0, 0, 0]), lineWidth=1.5, name='unnamed Rect', opacity=0.1, ori=0.0, pos=array([0., 0.]), size=array([1.77777778, 0.5       ]), units='height', vertices=ndarray(...), win=Window(...))
34.3955 	EXP 	window1: mouseVisible = True
34.3985 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([ 0.  , -0.35]), rgb=UNKNOWN, text='Inhale', units='height', win=Window(...), wrapWidth=1)
34.3986 	EXP 	window1: mouseVisible = True
34.3993 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([0. , 0.4]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
34.4028 	DATA 	Keypress: f1
34.4068 	EXP 	text_3: autoDraw = False
34.4068 	EXP 	unnamed TextStim: height = 0.03
34.4068 	EXP 	unnamed TextStim: height = 0.03
34.4068 	EXP 	unnamed TextStim: height = 0.03
34.4068 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.4068 	EXP 	unnamed TextStim: text = 'Inhale'
34.4155 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.4155 	EXP 	unnamed TextStim: text = 'Inhale'
34.4313 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.4313 	EXP 	unnamed TextStim: text = 'Inhale'
34.4478 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.4478 	EXP 	unnamed TextStim: text = 'Inhale'
34.4641 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.4641 	EXP 	unnamed TextStim: text = 'Inhale'
34.4807 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.4807 	EXP 	unnamed TextStim: text = 'Inhale'
34.4976 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.4976 	EXP 	unnamed TextStim: text = 'Inhale'
34.5146 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.5146 	EXP 	unnamed TextStim: text = 'Inhale'
34.5311 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.5311 	EXP 	unnamed TextStim: text = 'Inhale'
34.5476 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.5476 	EXP 	unnamed TextStim: text = 'Inhale'
34.5642 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.5642 	EXP 	unnamed TextStim: text = 'Inhale'
34.5809 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.5809 	EXP 	unnamed TextStim: text = 'Inhale'
34.5975 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.5975 	EXP 	unnamed TextStim: text = 'Inhale'
34.6139 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.6139 	EXP 	unnamed TextStim: text = 'Inhale'
34.6308 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.6308 	EXP 	unnamed TextStim: text = 'Inhale'
34.6475 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.6475 	EXP 	unnamed TextStim: text = 'Inhale'
34.6647 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.6647 	EXP 	unnamed TextStim: text = 'Inhale'
34.6809 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.6809 	EXP 	unnamed TextStim: text = 'Inhale'
34.6977 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.6977 	EXP 	unnamed TextStim: text = 'Inhale'
34.7143 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.7143 	EXP 	unnamed TextStim: text = 'Inhale'
34.7309 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.7309 	EXP 	unnamed TextStim: text = 'Inhale'
34.7474 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.7474 	EXP 	unnamed TextStim: text = 'Inhale'
34.7648 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.7648 	EXP 	unnamed TextStim: text = 'Inhale'
34.7809 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.7809 	EXP 	unnamed TextStim: text = 'Inhale'
34.7974 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.7974 	EXP 	unnamed TextStim: text = 'Inhale'
34.8141 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.8141 	EXP 	unnamed TextStim: text = 'Inhale'
34.8311 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.8311 	EXP 	unnamed TextStim: text = 'Inhale'
34.8476 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.8476 	EXP 	unnamed TextStim: text = 'Inhale'
34.8640 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.8640 	EXP 	unnamed TextStim: text = 'Inhale'
34.8807 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.8807 	EXP 	unnamed TextStim: text = 'Inhale'
34.8971 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.8971 	EXP 	unnamed TextStim: text = 'Inhale'
34.9139 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.9139 	EXP 	unnamed TextStim: text = 'Inhale'
34.9309 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.9309 	EXP 	unnamed TextStim: text = 'Inhale'
34.9473 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.9473 	EXP 	unnamed TextStim: text = 'Inhale'
34.9638 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.9638 	EXP 	unnamed TextStim: text = 'Inhale'
34.9806 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.9806 	EXP 	unnamed TextStim: text = 'Inhale'
34.9973 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
34.9973 	EXP 	unnamed TextStim: text = 'Inhale'
35.0140 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.0140 	EXP 	unnamed TextStim: text = 'Inhale'
35.0309 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.0309 	EXP 	unnamed TextStim: text = 'Inhale'
35.0472 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.0472 	EXP 	unnamed TextStim: text = 'Inhale'
35.0638 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.0638 	EXP 	unnamed TextStim: text = 'Inhale'
35.0806 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.0806 	EXP 	unnamed TextStim: text = 'Inhale'
35.0970 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.0970 	EXP 	unnamed TextStim: text = 'Inhale'
35.1140 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.1140 	EXP 	unnamed TextStim: text = 'Inhale'
35.1305 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.1305 	EXP 	unnamed TextStim: text = 'Inhale'
35.1471 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.1471 	EXP 	unnamed TextStim: text = 'Inhale'
35.1640 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.1640 	EXP 	unnamed TextStim: text = 'Inhale'
35.1807 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.1807 	EXP 	unnamed TextStim: text = 'Inhale'
35.1972 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.1972 	EXP 	unnamed TextStim: text = 'Inhale'
35.2138 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.2138 	EXP 	unnamed TextStim: text = 'Inhale'
35.2303 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.2303 	EXP 	unnamed TextStim: text = 'Inhale'
35.2470 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.2470 	EXP 	unnamed TextStim: text = 'Inhale'
35.2634 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.2634 	EXP 	unnamed TextStim: text = 'Inhale'
35.2805 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.2805 	EXP 	unnamed TextStim: text = 'Inhale'
35.2973 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.2973 	EXP 	unnamed TextStim: text = 'Inhale'
35.3138 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.3138 	EXP 	unnamed TextStim: text = 'Inhale'
35.3305 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.3305 	EXP 	unnamed TextStim: text = 'Inhale'
35.3473 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.3473 	EXP 	unnamed TextStim: text = 'Inhale'
35.3637 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.3637 	EXP 	unnamed TextStim: text = 'Inhale'
35.3803 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.3803 	EXP 	unnamed TextStim: text = 'Inhale'
35.3968 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.3968 	EXP 	unnamed TextStim: text = 'Inhale'
35.4135 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
35.4135 	EXP 	unnamed TextStim: text = 'Inhale'
35.4300 	EXP 	unnamed TextStim: height = 0.03
35.4300 	EXP 	unnamed TextStim: height = 0.03
35.4300 	EXP 	unnamed TextStim: height = 0.03
35.4300 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.4300 	EXP 	unnamed TextStim: text = 'Inhale'
35.4469 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.4469 	EXP 	unnamed TextStim: text = 'Inhale'
35.4635 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.4635 	EXP 	unnamed TextStim: text = 'Inhale'
35.4798 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.4798 	EXP 	unnamed TextStim: text = 'Inhale'
35.4967 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.4967 	EXP 	unnamed TextStim: text = 'Inhale'
35.5135 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.5135 	EXP 	unnamed TextStim: text = 'Inhale'
35.5300 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.5300 	EXP 	unnamed TextStim: text = 'Inhale'
35.5468 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.5468 	EXP 	unnamed TextStim: text = 'Inhale'
35.5631 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.5631 	EXP 	unnamed TextStim: text = 'Inhale'
35.5804 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.5804 	EXP 	unnamed TextStim: text = 'Inhale'
35.5968 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.5968 	EXP 	unnamed TextStim: text = 'Inhale'
35.6135 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.6135 	EXP 	unnamed TextStim: text = 'Inhale'
35.6301 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.6301 	EXP 	unnamed TextStim: text = 'Inhale'
35.6467 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.6467 	EXP 	unnamed TextStim: text = 'Inhale'
35.6635 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.6635 	EXP 	unnamed TextStim: text = 'Inhale'
35.6803 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.6803 	EXP 	unnamed TextStim: text = 'Inhale'
35.6970 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.6970 	EXP 	unnamed TextStim: text = 'Inhale'
35.7135 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.7135 	EXP 	unnamed TextStim: text = 'Inhale'
35.7299 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.7299 	EXP 	unnamed TextStim: text = 'Inhale'
35.7468 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.7468 	EXP 	unnamed TextStim: text = 'Inhale'
35.7632 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.7632 	EXP 	unnamed TextStim: text = 'Inhale'
35.7798 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.7798 	EXP 	unnamed TextStim: text = 'Inhale'
35.7965 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.7965 	EXP 	unnamed TextStim: text = 'Inhale'
35.8133 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.8133 	EXP 	unnamed TextStim: text = 'Inhale'
35.8297 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.8297 	EXP 	unnamed TextStim: text = 'Inhale'
35.8484 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.8484 	EXP 	unnamed TextStim: text = 'Inhale'
35.8635 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.8635 	EXP 	unnamed TextStim: text = 'Inhale'
35.8798 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.8798 	EXP 	unnamed TextStim: text = 'Inhale'
35.8972 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.8972 	EXP 	unnamed TextStim: text = 'Inhale'
35.9141 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.9141 	EXP 	unnamed TextStim: text = 'Inhale'
35.9301 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.9301 	EXP 	unnamed TextStim: text = 'Inhale'
35.9467 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.9467 	EXP 	unnamed TextStim: text = 'Inhale'
35.9654 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.9654 	EXP 	unnamed TextStim: text = 'Inhale'
35.9806 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.9806 	EXP 	unnamed TextStim: text = 'Inhale'
35.9968 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
35.9968 	EXP 	unnamed TextStim: text = 'Inhale'
36.0133 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.0133 	EXP 	unnamed TextStim: text = 'Inhale'
36.0293 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.0293 	EXP 	unnamed TextStim: text = 'Inhale'
36.0460 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.0460 	EXP 	unnamed TextStim: text = 'Inhale'
36.0628 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.0628 	EXP 	unnamed TextStim: text = 'Inhale'
36.0796 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.0796 	EXP 	unnamed TextStim: text = 'Inhale'
36.0966 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.0966 	EXP 	unnamed TextStim: text = 'Inhale'
36.1130 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.1130 	EXP 	unnamed TextStim: text = 'Inhale'
36.1298 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.1298 	EXP 	unnamed TextStim: text = 'Inhale'
36.1462 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.1462 	EXP 	unnamed TextStim: text = 'Inhale'
36.1631 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.1631 	EXP 	unnamed TextStim: text = 'Inhale'
36.1799 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.1799 	EXP 	unnamed TextStim: text = 'Inhale'
36.1968 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.1968 	EXP 	unnamed TextStim: text = 'Inhale'
36.2136 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.2136 	EXP 	unnamed TextStim: text = 'Inhale'
36.2299 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.2299 	EXP 	unnamed TextStim: text = 'Inhale'
36.2463 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.2463 	EXP 	unnamed TextStim: text = 'Inhale'
36.2629 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.2629 	EXP 	unnamed TextStim: text = 'Inhale'
36.2796 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.2796 	EXP 	unnamed TextStim: text = 'Inhale'
36.2962 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.2962 	EXP 	unnamed TextStim: text = 'Inhale'
36.3128 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.3128 	EXP 	unnamed TextStim: text = 'Inhale'
36.3295 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.3295 	EXP 	unnamed TextStim: text = 'Inhale'
36.3463 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.3463 	EXP 	unnamed TextStim: text = 'Inhale'
36.3630 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.3630 	EXP 	unnamed TextStim: text = 'Inhale'
36.3807 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.3807 	EXP 	unnamed TextStim: text = 'Inhale'
36.3962 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.3962 	EXP 	unnamed TextStim: text = 'Inhale'
36.4130 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
36.4130 	EXP 	unnamed TextStim: text = 'Inhale'
36.4296 	EXP 	unnamed TextStim: height = 0.03
36.4296 	EXP 	unnamed TextStim: height = 0.03
36.4296 	EXP 	unnamed TextStim: height = 0.03
36.4296 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.4296 	EXP 	unnamed TextStim: text = 'Inhale'
36.4461 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.4461 	EXP 	unnamed TextStim: text = 'Inhale'
36.4629 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.4629 	EXP 	unnamed TextStim: text = 'Inhale'
36.4792 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.4792 	EXP 	unnamed TextStim: text = 'Inhale'
36.4959 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.4959 	EXP 	unnamed TextStim: text = 'Inhale'
36.5130 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.5130 	EXP 	unnamed TextStim: text = 'Inhale'
36.5296 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.5296 	EXP 	unnamed TextStim: text = 'Inhale'
36.5459 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.5459 	EXP 	unnamed TextStim: text = 'Inhale'
36.5629 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.5629 	EXP 	unnamed TextStim: text = 'Inhale'
36.5794 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.5794 	EXP 	unnamed TextStim: text = 'Inhale'
36.5962 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.5962 	EXP 	unnamed TextStim: text = 'Inhale'
36.6128 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.6128 	EXP 	unnamed TextStim: text = 'Inhale'
36.6296 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.6296 	EXP 	unnamed TextStim: text = 'Inhale'
36.6462 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.6462 	EXP 	unnamed TextStim: text = 'Inhale'
36.6625 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.6625 	EXP 	unnamed TextStim: text = 'Inhale'
36.6793 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.6793 	EXP 	unnamed TextStim: text = 'Inhale'
36.6961 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.6961 	EXP 	unnamed TextStim: text = 'Inhale'
36.7130 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.7130 	EXP 	unnamed TextStim: text = 'Inhale'
36.7291 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.7291 	EXP 	unnamed TextStim: text = 'Inhale'
36.7461 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.7461 	EXP 	unnamed TextStim: text = 'Inhale'
36.7627 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.7627 	EXP 	unnamed TextStim: text = 'Inhale'
36.7792 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.7792 	EXP 	unnamed TextStim: text = 'Inhale'
36.7958 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.7958 	EXP 	unnamed TextStim: text = 'Inhale'
36.8130 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.8130 	EXP 	unnamed TextStim: text = 'Inhale'
36.8296 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.8296 	EXP 	unnamed TextStim: text = 'Inhale'
36.8462 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.8462 	EXP 	unnamed TextStim: text = 'Inhale'
36.8632 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.8632 	EXP 	unnamed TextStim: text = 'Inhale'
36.8796 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.8796 	EXP 	unnamed TextStim: text = 'Inhale'
36.8956 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.8956 	EXP 	unnamed TextStim: text = 'Inhale'
36.9128 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.9128 	EXP 	unnamed TextStim: text = 'Inhale'
36.9294 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.9294 	EXP 	unnamed TextStim: text = 'Inhale'
36.9477 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.9477 	EXP 	unnamed TextStim: text = 'Inhale'
36.9620 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.9620 	EXP 	unnamed TextStim: text = 'Inhale'
36.9796 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.9796 	EXP 	unnamed TextStim: text = 'Inhale'
36.9957 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
36.9957 	EXP 	unnamed TextStim: text = 'Inhale'
37.0123 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.0123 	EXP 	unnamed TextStim: text = 'Inhale'
37.0290 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.0290 	EXP 	unnamed TextStim: text = 'Inhale'
37.0452 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.0452 	EXP 	unnamed TextStim: text = 'Inhale'
37.0625 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.0625 	EXP 	unnamed TextStim: text = 'Inhale'
37.0788 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.0788 	EXP 	unnamed TextStim: text = 'Inhale'
37.0958 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.0958 	EXP 	unnamed TextStim: text = 'Inhale'
37.1126 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.1126 	EXP 	unnamed TextStim: text = 'Inhale'
37.1292 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.1292 	EXP 	unnamed TextStim: text = 'Inhale'
37.1458 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.1458 	EXP 	unnamed TextStim: text = 'Inhale'
37.1629 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.1629 	EXP 	unnamed TextStim: text = 'Inhale'
37.1798 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.1798 	EXP 	unnamed TextStim: text = 'Inhale'
37.1955 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.1955 	EXP 	unnamed TextStim: text = 'Inhale'
37.2120 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.2120 	EXP 	unnamed TextStim: text = 'Inhale'
37.2287 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.2287 	EXP 	unnamed TextStim: text = 'Inhale'
37.2455 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.2455 	EXP 	unnamed TextStim: text = 'Inhale'
37.2625 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.2625 	EXP 	unnamed TextStim: text = 'Inhale'
37.2789 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.2789 	EXP 	unnamed TextStim: text = 'Inhale'
37.2952 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.2952 	EXP 	unnamed TextStim: text = 'Inhale'
37.3122 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.3122 	EXP 	unnamed TextStim: text = 'Inhale'
37.3291 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.3291 	EXP 	unnamed TextStim: text = 'Inhale'
37.3457 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.3457 	EXP 	unnamed TextStim: text = 'Inhale'
37.3627 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.3627 	EXP 	unnamed TextStim: text = 'Inhale'
37.3784 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.3784 	EXP 	unnamed TextStim: text = 'Inhale'
37.3958 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.3958 	EXP 	unnamed TextStim: text = 'Inhale'
37.4126 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
37.4126 	EXP 	unnamed TextStim: text = 'Inhale'
37.4290 	EXP 	unnamed TextStim: height = 0.03
37.4290 	EXP 	unnamed TextStim: height = 0.03
37.4290 	EXP 	unnamed TextStim: height = 0.03
37.4290 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.4290 	EXP 	unnamed TextStim: text = 'Inhale'
37.4454 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.4454 	EXP 	unnamed TextStim: text = 'Inhale'
37.4623 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.4623 	EXP 	unnamed TextStim: text = 'Inhale'
37.4793 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.4793 	EXP 	unnamed TextStim: text = 'Inhale'
37.4953 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.4953 	EXP 	unnamed TextStim: text = 'Inhale'
37.5127 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.5127 	EXP 	unnamed TextStim: text = 'Inhale'
37.5289 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.5289 	EXP 	unnamed TextStim: text = 'Inhale'
37.5453 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.5453 	EXP 	unnamed TextStim: text = 'Inhale'
37.5622 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.5622 	EXP 	unnamed TextStim: text = 'Inhale'
37.5786 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.5786 	EXP 	unnamed TextStim: text = 'Inhale'
37.5952 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.5952 	EXP 	unnamed TextStim: text = 'Inhale'
37.6121 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.6121 	EXP 	unnamed TextStim: text = 'Inhale'
37.6288 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.6288 	EXP 	unnamed TextStim: text = 'Inhale'
37.6454 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.6454 	EXP 	unnamed TextStim: text = 'Inhale'
37.6623 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.6623 	EXP 	unnamed TextStim: text = 'Inhale'
37.6789 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.6789 	EXP 	unnamed TextStim: text = 'Inhale'
37.6951 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.6951 	EXP 	unnamed TextStim: text = 'Inhale'
37.7119 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.7119 	EXP 	unnamed TextStim: text = 'Inhale'
37.7285 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.7285 	EXP 	unnamed TextStim: text = 'Inhale'
37.7449 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.7449 	EXP 	unnamed TextStim: text = 'Inhale'
37.7621 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.7621 	EXP 	unnamed TextStim: text = 'Inhale'
37.7788 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.7788 	EXP 	unnamed TextStim: text = 'Inhale'
37.7954 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.7954 	EXP 	unnamed TextStim: text = 'Inhale'
37.8123 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.8123 	EXP 	unnamed TextStim: text = 'Inhale'
37.8284 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.8284 	EXP 	unnamed TextStim: text = 'Inhale'
37.8446 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.8446 	EXP 	unnamed TextStim: text = 'Inhale'
37.8623 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.8623 	EXP 	unnamed TextStim: text = 'Inhale'
37.8783 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.8783 	EXP 	unnamed TextStim: text = 'Inhale'
37.8951 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.8951 	EXP 	unnamed TextStim: text = 'Inhale'
37.9123 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.9123 	EXP 	unnamed TextStim: text = 'Inhale'
37.9283 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.9283 	EXP 	unnamed TextStim: text = 'Inhale'
37.9455 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.9455 	EXP 	unnamed TextStim: text = 'Inhale'
37.9621 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.9621 	EXP 	unnamed TextStim: text = 'Inhale'
37.9790 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.9790 	EXP 	unnamed TextStim: text = 'Inhale'
37.9957 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
37.9957 	EXP 	unnamed TextStim: text = 'Inhale'
38.0117 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.0117 	EXP 	unnamed TextStim: text = 'Inhale'
38.0302 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.0302 	EXP 	unnamed TextStim: text = 'Inhale'
38.0451 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.0451 	EXP 	unnamed TextStim: text = 'Inhale'
38.0615 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.0615 	EXP 	unnamed TextStim: text = 'Inhale'
38.0781 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.0781 	EXP 	unnamed TextStim: text = 'Inhale'
38.0946 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.0946 	EXP 	unnamed TextStim: text = 'Inhale'
38.1115 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.1115 	EXP 	unnamed TextStim: text = 'Inhale'
38.1286 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.1286 	EXP 	unnamed TextStim: text = 'Inhale'
38.1450 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.1450 	EXP 	unnamed TextStim: text = 'Inhale'
38.1615 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.1615 	EXP 	unnamed TextStim: text = 'Inhale'
38.1777 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.1777 	EXP 	unnamed TextStim: text = 'Inhale'
38.1948 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.1948 	EXP 	unnamed TextStim: text = 'Inhale'
38.2117 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.2117 	EXP 	unnamed TextStim: text = 'Inhale'
38.2282 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.2282 	EXP 	unnamed TextStim: text = 'Inhale'
38.2447 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.2447 	EXP 	unnamed TextStim: text = 'Inhale'
38.2618 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.2618 	EXP 	unnamed TextStim: text = 'Inhale'
38.2782 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.2782 	EXP 	unnamed TextStim: text = 'Inhale'
38.2945 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.2945 	EXP 	unnamed TextStim: text = 'Inhale'
38.3112 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.3112 	EXP 	unnamed TextStim: text = 'Inhale'
38.3275 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.3275 	EXP 	unnamed TextStim: text = 'Inhale'
38.3442 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.3442 	EXP 	unnamed TextStim: text = 'Inhale'
38.3617 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.3617 	EXP 	unnamed TextStim: text = 'Inhale'
38.3781 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.3781 	EXP 	unnamed TextStim: text = 'Inhale'
38.3944 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.3944 	EXP 	unnamed TextStim: text = 'Inhale'
38.4116 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
38.4116 	EXP 	unnamed TextStim: text = 'Inhale'
38.4279 	EXP 	unnamed TextStim: height = 0.03
38.4279 	EXP 	unnamed TextStim: height = 0.03
38.4279 	EXP 	unnamed TextStim: height = 0.03
38.4279 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.4279 	EXP 	unnamed TextStim: text = 'Inhale'
38.4446 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.4446 	EXP 	unnamed TextStim: text = 'Inhale'
38.4614 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.4614 	EXP 	unnamed TextStim: text = 'Inhale'
38.4783 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.4783 	EXP 	unnamed TextStim: text = 'Inhale'
38.4944 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.4944 	EXP 	unnamed TextStim: text = 'Inhale'
38.5107 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.5107 	EXP 	unnamed TextStim: text = 'Inhale'
38.5277 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.5277 	EXP 	unnamed TextStim: text = 'Inhale'
38.5443 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.5443 	EXP 	unnamed TextStim: text = 'Inhale'
38.5613 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.5613 	EXP 	unnamed TextStim: text = 'Inhale'
38.5774 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.5774 	EXP 	unnamed TextStim: text = 'Inhale'
38.5944 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.5944 	EXP 	unnamed TextStim: text = 'Inhale'
38.6111 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.6111 	EXP 	unnamed TextStim: text = 'Inhale'
38.6278 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.6278 	EXP 	unnamed TextStim: text = 'Inhale'
38.6447 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.6447 	EXP 	unnamed TextStim: text = 'Inhale'
38.6614 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.6614 	EXP 	unnamed TextStim: text = 'Inhale'
38.6776 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.6776 	EXP 	unnamed TextStim: text = 'Inhale'
38.6948 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.6948 	EXP 	unnamed TextStim: text = 'Inhale'
38.7115 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.7115 	EXP 	unnamed TextStim: text = 'Inhale'
38.7279 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.7279 	EXP 	unnamed TextStim: text = 'Inhale'
38.7442 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.7442 	EXP 	unnamed TextStim: text = 'Inhale'
38.7609 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.7609 	EXP 	unnamed TextStim: text = 'Inhale'
38.7774 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.7774 	EXP 	unnamed TextStim: text = 'Inhale'
38.7939 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.7939 	EXP 	unnamed TextStim: text = 'Inhale'
38.8110 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.8110 	EXP 	unnamed TextStim: text = 'Inhale'
38.8276 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.8276 	EXP 	unnamed TextStim: text = 'Inhale'
38.8441 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.8441 	EXP 	unnamed TextStim: text = 'Inhale'
38.8607 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.8607 	EXP 	unnamed TextStim: text = 'Inhale'
38.8773 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.8773 	EXP 	unnamed TextStim: text = 'Inhale'
38.8942 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.8942 	EXP 	unnamed TextStim: text = 'Inhale'
38.9110 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.9110 	EXP 	unnamed TextStim: text = 'Inhale'
38.9276 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.9276 	EXP 	unnamed TextStim: height = 0.05
38.9276 	EXP 	unnamed TextStim: height = 0.05
38.9276 	EXP 	unnamed TextStim: height = 0.05
38.9276 	EXP 	unnamed TextStim: text = 'Hold'
38.9441 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.9441 	EXP 	unnamed TextStim: text = 'Hold'
38.9610 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.9610 	EXP 	unnamed TextStim: text = 'Hold'
38.9773 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.9773 	EXP 	unnamed TextStim: text = 'Hold'
38.9941 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
38.9941 	EXP 	unnamed TextStim: text = 'Hold'
39.0106 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.0106 	EXP 	unnamed TextStim: text = 'Hold'
39.0272 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.0272 	EXP 	unnamed TextStim: text = 'Hold'
39.0440 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.0440 	EXP 	unnamed TextStim: text = 'Hold'
39.0609 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.0609 	EXP 	unnamed TextStim: text = 'Hold'
39.0771 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.0771 	EXP 	unnamed TextStim: text = 'Hold'
39.0938 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.0938 	EXP 	unnamed TextStim: text = 'Hold'
39.1106 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.1106 	EXP 	unnamed TextStim: text = 'Hold'
39.1275 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.1275 	EXP 	unnamed TextStim: text = 'Hold'
39.1441 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.1441 	EXP 	unnamed TextStim: text = 'Hold'
39.1608 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.1608 	EXP 	unnamed TextStim: text = 'Hold'
39.1774 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.1774 	EXP 	unnamed TextStim: text = 'Hold'
39.1942 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.1942 	EXP 	unnamed TextStim: text = 'Hold'
39.2110 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.2110 	EXP 	unnamed TextStim: text = 'Hold'
39.2274 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.2274 	EXP 	unnamed TextStim: text = 'Hold'
39.2440 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.2440 	EXP 	unnamed TextStim: text = 'Hold'
39.2607 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.2607 	EXP 	unnamed TextStim: text = 'Hold'
39.2772 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.2772 	EXP 	unnamed TextStim: text = 'Hold'
39.2938 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.2938 	EXP 	unnamed TextStim: text = 'Hold'
39.3109 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.3109 	EXP 	unnamed TextStim: text = 'Hold'
39.3274 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.3274 	EXP 	unnamed TextStim: text = 'Hold'
39.3440 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.3440 	EXP 	unnamed TextStim: text = 'Hold'
39.3605 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.3605 	EXP 	unnamed TextStim: text = 'Hold'
39.3771 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.3771 	EXP 	unnamed TextStim: text = 'Hold'
39.3937 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.3937 	EXP 	unnamed TextStim: text = 'Hold'
39.4109 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.4109 	EXP 	unnamed TextStim: text = 'Hold'
39.4273 	EXP 	unnamed TextStim: height = 0.03
39.4273 	EXP 	unnamed TextStim: height = 0.03
39.4273 	EXP 	unnamed TextStim: height = 0.03
39.4273 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.4273 	EXP 	unnamed TextStim: height = 0.05
39.4273 	EXP 	unnamed TextStim: height = 0.05
39.4273 	EXP 	unnamed TextStim: height = 0.05
39.4273 	EXP 	unnamed TextStim: text = 'Exhale'
39.4436 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.4436 	EXP 	unnamed TextStim: text = 'Exhale'
39.4604 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.4604 	EXP 	unnamed TextStim: text = 'Exhale'
39.4772 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.4772 	EXP 	unnamed TextStim: text = 'Exhale'
39.4937 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.4937 	EXP 	unnamed TextStim: text = 'Exhale'
39.5101 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.5101 	EXP 	unnamed TextStim: text = 'Exhale'
39.5267 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.5267 	EXP 	unnamed TextStim: text = 'Exhale'
39.5433 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.5433 	EXP 	unnamed TextStim: text = 'Exhale'
39.5601 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.5601 	EXP 	unnamed TextStim: text = 'Exhale'
39.5769 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.5769 	EXP 	unnamed TextStim: text = 'Exhale'
39.5933 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.5933 	EXP 	unnamed TextStim: text = 'Exhale'
39.6106 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.6106 	EXP 	unnamed TextStim: text = 'Exhale'
39.6271 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.6271 	EXP 	unnamed TextStim: text = 'Exhale'
39.6436 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.6436 	EXP 	unnamed TextStim: text = 'Exhale'
39.6603 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.6603 	EXP 	unnamed TextStim: text = 'Exhale'
39.6779 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.6779 	EXP 	unnamed TextStim: text = 'Exhale'
39.6935 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.6935 	EXP 	unnamed TextStim: text = 'Exhale'
39.7100 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.7100 	EXP 	unnamed TextStim: text = 'Exhale'
39.7268 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.7268 	EXP 	unnamed TextStim: text = 'Exhale'
39.7434 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.7434 	EXP 	unnamed TextStim: text = 'Exhale'
39.7605 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.7605 	EXP 	unnamed TextStim: text = 'Exhale'
39.7767 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.7767 	EXP 	unnamed TextStim: text = 'Exhale'
39.7933 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.7933 	EXP 	unnamed TextStim: text = 'Exhale'
39.8102 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.8102 	EXP 	unnamed TextStim: text = 'Exhale'
39.8264 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.8264 	EXP 	unnamed TextStim: text = 'Exhale'
39.8435 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.8435 	EXP 	unnamed TextStim: text = 'Exhale'
39.8599 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.8599 	EXP 	unnamed TextStim: text = 'Exhale'
39.8770 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.8770 	EXP 	unnamed TextStim: text = 'Exhale'
39.8935 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.8935 	EXP 	unnamed TextStim: text = 'Exhale'
39.9101 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.9101 	EXP 	unnamed TextStim: text = 'Exhale'
39.9270 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.9270 	EXP 	unnamed TextStim: text = 'Exhale'
39.9431 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.9431 	EXP 	unnamed TextStim: text = 'Exhale'
39.9598 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.9598 	EXP 	unnamed TextStim: text = 'Exhale'
39.9766 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.9766 	EXP 	unnamed TextStim: text = 'Exhale'
39.9934 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
39.9934 	EXP 	unnamed TextStim: text = 'Exhale'
40.0099 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.0099 	EXP 	unnamed TextStim: text = 'Exhale'
40.0270 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.0270 	EXP 	unnamed TextStim: text = 'Exhale'
40.0453 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.0453 	EXP 	unnamed TextStim: text = 'Exhale'
40.0596 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.0596 	EXP 	unnamed TextStim: text = 'Exhale'
40.0770 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.0770 	EXP 	unnamed TextStim: text = 'Exhale'
40.0931 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.0931 	EXP 	unnamed TextStim: text = 'Exhale'
40.1099 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.1099 	EXP 	unnamed TextStim: text = 'Exhale'
40.1263 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.1263 	EXP 	unnamed TextStim: text = 'Exhale'
40.1431 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.1431 	EXP 	unnamed TextStim: text = 'Exhale'
40.1595 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.1595 	EXP 	unnamed TextStim: text = 'Exhale'
40.1763 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.1763 	EXP 	unnamed TextStim: text = 'Exhale'
40.1933 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.1933 	EXP 	unnamed TextStim: text = 'Exhale'
40.2100 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.2100 	EXP 	unnamed TextStim: text = 'Exhale'
40.2285 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.2285 	EXP 	unnamed TextStim: text = 'Exhale'
40.2431 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.2431 	EXP 	unnamed TextStim: text = 'Exhale'
40.2596 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.2596 	EXP 	unnamed TextStim: text = 'Exhale'
40.2770 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.2770 	EXP 	unnamed TextStim: text = 'Exhale'
40.2939 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.2939 	EXP 	unnamed TextStim: text = 'Exhale'
40.3107 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.3107 	EXP 	unnamed TextStim: text = 'Exhale'
40.3276 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.3276 	EXP 	unnamed TextStim: text = 'Exhale'
40.3443 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.3443 	EXP 	unnamed TextStim: text = 'Exhale'
40.3604 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.3604 	EXP 	unnamed TextStim: text = 'Exhale'
40.3767 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.3767 	EXP 	unnamed TextStim: text = 'Exhale'
40.3938 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.3938 	EXP 	unnamed TextStim: text = 'Exhale'
40.4104 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.4104 	EXP 	unnamed TextStim: text = 'Exhale'
40.4269 	EXP 	unnamed TextStim: height = 0.03
40.4269 	EXP 	unnamed TextStim: height = 0.03
40.4269 	EXP 	unnamed TextStim: height = 0.03
40.4269 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.4269 	EXP 	unnamed TextStim: text = 'Exhale'
40.4441 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.4441 	EXP 	unnamed TextStim: text = 'Exhale'
40.4607 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.4607 	EXP 	unnamed TextStim: text = 'Exhale'
40.4770 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.4770 	EXP 	unnamed TextStim: text = 'Exhale'
40.4944 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.4944 	EXP 	unnamed TextStim: text = 'Exhale'
40.5104 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.5104 	EXP 	unnamed TextStim: text = 'Exhale'
40.5270 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.5270 	EXP 	unnamed TextStim: text = 'Exhale'
40.5444 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.5444 	EXP 	unnamed TextStim: text = 'Exhale'
40.5607 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.5607 	EXP 	unnamed TextStim: text = 'Exhale'
40.5775 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.5775 	EXP 	unnamed TextStim: text = 'Exhale'
40.5940 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.5940 	EXP 	unnamed TextStim: text = 'Exhale'
40.6105 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.6105 	EXP 	unnamed TextStim: text = 'Exhale'
40.6266 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.6266 	EXP 	unnamed TextStim: text = 'Exhale'
40.6435 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.6435 	EXP 	unnamed TextStim: text = 'Exhale'
40.6602 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.6602 	EXP 	unnamed TextStim: text = 'Exhale'
40.6763 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.6763 	EXP 	unnamed TextStim: text = 'Exhale'
40.6940 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.6940 	EXP 	unnamed TextStim: text = 'Exhale'
40.7103 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.7103 	EXP 	unnamed TextStim: text = 'Exhale'
40.7267 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.7267 	EXP 	unnamed TextStim: text = 'Exhale'
40.7442 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.7442 	EXP 	unnamed TextStim: text = 'Exhale'
40.7599 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.7599 	EXP 	unnamed TextStim: text = 'Exhale'
40.7788 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.7788 	EXP 	unnamed TextStim: text = 'Exhale'
40.7947 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.7947 	EXP 	unnamed TextStim: text = 'Exhale'
40.8105 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.8105 	EXP 	unnamed TextStim: text = 'Exhale'
40.8272 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.8272 	EXP 	unnamed TextStim: text = 'Exhale'
40.8444 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.8444 	EXP 	unnamed TextStim: text = 'Exhale'
40.8602 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.8602 	EXP 	unnamed TextStim: text = 'Exhale'
40.8767 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.8767 	EXP 	unnamed TextStim: text = 'Exhale'
40.8937 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.8937 	EXP 	unnamed TextStim: text = 'Exhale'
40.9101 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.9101 	EXP 	unnamed TextStim: text = 'Exhale'
40.9265 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.9265 	EXP 	unnamed TextStim: text = 'Exhale'
40.9444 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.9444 	EXP 	unnamed TextStim: text = 'Exhale'
40.9600 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.9600 	EXP 	unnamed TextStim: text = 'Exhale'
40.9767 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.9767 	EXP 	unnamed TextStim: text = 'Exhale'
40.9938 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
40.9938 	EXP 	unnamed TextStim: text = 'Exhale'
41.0100 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.0100 	EXP 	unnamed TextStim: text = 'Exhale'
41.0267 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.0267 	EXP 	unnamed TextStim: text = 'Exhale'
41.0443 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.0443 	EXP 	unnamed TextStim: text = 'Exhale'
41.0599 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.0599 	EXP 	unnamed TextStim: text = 'Exhale'
41.0770 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.0770 	EXP 	unnamed TextStim: text = 'Exhale'
41.0941 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.0941 	EXP 	unnamed TextStim: text = 'Exhale'
41.1111 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.1111 	EXP 	unnamed TextStim: text = 'Exhale'
41.1266 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.1266 	EXP 	unnamed TextStim: text = 'Exhale'
41.1437 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.1437 	EXP 	unnamed TextStim: text = 'Exhale'
41.1598 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.1598 	EXP 	unnamed TextStim: text = 'Exhale'
41.1763 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.1763 	EXP 	unnamed TextStim: text = 'Exhale'
41.1932 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.1932 	EXP 	unnamed TextStim: text = 'Exhale'
41.2098 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.2098 	EXP 	unnamed TextStim: text = 'Exhale'
41.2264 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.2264 	EXP 	unnamed TextStim: text = 'Exhale'
41.2437 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.2437 	EXP 	unnamed TextStim: text = 'Exhale'
41.2600 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.2600 	EXP 	unnamed TextStim: text = 'Exhale'
41.2764 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.2764 	EXP 	unnamed TextStim: text = 'Exhale'
41.2934 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.2934 	EXP 	unnamed TextStim: text = 'Exhale'
41.3097 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.3097 	EXP 	unnamed TextStim: text = 'Exhale'
41.3263 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.3263 	EXP 	unnamed TextStim: text = 'Exhale'
41.3445 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.3445 	EXP 	unnamed TextStim: text = 'Exhale'
41.3601 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.3601 	EXP 	unnamed TextStim: text = 'Exhale'
41.3766 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.3766 	EXP 	unnamed TextStim: text = 'Exhale'
41.3931 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.3931 	EXP 	unnamed TextStim: text = 'Exhale'
41.4095 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.4095 	EXP 	unnamed TextStim: text = 'Exhale'
41.4259 	EXP 	unnamed TextStim: height = 0.03
41.4259 	EXP 	unnamed TextStim: height = 0.03
41.4259 	EXP 	unnamed TextStim: height = 0.03
41.4259 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.4259 	EXP 	unnamed TextStim: text = 'Exhale'
41.4428 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.4428 	EXP 	unnamed TextStim: text = 'Exhale'
41.4596 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.4596 	EXP 	unnamed TextStim: text = 'Exhale'
41.4762 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.4762 	EXP 	unnamed TextStim: text = 'Exhale'
41.4929 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.4929 	EXP 	unnamed TextStim: text = 'Exhale'
41.5095 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.5095 	EXP 	unnamed TextStim: text = 'Exhale'
41.5261 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.5261 	EXP 	unnamed TextStim: text = 'Exhale'
41.5433 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.5433 	EXP 	unnamed TextStim: text = 'Exhale'
41.5594 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.5594 	EXP 	unnamed TextStim: text = 'Exhale'
41.5764 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.5764 	EXP 	unnamed TextStim: text = 'Exhale'
41.5918 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.5918 	EXP 	unnamed TextStim: text = 'Exhale'
41.6086 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.6086 	EXP 	unnamed TextStim: text = 'Exhale'
41.6250 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.6250 	EXP 	unnamed TextStim: text = 'Exhale'
41.6421 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.6421 	EXP 	unnamed TextStim: text = 'Exhale'
41.6582 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.6582 	EXP 	unnamed TextStim: text = 'Exhale'
41.6750 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.6750 	EXP 	unnamed TextStim: text = 'Exhale'
41.6922 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.6922 	EXP 	unnamed TextStim: text = 'Exhale'
41.7086 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.7086 	EXP 	unnamed TextStim: text = 'Exhale'
41.7251 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.7251 	EXP 	unnamed TextStim: text = 'Exhale'
41.7421 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.7421 	EXP 	unnamed TextStim: text = 'Exhale'
41.7589 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.7589 	EXP 	unnamed TextStim: text = 'Exhale'
41.7749 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.7749 	EXP 	unnamed TextStim: text = 'Exhale'
41.7917 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.7917 	EXP 	unnamed TextStim: text = 'Exhale'
41.8084 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.8084 	EXP 	unnamed TextStim: text = 'Exhale'
41.8255 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.8255 	EXP 	unnamed TextStim: text = 'Exhale'
41.8424 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.8424 	EXP 	unnamed TextStim: text = 'Exhale'
41.8603 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.8603 	EXP 	unnamed TextStim: text = 'Exhale'
41.8750 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.8750 	EXP 	unnamed TextStim: text = 'Exhale'
41.8921 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.8921 	EXP 	unnamed TextStim: text = 'Exhale'
41.9087 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.9087 	EXP 	unnamed TextStim: text = 'Exhale'
41.9250 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.9250 	EXP 	unnamed TextStim: text = 'Exhale'
41.9424 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.9424 	EXP 	unnamed TextStim: text = 'Exhale'
41.9583 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.9583 	EXP 	unnamed TextStim: text = 'Exhale'
41.9745 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.9745 	EXP 	unnamed TextStim: text = 'Exhale'
41.9920 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
41.9920 	EXP 	unnamed TextStim: text = 'Exhale'
42.0081 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.0081 	EXP 	unnamed TextStim: text = 'Exhale'
42.0247 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.0247 	EXP 	unnamed TextStim: text = 'Exhale'
42.0420 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.0420 	EXP 	unnamed TextStim: text = 'Exhale'
42.0578 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.0578 	EXP 	unnamed TextStim: text = 'Exhale'
42.0746 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.0746 	EXP 	unnamed TextStim: text = 'Exhale'
42.0919 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.0919 	EXP 	unnamed TextStim: text = 'Exhale'
42.1087 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.1087 	EXP 	unnamed TextStim: text = 'Exhale'
42.1256 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.1256 	EXP 	unnamed TextStim: text = 'Exhale'
42.1417 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.1417 	EXP 	unnamed TextStim: text = 'Exhale'
42.1584 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.1584 	EXP 	unnamed TextStim: text = 'Exhale'
42.1747 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.1747 	EXP 	unnamed TextStim: text = 'Exhale'
42.1919 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.1919 	EXP 	unnamed TextStim: text = 'Exhale'
42.2087 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.2087 	EXP 	unnamed TextStim: text = 'Exhale'
42.2248 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.2248 	EXP 	unnamed TextStim: text = 'Exhale'
42.2416 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.2416 	EXP 	unnamed TextStim: text = 'Exhale'
42.2578 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.2578 	EXP 	unnamed TextStim: text = 'Exhale'
42.2748 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.2748 	EXP 	unnamed TextStim: text = 'Exhale'
42.2916 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.2916 	EXP 	unnamed TextStim: text = 'Exhale'
42.3081 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.3081 	EXP 	unnamed TextStim: text = 'Exhale'
42.3250 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.3250 	EXP 	unnamed TextStim: text = 'Exhale'
42.3418 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.3418 	EXP 	unnamed TextStim: text = 'Exhale'
42.3580 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.3580 	EXP 	unnamed TextStim: text = 'Exhale'
42.3748 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.3748 	EXP 	unnamed TextStim: text = 'Exhale'
42.3910 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.3910 	EXP 	unnamed TextStim: text = 'Exhale'
42.4080 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.4080 	EXP 	unnamed TextStim: text = 'Exhale'
42.4248 	EXP 	unnamed TextStim: height = 0.03
42.4248 	EXP 	unnamed TextStim: height = 0.03
42.4248 	EXP 	unnamed TextStim: height = 0.03
42.4248 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.4248 	EXP 	unnamed TextStim: text = 'Exhale'
42.4409 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.4409 	EXP 	unnamed TextStim: text = 'Exhale'
42.4582 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.4582 	EXP 	unnamed TextStim: text = 'Exhale'
42.4746 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.4746 	EXP 	unnamed TextStim: text = 'Exhale'
42.4916 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.4916 	EXP 	unnamed TextStim: text = 'Exhale'
42.5078 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.5078 	EXP 	unnamed TextStim: text = 'Exhale'
42.5242 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.5242 	EXP 	unnamed TextStim: text = 'Exhale'
42.5414 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.5414 	EXP 	unnamed TextStim: text = 'Exhale'
42.5579 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.5579 	EXP 	unnamed TextStim: text = 'Exhale'
42.5749 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.5749 	EXP 	unnamed TextStim: text = 'Exhale'
42.5911 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.5911 	EXP 	unnamed TextStim: text = 'Exhale'
42.6077 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.6077 	EXP 	unnamed TextStim: text = 'Exhale'
42.6245 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.6245 	EXP 	unnamed TextStim: text = 'Exhale'
42.6414 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.6414 	EXP 	unnamed TextStim: text = 'Exhale'
42.6577 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.6577 	EXP 	unnamed TextStim: text = 'Exhale'
42.6745 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.6745 	EXP 	unnamed TextStim: text = 'Exhale'
42.6911 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.6911 	EXP 	unnamed TextStim: text = 'Exhale'
42.7077 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.7077 	EXP 	unnamed TextStim: text = 'Exhale'
42.7246 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.7246 	EXP 	unnamed TextStim: text = 'Exhale'
42.7415 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.7415 	EXP 	unnamed TextStim: text = 'Exhale'
42.7578 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.7578 	EXP 	unnamed TextStim: text = 'Exhale'
42.7742 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.7742 	EXP 	unnamed TextStim: text = 'Exhale'
42.7912 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.7912 	EXP 	unnamed TextStim: text = 'Exhale'
42.8077 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.8077 	EXP 	unnamed TextStim: text = 'Exhale'
42.8244 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.8244 	EXP 	unnamed TextStim: text = 'Exhale'
42.8410 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.8410 	EXP 	unnamed TextStim: text = 'Exhale'
42.8578 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.8578 	EXP 	unnamed TextStim: text = 'Exhale'
42.8743 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.8743 	EXP 	unnamed TextStim: text = 'Exhale'
42.8910 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.8910 	EXP 	unnamed TextStim: text = 'Exhale'
42.9077 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.9077 	EXP 	unnamed TextStim: text = 'Exhale'
42.9240 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.9240 	EXP 	unnamed TextStim: text = 'Exhale'
42.9425 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.9425 	EXP 	unnamed TextStim: text = 'Exhale'
42.9574 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.9574 	EXP 	unnamed TextStim: text = 'Exhale'
42.9737 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.9737 	EXP 	unnamed TextStim: text = 'Exhale'
42.9911 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
42.9911 	EXP 	unnamed TextStim: text = 'Exhale'
43.0076 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.0076 	EXP 	unnamed TextStim: text = 'Exhale'
43.0241 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.0241 	EXP 	unnamed TextStim: text = 'Exhale'
43.0407 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.0407 	EXP 	unnamed TextStim: text = 'Exhale'
43.0568 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.0568 	EXP 	unnamed TextStim: text = 'Exhale'
43.0737 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.0737 	EXP 	unnamed TextStim: text = 'Exhale'
43.0907 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.0907 	EXP 	unnamed TextStim: text = 'Exhale'
43.1077 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.1077 	EXP 	unnamed TextStim: text = 'Exhale'
43.1237 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.1237 	EXP 	unnamed TextStim: text = 'Exhale'
43.1412 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.1412 	EXP 	unnamed TextStim: text = 'Exhale'
43.1575 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.1575 	EXP 	unnamed TextStim: text = 'Exhale'
43.1739 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.1739 	EXP 	unnamed TextStim: text = 'Exhale'
43.1906 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.1906 	EXP 	unnamed TextStim: text = 'Exhale'
43.2077 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.2077 	EXP 	unnamed TextStim: text = 'Exhale'
43.2239 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.2239 	EXP 	unnamed TextStim: text = 'Exhale'
43.2406 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.2406 	EXP 	unnamed TextStim: text = 'Exhale'
43.2576 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.2576 	EXP 	unnamed TextStim: text = 'Exhale'
43.2738 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.2738 	EXP 	unnamed TextStim: text = 'Exhale'
43.2904 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.2904 	EXP 	unnamed TextStim: text = 'Exhale'
43.3077 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.3077 	EXP 	unnamed TextStim: text = 'Exhale'
43.3239 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.3239 	EXP 	unnamed TextStim: text = 'Exhale'
43.3405 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.3405 	EXP 	unnamed TextStim: text = 'Exhale'
43.3566 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.3566 	EXP 	unnamed TextStim: text = 'Exhale'
43.3737 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.3737 	EXP 	unnamed TextStim: text = 'Exhale'
43.3903 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.3903 	EXP 	unnamed TextStim: text = 'Exhale'
43.4069 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.4069 	EXP 	unnamed TextStim: text = 'Exhale'
43.4236 	EXP 	unnamed TextStim: height = 0.03
43.4236 	EXP 	unnamed TextStim: height = 0.03
43.4236 	EXP 	unnamed TextStim: height = 0.03
43.4236 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.4236 	EXP 	unnamed TextStim: text = 'Exhale'
43.4405 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.4405 	EXP 	unnamed TextStim: text = 'Exhale'
43.4568 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.4568 	EXP 	unnamed TextStim: text = 'Exhale'
43.4750 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.4750 	EXP 	unnamed TextStim: text = 'Exhale'
43.4904 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.4904 	EXP 	unnamed TextStim: text = 'Exhale'
43.5070 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.5070 	EXP 	unnamed TextStim: text = 'Exhale'
43.5232 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.5232 	EXP 	unnamed TextStim: text = 'Exhale'
43.5406 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.5406 	EXP 	unnamed TextStim: text = 'Exhale'
43.5577 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.5577 	EXP 	unnamed TextStim: text = 'Exhale'
43.5737 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.5737 	EXP 	unnamed TextStim: text = 'Exhale'
43.5902 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.5902 	EXP 	unnamed TextStim: text = 'Exhale'
43.6068 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.6068 	EXP 	unnamed TextStim: text = 'Exhale'
43.6232 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.6232 	EXP 	unnamed TextStim: text = 'Exhale'
43.6403 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.6403 	EXP 	unnamed TextStim: text = 'Exhale'
43.6568 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.6568 	EXP 	unnamed TextStim: text = 'Exhale'
43.6732 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.6732 	EXP 	unnamed TextStim: text = 'Exhale'
43.6900 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.6900 	EXP 	unnamed TextStim: text = 'Exhale'
43.7067 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.7067 	EXP 	unnamed TextStim: text = 'Exhale'
43.7234 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.7234 	EXP 	unnamed TextStim: text = 'Exhale'
43.7402 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.7402 	EXP 	unnamed TextStim: text = 'Exhale'
43.7568 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.7568 	EXP 	unnamed TextStim: text = 'Exhale'
43.7733 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.7733 	EXP 	unnamed TextStim: text = 'Exhale'
43.7902 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.7902 	EXP 	unnamed TextStim: text = 'Exhale'
43.8067 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.8067 	EXP 	unnamed TextStim: text = 'Exhale'
43.8230 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.8230 	EXP 	unnamed TextStim: text = 'Exhale'
43.8403 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.8403 	EXP 	unnamed TextStim: text = 'Exhale'
43.8568 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.8568 	EXP 	unnamed TextStim: text = 'Exhale'
43.8736 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.8736 	EXP 	unnamed TextStim: text = 'Exhale'
43.8898 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.8898 	EXP 	unnamed TextStim: text = 'Exhale'
43.9066 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.9066 	EXP 	unnamed TextStim: text = 'Exhale'
43.9230 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.9230 	EXP 	unnamed TextStim: height = 0.05
43.9230 	EXP 	unnamed TextStim: height = 0.05
43.9230 	EXP 	unnamed TextStim: height = 0.05
43.9230 	EXP 	unnamed TextStim: text = 'Hold'
43.9400 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.9400 	EXP 	unnamed TextStim: text = 'Hold'
43.9566 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.9566 	EXP 	unnamed TextStim: text = 'Hold'
43.9733 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.9733 	EXP 	unnamed TextStim: text = 'Hold'
43.9896 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
43.9896 	EXP 	unnamed TextStim: text = 'Hold'
44.0064 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.0064 	EXP 	unnamed TextStim: text = 'Hold'
44.0245 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.0245 	EXP 	unnamed TextStim: text = 'Hold'
44.0396 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.0396 	EXP 	unnamed TextStim: text = 'Hold'
44.0565 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.0565 	EXP 	unnamed TextStim: text = 'Hold'
44.0731 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.0731 	EXP 	unnamed TextStim: text = 'Hold'
44.0901 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.0901 	EXP 	unnamed TextStim: text = 'Hold'
44.1063 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.1063 	EXP 	unnamed TextStim: text = 'Hold'
44.1232 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.1232 	EXP 	unnamed TextStim: text = 'Hold'
44.1399 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.1399 	EXP 	unnamed TextStim: text = 'Hold'
44.1563 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.1563 	EXP 	unnamed TextStim: text = 'Hold'
44.1731 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.1731 	EXP 	unnamed TextStim: text = 'Hold'
44.1898 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.1898 	EXP 	unnamed TextStim: text = 'Hold'
44.2062 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.2062 	EXP 	unnamed TextStim: text = 'Hold'
44.2227 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.2227 	EXP 	unnamed TextStim: text = 'Hold'
44.2395 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.2395 	EXP 	unnamed TextStim: text = 'Hold'
44.2565 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.2565 	EXP 	unnamed TextStim: text = 'Hold'
44.2728 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.2728 	EXP 	unnamed TextStim: text = 'Hold'
44.2897 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.2897 	EXP 	unnamed TextStim: text = 'Hold'
44.3059 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.3059 	EXP 	unnamed TextStim: text = 'Hold'
44.3229 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.3229 	EXP 	unnamed TextStim: text = 'Hold'
44.3395 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.3395 	EXP 	unnamed TextStim: text = 'Hold'
44.3562 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.3562 	EXP 	unnamed TextStim: text = 'Hold'
44.3730 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.3730 	EXP 	unnamed TextStim: text = 'Hold'
44.3895 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.3895 	EXP 	unnamed TextStim: text = 'Hold'
44.4060 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
44.4060 	EXP 	unnamed TextStim: text = 'Hold'
44.4234 	EXP 	unnamed TextStim: height = 0.03
44.4234 	EXP 	unnamed TextStim: height = 0.03
44.4234 	EXP 	unnamed TextStim: height = 0.03
44.4234 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.4234 	EXP 	unnamed TextStim: height = 0.05
44.4234 	EXP 	unnamed TextStim: height = 0.05
44.4234 	EXP 	unnamed TextStim: height = 0.05
44.4234 	EXP 	unnamed TextStim: text = 'Inhale'
44.4396 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.4396 	EXP 	unnamed TextStim: text = 'Inhale'
44.4562 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.4562 	EXP 	unnamed TextStim: text = 'Inhale'
44.4735 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.4735 	EXP 	unnamed TextStim: text = 'Inhale'
44.4899 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.4899 	EXP 	unnamed TextStim: text = 'Inhale'
44.5068 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.5068 	EXP 	unnamed TextStim: text = 'Inhale'
44.5240 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.5240 	EXP 	unnamed TextStim: text = 'Inhale'
44.5406 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.5406 	EXP 	unnamed TextStim: text = 'Inhale'
44.5564 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.5564 	EXP 	unnamed TextStim: text = 'Inhale'
44.5733 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.5733 	EXP 	unnamed TextStim: text = 'Inhale'
44.5891 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.5891 	EXP 	unnamed TextStim: text = 'Inhale'
44.6061 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.6061 	EXP 	unnamed TextStim: text = 'Inhale'
44.6229 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.6229 	EXP 	unnamed TextStim: text = 'Inhale'
44.6394 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.6394 	EXP 	unnamed TextStim: text = 'Inhale'
44.6555 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.6555 	EXP 	unnamed TextStim: text = 'Inhale'
44.6727 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.6727 	EXP 	unnamed TextStim: text = 'Inhale'
44.6891 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.6891 	EXP 	unnamed TextStim: text = 'Inhale'
44.7063 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.7063 	EXP 	unnamed TextStim: text = 'Inhale'
44.7229 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.7229 	EXP 	unnamed TextStim: text = 'Inhale'
44.7394 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.7394 	EXP 	unnamed TextStim: text = 'Inhale'
44.7559 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.7559 	EXP 	unnamed TextStim: text = 'Inhale'
44.7730 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.7730 	EXP 	unnamed TextStim: text = 'Inhale'
44.7893 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.7893 	EXP 	unnamed TextStim: text = 'Inhale'
44.8057 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.8057 	EXP 	unnamed TextStim: text = 'Inhale'
44.8223 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.8223 	EXP 	unnamed TextStim: text = 'Inhale'
44.8390 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.8390 	EXP 	unnamed TextStim: text = 'Inhale'
44.8563 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.8563 	EXP 	unnamed TextStim: text = 'Inhale'
44.8733 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.8733 	EXP 	unnamed TextStim: text = 'Inhale'
44.8897 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.8897 	EXP 	unnamed TextStim: text = 'Inhale'
44.9059 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.9059 	EXP 	unnamed TextStim: text = 'Inhale'
44.9227 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.9227 	EXP 	unnamed TextStim: text = 'Inhale'
44.9389 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.9389 	EXP 	unnamed TextStim: text = 'Inhale'
44.9556 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.9556 	EXP 	unnamed TextStim: text = 'Inhale'
44.9734 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.9734 	EXP 	unnamed TextStim: text = 'Inhale'
44.9890 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
44.9890 	EXP 	unnamed TextStim: text = 'Inhale'
45.0058 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.0058 	EXP 	unnamed TextStim: text = 'Inhale'
45.0230 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.0230 	EXP 	unnamed TextStim: text = 'Inhale'
45.0389 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.0389 	EXP 	unnamed TextStim: text = 'Inhale'
45.0559 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.0559 	EXP 	unnamed TextStim: text = 'Inhale'
45.0724 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.0724 	EXP 	unnamed TextStim: text = 'Inhale'
45.0888 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.0888 	EXP 	unnamed TextStim: text = 'Inhale'
45.1056 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.1056 	EXP 	unnamed TextStim: text = 'Inhale'
45.1224 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.1224 	EXP 	unnamed TextStim: text = 'Inhale'
45.1392 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.1392 	EXP 	unnamed TextStim: text = 'Inhale'
45.1558 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.1558 	EXP 	unnamed TextStim: text = 'Inhale'
45.1726 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.1726 	EXP 	unnamed TextStim: text = 'Inhale'
45.1895 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.1895 	EXP 	unnamed TextStim: text = 'Inhale'
45.2058 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.2058 	EXP 	unnamed TextStim: text = 'Inhale'
45.2235 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.2235 	EXP 	unnamed TextStim: text = 'Inhale'
45.2390 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.2390 	EXP 	unnamed TextStim: text = 'Inhale'
45.2554 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.2554 	EXP 	unnamed TextStim: text = 'Inhale'
45.2728 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.2728 	EXP 	unnamed TextStim: text = 'Inhale'
45.2891 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.2891 	EXP 	unnamed TextStim: text = 'Inhale'
45.3055 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.3055 	EXP 	unnamed TextStim: text = 'Inhale'
45.3222 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.3222 	EXP 	unnamed TextStim: text = 'Inhale'
45.3386 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.3386 	EXP 	unnamed TextStim: text = 'Inhale'
45.3555 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.3555 	EXP 	unnamed TextStim: text = 'Inhale'
45.3721 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.3721 	EXP 	unnamed TextStim: text = 'Inhale'
45.3892 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.3892 	EXP 	unnamed TextStim: text = 'Inhale'
45.4059 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
45.4059 	EXP 	unnamed TextStim: text = 'Inhale'
45.4234 	EXP 	unnamed TextStim: height = 0.03
45.4234 	EXP 	unnamed TextStim: height = 0.03
45.4234 	EXP 	unnamed TextStim: height = 0.03
45.4234 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.4234 	EXP 	unnamed TextStim: text = 'Inhale'
45.4389 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.4389 	EXP 	unnamed TextStim: text = 'Inhale'
45.4555 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.4555 	EXP 	unnamed TextStim: text = 'Inhale'
45.4722 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.4722 	EXP 	unnamed TextStim: text = 'Inhale'
45.4886 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.4886 	EXP 	unnamed TextStim: text = 'Inhale'
45.5052 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.5052 	EXP 	unnamed TextStim: text = 'Inhale'
45.5223 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.5223 	EXP 	unnamed TextStim: text = 'Inhale'
45.5385 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.5385 	EXP 	unnamed TextStim: text = 'Inhale'
45.5549 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.5549 	EXP 	unnamed TextStim: text = 'Inhale'
45.5720 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.5720 	EXP 	unnamed TextStim: text = 'Inhale'
45.5887 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.5887 	EXP 	unnamed TextStim: text = 'Inhale'
45.6052 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.6052 	EXP 	unnamed TextStim: text = 'Inhale'
45.6220 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.6220 	EXP 	unnamed TextStim: text = 'Inhale'
45.6385 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.6385 	EXP 	unnamed TextStim: text = 'Inhale'
45.6550 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.6550 	EXP 	unnamed TextStim: text = 'Inhale'
45.6723 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.6723 	EXP 	unnamed TextStim: text = 'Inhale'
45.6886 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.6886 	EXP 	unnamed TextStim: text = 'Inhale'
45.7066 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.7066 	EXP 	unnamed TextStim: text = 'Inhale'
45.7224 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.7224 	EXP 	unnamed TextStim: text = 'Inhale'
45.7390 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.7390 	EXP 	unnamed TextStim: text = 'Inhale'
45.7550 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.7550 	EXP 	unnamed TextStim: text = 'Inhale'
45.7719 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.7719 	EXP 	unnamed TextStim: text = 'Inhale'
45.7883 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.7883 	EXP 	unnamed TextStim: text = 'Inhale'
45.8051 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.8051 	EXP 	unnamed TextStim: text = 'Inhale'
45.8222 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.8222 	EXP 	unnamed TextStim: text = 'Inhale'
45.8385 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.8385 	EXP 	unnamed TextStim: text = 'Inhale'
45.8552 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.8552 	EXP 	unnamed TextStim: text = 'Inhale'
45.8721 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.8721 	EXP 	unnamed TextStim: text = 'Inhale'
45.8886 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.8886 	EXP 	unnamed TextStim: text = 'Inhale'
45.9052 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.9052 	EXP 	unnamed TextStim: text = 'Inhale'
45.9219 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.9219 	EXP 	unnamed TextStim: text = 'Inhale'
45.9381 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.9381 	EXP 	unnamed TextStim: text = 'Inhale'
45.9551 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.9551 	EXP 	unnamed TextStim: text = 'Inhale'
45.9724 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.9724 	EXP 	unnamed TextStim: text = 'Inhale'
45.9888 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
45.9888 	EXP 	unnamed TextStim: text = 'Inhale'
46.0051 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.0051 	EXP 	unnamed TextStim: text = 'Inhale'
46.0215 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.0215 	EXP 	unnamed TextStim: text = 'Inhale'
46.0383 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.0383 	EXP 	unnamed TextStim: text = 'Inhale'
46.0546 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.0546 	EXP 	unnamed TextStim: text = 'Inhale'
46.0716 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.0716 	EXP 	unnamed TextStim: text = 'Inhale'
46.0889 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.0889 	EXP 	unnamed TextStim: text = 'Inhale'
46.1053 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.1053 	EXP 	unnamed TextStim: text = 'Inhale'
46.1220 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.1220 	EXP 	unnamed TextStim: text = 'Inhale'
46.1385 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.1385 	EXP 	unnamed TextStim: text = 'Inhale'
46.1549 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.1549 	EXP 	unnamed TextStim: text = 'Inhale'
46.1714 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.1714 	EXP 	unnamed TextStim: text = 'Inhale'
46.1888 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.1888 	EXP 	unnamed TextStim: text = 'Inhale'
46.2050 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.2050 	EXP 	unnamed TextStim: text = 'Inhale'
46.2225 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.2225 	EXP 	unnamed TextStim: text = 'Inhale'
46.2381 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.2381 	EXP 	unnamed TextStim: text = 'Inhale'
46.2567 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.2567 	EXP 	unnamed TextStim: text = 'Inhale'
46.2721 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.2721 	EXP 	unnamed TextStim: text = 'Inhale'
46.2882 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.2882 	EXP 	unnamed TextStim: text = 'Inhale'
46.3047 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.3047 	EXP 	unnamed TextStim: text = 'Inhale'
46.3215 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.3215 	EXP 	unnamed TextStim: text = 'Inhale'
46.3380 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.3380 	EXP 	unnamed TextStim: text = 'Inhale'
46.3547 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.3547 	EXP 	unnamed TextStim: text = 'Inhale'
46.3714 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.3714 	EXP 	unnamed TextStim: text = 'Inhale'
46.3878 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.3878 	EXP 	unnamed TextStim: text = 'Inhale'
46.4047 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
46.4047 	EXP 	unnamed TextStim: text = 'Inhale'
46.4217 	EXP 	unnamed TextStim: height = 0.03
46.4217 	EXP 	unnamed TextStim: height = 0.03
46.4217 	EXP 	unnamed TextStim: height = 0.03
46.4217 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.4217 	EXP 	unnamed TextStim: text = 'Inhale'
46.4377 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.4377 	EXP 	unnamed TextStim: text = 'Inhale'
46.4545 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.4545 	EXP 	unnamed TextStim: text = 'Inhale'
46.4720 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.4720 	EXP 	unnamed TextStim: text = 'Inhale'
46.4880 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.4880 	EXP 	unnamed TextStim: text = 'Inhale'
46.5045 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.5045 	EXP 	unnamed TextStim: text = 'Inhale'
46.5215 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.5215 	EXP 	unnamed TextStim: text = 'Inhale'
46.5377 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.5377 	EXP 	unnamed TextStim: text = 'Inhale'
46.5548 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.5548 	EXP 	unnamed TextStim: text = 'Inhale'
46.5713 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.5713 	EXP 	unnamed TextStim: text = 'Inhale'
46.5877 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.5877 	EXP 	unnamed TextStim: text = 'Inhale'
46.6044 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.6044 	EXP 	unnamed TextStim: text = 'Inhale'
46.6209 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.6209 	EXP 	unnamed TextStim: text = 'Inhale'
46.6377 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.6377 	EXP 	unnamed TextStim: text = 'Inhale'
46.6544 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.6544 	EXP 	unnamed TextStim: text = 'Inhale'
46.6706 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.6706 	EXP 	unnamed TextStim: text = 'Inhale'
46.6877 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.6877 	EXP 	unnamed TextStim: text = 'Inhale'
46.7046 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.7046 	EXP 	unnamed TextStim: text = 'Inhale'
46.7208 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.7208 	EXP 	unnamed TextStim: text = 'Inhale'
46.7376 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.7376 	EXP 	unnamed TextStim: text = 'Inhale'
46.7543 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.7543 	EXP 	unnamed TextStim: text = 'Inhale'
46.7708 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.7708 	EXP 	unnamed TextStim: text = 'Inhale'
46.7884 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.7884 	EXP 	unnamed TextStim: text = 'Inhale'
46.8044 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.8044 	EXP 	unnamed TextStim: text = 'Inhale'
46.8212 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.8212 	EXP 	unnamed TextStim: text = 'Inhale'
46.8377 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.8377 	EXP 	unnamed TextStim: text = 'Inhale'
46.8541 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.8541 	EXP 	unnamed TextStim: text = 'Inhale'
46.8709 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.8709 	EXP 	unnamed TextStim: text = 'Inhale'
46.8876 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.8876 	EXP 	unnamed TextStim: text = 'Inhale'
46.9042 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.9042 	EXP 	unnamed TextStim: text = 'Inhale'
46.9210 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.9210 	EXP 	unnamed TextStim: text = 'Inhale'
46.9376 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.9376 	EXP 	unnamed TextStim: text = 'Inhale'
46.9545 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.9545 	EXP 	unnamed TextStim: text = 'Inhale'
46.9713 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.9713 	EXP 	unnamed TextStim: text = 'Inhale'
46.9877 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
46.9877 	EXP 	unnamed TextStim: text = 'Inhale'
47.0041 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.0041 	EXP 	unnamed TextStim: text = 'Inhale'
47.0208 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.0208 	EXP 	unnamed TextStim: text = 'Inhale'
47.0369 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.0369 	EXP 	unnamed TextStim: text = 'Inhale'
47.0539 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.0539 	EXP 	unnamed TextStim: text = 'Inhale'
47.0709 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.0709 	EXP 	unnamed TextStim: text = 'Inhale'
47.0876 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.0876 	EXP 	unnamed TextStim: text = 'Inhale'
47.1040 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.1040 	EXP 	unnamed TextStim: text = 'Inhale'
47.1207 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.1207 	EXP 	unnamed TextStim: text = 'Inhale'
47.1377 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.1377 	EXP 	unnamed TextStim: text = 'Inhale'
47.1543 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.1543 	EXP 	unnamed TextStim: text = 'Inhale'
47.1708 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.1708 	EXP 	unnamed TextStim: text = 'Inhale'
47.1877 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.1877 	EXP 	unnamed TextStim: text = 'Inhale'
47.2043 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.2043 	EXP 	unnamed TextStim: text = 'Inhale'
47.2212 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.2212 	EXP 	unnamed TextStim: text = 'Inhale'
47.2379 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.2379 	EXP 	unnamed TextStim: text = 'Inhale'
47.2539 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.2539 	EXP 	unnamed TextStim: text = 'Inhale'
47.2706 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.2706 	EXP 	unnamed TextStim: text = 'Inhale'
47.2876 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.2876 	EXP 	unnamed TextStim: text = 'Inhale'
47.3039 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.3039 	EXP 	unnamed TextStim: text = 'Inhale'
47.3208 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.3208 	EXP 	unnamed TextStim: text = 'Inhale'
47.3377 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.3377 	EXP 	unnamed TextStim: text = 'Inhale'
47.3539 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.3539 	EXP 	unnamed TextStim: text = 'Inhale'
47.3704 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.3704 	EXP 	unnamed TextStim: text = 'Inhale'
47.3876 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.3876 	EXP 	unnamed TextStim: text = 'Inhale'
47.4039 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
47.4039 	EXP 	unnamed TextStim: text = 'Inhale'
47.4211 	EXP 	unnamed TextStim: height = 0.03
47.4211 	EXP 	unnamed TextStim: height = 0.03
47.4211 	EXP 	unnamed TextStim: height = 0.03
47.4211 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.4211 	EXP 	unnamed TextStim: text = 'Inhale'
47.4376 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.4376 	EXP 	unnamed TextStim: text = 'Inhale'
47.4539 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.4539 	EXP 	unnamed TextStim: text = 'Inhale'
47.4705 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.4705 	EXP 	unnamed TextStim: text = 'Inhale'
47.4877 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.4877 	EXP 	unnamed TextStim: text = 'Inhale'
47.5037 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.5037 	EXP 	unnamed TextStim: text = 'Inhale'
47.5201 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.5201 	EXP 	unnamed TextStim: text = 'Inhale'
47.5369 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.5369 	EXP 	unnamed TextStim: text = 'Inhale'
47.5534 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.5534 	EXP 	unnamed TextStim: text = 'Inhale'
47.5700 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.5700 	EXP 	unnamed TextStim: text = 'Inhale'
47.5867 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.5867 	EXP 	unnamed TextStim: text = 'Inhale'
47.6035 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.6035 	EXP 	unnamed TextStim: text = 'Inhale'
47.6201 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.6201 	EXP 	unnamed TextStim: text = 'Inhale'
47.6365 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.6365 	EXP 	unnamed TextStim: text = 'Inhale'
47.6536 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.6536 	EXP 	unnamed TextStim: text = 'Inhale'
47.6702 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.6702 	EXP 	unnamed TextStim: text = 'Inhale'
47.6870 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.6870 	EXP 	unnamed TextStim: text = 'Inhale'
47.7035 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.7035 	EXP 	unnamed TextStim: text = 'Inhale'
47.7204 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.7204 	EXP 	unnamed TextStim: text = 'Inhale'
47.7369 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.7369 	EXP 	unnamed TextStim: text = 'Inhale'
47.7532 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.7532 	EXP 	unnamed TextStim: text = 'Inhale'
47.7702 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.7702 	EXP 	unnamed TextStim: text = 'Inhale'
47.7869 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.7869 	EXP 	unnamed TextStim: text = 'Inhale'
47.8036 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.8036 	EXP 	unnamed TextStim: text = 'Inhale'
47.8202 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.8202 	EXP 	unnamed TextStim: text = 'Inhale'
47.8365 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.8365 	EXP 	unnamed TextStim: text = 'Inhale'
47.8533 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.8533 	EXP 	unnamed TextStim: text = 'Inhale'
47.8699 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.8699 	EXP 	unnamed TextStim: text = 'Inhale'
47.8876 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.8876 	EXP 	unnamed TextStim: text = 'Inhale'
47.9036 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.9036 	EXP 	unnamed TextStim: text = 'Inhale'
47.9205 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.9205 	EXP 	unnamed TextStim: text = 'Inhale'
47.9372 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.9372 	EXP 	unnamed TextStim: text = 'Inhale'
47.9542 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.9542 	EXP 	unnamed TextStim: text = 'Inhale'
47.9732 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.9732 	EXP 	unnamed TextStim: text = 'Inhale'
47.9864 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
47.9864 	EXP 	unnamed TextStim: text = 'Inhale'
48.0031 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.0031 	EXP 	unnamed TextStim: text = 'Inhale'
48.0199 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.0199 	EXP 	unnamed TextStim: text = 'Inhale'
48.0365 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.0365 	EXP 	unnamed TextStim: text = 'Inhale'
48.0528 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.0528 	EXP 	unnamed TextStim: text = 'Inhale'
48.0699 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.0699 	EXP 	unnamed TextStim: text = 'Inhale'
48.0864 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.0864 	EXP 	unnamed TextStim: text = 'Inhale'
48.1034 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.1034 	EXP 	unnamed TextStim: text = 'Inhale'
48.1201 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.1201 	EXP 	unnamed TextStim: text = 'Inhale'
48.1366 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.1366 	EXP 	unnamed TextStim: text = 'Inhale'
48.1535 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.1535 	EXP 	unnamed TextStim: text = 'Inhale'
48.1702 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.1702 	EXP 	unnamed TextStim: text = 'Inhale'
48.1867 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.1867 	EXP 	unnamed TextStim: text = 'Inhale'
48.2033 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.2033 	EXP 	unnamed TextStim: text = 'Inhale'
48.2193 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.2193 	EXP 	unnamed TextStim: text = 'Inhale'
48.2362 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.2362 	EXP 	unnamed TextStim: text = 'Inhale'
48.2534 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.2534 	EXP 	unnamed TextStim: text = 'Inhale'
48.2699 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.2699 	EXP 	unnamed TextStim: text = 'Inhale'
48.2863 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.2863 	EXP 	unnamed TextStim: text = 'Inhale'
48.3032 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.3032 	EXP 	unnamed TextStim: text = 'Inhale'
48.3198 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.3198 	EXP 	unnamed TextStim: text = 'Inhale'
48.3366 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.3366 	EXP 	unnamed TextStim: text = 'Inhale'
48.3536 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.3536 	EXP 	unnamed TextStim: text = 'Inhale'
48.3698 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.3698 	EXP 	unnamed TextStim: text = 'Inhale'
48.3861 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.3861 	EXP 	unnamed TextStim: text = 'Inhale'
48.4030 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
48.4030 	EXP 	unnamed TextStim: text = 'Inhale'
48.4194 	EXP 	unnamed TextStim: height = 0.03
48.4194 	EXP 	unnamed TextStim: height = 0.03
48.4194 	EXP 	unnamed TextStim: height = 0.03
48.4194 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.4194 	EXP 	unnamed TextStim: text = 'Inhale'
48.4383 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.4383 	EXP 	unnamed TextStim: text = 'Inhale'
48.4532 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.4532 	EXP 	unnamed TextStim: text = 'Inhale'
48.4694 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.4694 	EXP 	unnamed TextStim: text = 'Inhale'
48.4859 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.4859 	EXP 	unnamed TextStim: text = 'Inhale'
48.5031 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.5031 	EXP 	unnamed TextStim: text = 'Inhale'
48.5196 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.5196 	EXP 	unnamed TextStim: text = 'Inhale'
48.5362 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.5362 	EXP 	unnamed TextStim: text = 'Inhale'
48.5528 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.5528 	EXP 	unnamed TextStim: text = 'Inhale'
48.5693 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.5693 	EXP 	unnamed TextStim: text = 'Inhale'
48.5861 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.5861 	EXP 	unnamed TextStim: text = 'Inhale'
48.6029 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.6029 	EXP 	unnamed TextStim: text = 'Inhale'
48.6200 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.6200 	EXP 	unnamed TextStim: text = 'Inhale'
48.6365 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.6365 	EXP 	unnamed TextStim: text = 'Inhale'
48.6529 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.6529 	EXP 	unnamed TextStim: text = 'Inhale'
48.6699 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.6699 	EXP 	unnamed TextStim: text = 'Inhale'
48.6860 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.6860 	EXP 	unnamed TextStim: text = 'Inhale'
48.7025 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.7025 	EXP 	unnamed TextStim: text = 'Inhale'
48.7192 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.7192 	EXP 	unnamed TextStim: text = 'Inhale'
48.7357 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.7357 	EXP 	unnamed TextStim: text = 'Inhale'
48.7526 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.7526 	EXP 	unnamed TextStim: text = 'Inhale'
48.7692 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.7692 	EXP 	unnamed TextStim: text = 'Inhale'
48.7858 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.7858 	EXP 	unnamed TextStim: text = 'Inhale'
48.8027 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.8027 	EXP 	unnamed TextStim: text = 'Inhale'
48.8194 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.8194 	EXP 	unnamed TextStim: text = 'Inhale'
48.8364 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.8364 	EXP 	unnamed TextStim: text = 'Inhale'
48.8530 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.8530 	EXP 	unnamed TextStim: text = 'Inhale'
48.8694 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.8694 	EXP 	unnamed TextStim: text = 'Inhale'
48.8860 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.8860 	EXP 	unnamed TextStim: text = 'Inhale'
48.9028 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.9028 	EXP 	unnamed TextStim: text = 'Inhale'
48.9193 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.9193 	EXP 	unnamed TextStim: height = 0.05
48.9193 	EXP 	unnamed TextStim: height = 0.05
48.9193 	EXP 	unnamed TextStim: height = 0.05
48.9193 	EXP 	unnamed TextStim: text = 'Hold'
48.9361 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.9361 	EXP 	unnamed TextStim: text = 'Hold'
48.9530 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.9530 	EXP 	unnamed TextStim: text = 'Hold'
48.9695 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.9695 	EXP 	unnamed TextStim: text = 'Hold'
48.9881 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
48.9881 	EXP 	unnamed TextStim: text = 'Hold'
49.0032 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.0032 	EXP 	unnamed TextStim: text = 'Hold'
49.0191 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.0191 	EXP 	unnamed TextStim: text = 'Hold'
49.0358 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.0358 	EXP 	unnamed TextStim: text = 'Hold'
49.0525 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.0525 	EXP 	unnamed TextStim: text = 'Hold'
49.0691 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.0691 	EXP 	unnamed TextStim: text = 'Hold'
49.0854 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.0854 	EXP 	unnamed TextStim: text = 'Hold'
49.1031 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.1031 	EXP 	unnamed TextStim: text = 'Hold'
49.1192 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.1192 	EXP 	unnamed TextStim: text = 'Hold'
49.1358 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.1358 	EXP 	unnamed TextStim: text = 'Hold'
49.1527 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.1527 	EXP 	unnamed TextStim: text = 'Hold'
49.1692 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.1692 	EXP 	unnamed TextStim: text = 'Hold'
49.1855 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.1855 	EXP 	unnamed TextStim: text = 'Hold'
49.2032 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.2032 	EXP 	unnamed TextStim: text = 'Hold'
49.2199 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.2199 	EXP 	unnamed TextStim: text = 'Hold'
49.2364 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.2364 	EXP 	unnamed TextStim: text = 'Hold'
49.2535 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.2535 	EXP 	unnamed TextStim: text = 'Hold'
49.2700 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.2700 	EXP 	unnamed TextStim: text = 'Hold'
49.2864 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.2864 	EXP 	unnamed TextStim: text = 'Hold'
49.3031 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.3031 	EXP 	unnamed TextStim: text = 'Hold'
49.3199 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.3199 	EXP 	unnamed TextStim: text = 'Hold'
49.3366 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.3366 	EXP 	unnamed TextStim: text = 'Hold'
49.3535 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.3535 	EXP 	unnamed TextStim: text = 'Hold'
49.3696 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.3696 	EXP 	unnamed TextStim: text = 'Hold'
49.3861 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.3861 	EXP 	unnamed TextStim: text = 'Hold'
49.4034 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
49.4034 	EXP 	unnamed TextStim: text = 'Hold'
51.4893 	EXP 	stroop_instr: autoDraw = True
51.4893 	EXP 	instrText_4: autoDraw = True
52.3196 	DATA 	Keypress: escape
52.4838 	EXP 	01_resting_state: status = STOPPED
52.4997 	EXP 	stroop_instr: autoDraw = False
52.4997 	EXP 	instrText_4: autoDraw = False
52.4998 	EXP 	01_resting_state: status = STOPPED
52.5961 	EXP 	window1: mouseVisible = True
