import pandas as pd
from psychopy import core, visual, event, sound

# Read the Excel file
schedule_data = pd.read_excel('participant_schedule.xlsx')

# Get current participant and session numbers
participant_num = int(expInfo['participant'])
session_num = int(expInfo['session'])

# Find the row for the current participant and session
current_row = schedule_data[(schedule_data['participant'] == participant_num) & 
                            (schedule_data['session'] == session_num)]

if not current_row.empty:
    # Get video filenames for the current participant and session
    interview_video = current_row['interview_video'].values[0]
    breathing_video = current_row['breathing_video'].values[0]
    
    # Store these in expInfo for easier access
    expInfo['interview_video'] = interview_video
    expInfo['breathing_video'] = breathing_video
else:
    # Handle case where participant/session combination is not in the Excel file
    print(f"Warning: Participant {participant_num}, Session {session_num} not found in participant_schedule.xlsx")
    # Set default video filenames
    expInfo['interview_video'] = 'default_interview.mp4'
    expInfo['breathing_video'] = 'default_breathing.mp4'

breathing_task_text = ''
if breathing_video == 'breath_paced_breathing.mp4':
    breathing_task_text = 'Paced Breathing'
elif breathing_video == 'breath_wim_hof.mp4':
    breathing_task_text = 'Wim Hof Breathing'
elif breathing_video == 'breath_resting_state.mp4':
    breathing_task_text = 'Resting state'
breathing_task_text = breathing_task_text + " task: 6 min. \n Just follow the video for inhale and exhale, and relax. \n Press F1 to start."

# Print debug information
print(f"Participant: {participant_num}, Session: {session_num}")
print(f"Current row: {current_row}")
print(f"Breathing video: {expInfo['breathing_video']}")
print(f"Interview video: {expInfo['interview_video']}")

# Print all data stored in expInfo
print(f"All data in expInfo: {expInfo}")

# Initialize the wrong answer sound
#wrong_sound = sound.Sound('sound/wrong.wav')
# Replace the current wrong_sound initialization with:
wrong_sound = sound.Sound('sound/wrong.wav')