7.8074 	WARNING 	User requested fullscreen with size [1536  864], but screen is actually [1920, 1080]. Using actual size
8.4315 	EXP 	Created window1 = Window(allowGUI=True, allowStencil=True, autoLog=True, backendConf=UNKNOWN, backgroundFit=<method-wrapper '__getattribute__' of attributeSetter object at 0x000002531657B3D0>, backgroundImage=<method-wrapper '__getattribute__' of attributeSetter object at 0x000002531657B370>, bitsMode=UNKNOWN, blendMode='avg', bpc=(8, 8, 8), color=array([0, 0, 0]), colorSpace='rgb', depthBits=8, fullscr=<method-wrapper '__getattribute__' of attributeSetter object at 0x000002531657B1C0>, gamma=None, gammaErrorPolicy='raise', lms=UNKNOWN, monitor=<psychopy.monitors.calibTools.Monitor object at 0x000002531CDAC3A0>, multiSample=False, name='window1', numSamples=2, pos=(0, 0), screen=0, size=array([1920, 1080]), stencilBits=8, stereo=False, title='PsychoPy', units='height', useFBO=True, useRetina=False, viewOri=0.0, viewPos=None, viewScale=None, waitBlanking=True, winType='pyglet')
8.4316 	EXP 	window1: mouseVisible = True
8.4316 	EXP 	window1: backgroundImage = ''
8.4316 	EXP 	window1: backgroundFit = 'none'
8.4333 	EXP 	window1: Attempting to measure frame rate of screen (0) ...
8.4333 	EXP 	window1: recordFrameIntervals = False
8.5973 	EXP 	window1: recordFrameIntervals = True
8.7811 	EXP 	Screen (0) actual frame rate measured at 59.99Hz
8.7812 	EXP 	window1: recordFrameIntervals = False
8.7815 	EXP 	window1: mouseVisible = False
11.5923 	EXP 	01_resting_state: status = STARTED
12.0358 	EXP 	window1: mouseVisible = True
12.1184 	EXP 	Created text = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.1185 	EXP 	window1: mouseVisible = True
12.1228 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
12.1229 	EXP 	window1: mouseVisible = True
12.1365 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.1384 	EXP 	Sound exp_end_bip set volume 1.000
12.1386 	EXP 	window1: mouseVisible = True
12.1499 	EXP 	Created text_2 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_2', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.1549 	EXP 	window1: mouseVisible = True
12.1797 	EXP 	window1: mouseVisible = True
12.1866 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.1882 	EXP 	Sound exp_end_bip set volume 1.000
12.1884 	EXP 	window1: mouseVisible = True
12.1950 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.1951 	EXP 	window1: mouseVisible = True
12.2009 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.2026 	EXP 	Sound exp_end_bip set volume 1.000
12.2027 	EXP 	window1: mouseVisible = True
12.2091 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.2107 	EXP 	Sound exp_end_bip set volume 1.000
12.2109 	EXP 	window1: mouseVisible = True
12.2152 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.2153 	EXP 	window1: mouseVisible = True
12.2212 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.2230 	EXP 	Sound exp_end_bip set volume 1.000
12.2232 	EXP 	window1: mouseVisible = True
12.2368 	EXP 	Created stroop_instr = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='stroop_instr', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.2369 	EXP 	window1: mouseVisible = True
12.2463 	EXP 	Created instrText_4 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-2.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_4', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.2464 	EXP 	window1: mouseVisible = True
12.2477 	EXP 	Created stim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.15, italic=False, languageStyle='LTR', name='stim', opacity=1.0, ori=0, pos=array([0., 0.]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
12.2480 	EXP 	window1: mouseVisible = True
12.2481 	EXP 	window1: mouseVisible = True
12.2485 	EXP 	window1: mouseVisible = True
12.2488 	EXP 	window1: mouseVisible = True
12.2523 	EXP 	window1: mouseVisible = True
12.2524 	EXP 	window1: mouseVisible = True
12.2528 	EXP 	window1: mouseVisible = True
12.2532 	EXP 	window1: mouseVisible = True
12.2711 	EXP 	window1: mouseVisible = True
12.2754 	EXP 	Created instrText_2 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-4.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_2', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.2756 	EXP 	window1: mouseVisible = True
12.2757 	EXP 	window1: mouseVisible = True
12.2761 	EXP 	window1: mouseVisible = True
12.2764 	EXP 	window1: mouseVisible = True
12.2797 	EXP 	window1: mouseVisible = True
12.2798 	EXP 	window1: mouseVisible = True
12.2801 	EXP 	window1: mouseVisible = True
12.2805 	EXP 	window1: mouseVisible = True
12.2836 	EXP 	window1: mouseVisible = True
12.2840 	EXP 	window1: mouseVisible = True
12.2847 	EXP 	window1: mouseVisible = True
12.2851 	EXP 	window1: mouseVisible = True
12.2886 	EXP 	window1: mouseVisible = True
12.2887 	EXP 	window1: mouseVisible = True
12.2893 	EXP 	window1: mouseVisible = True
12.2899 	EXP 	window1: mouseVisible = True
12.2930 	EXP 	window1: mouseVisible = True
12.2971 	EXP 	Created instrText_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-3.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_3', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.2972 	EXP 	window1: mouseVisible = True
12.3110 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.3128 	EXP 	Sound exp_end_bip set volume 1.000
12.3129 	EXP 	window1: mouseVisible = True
12.3195 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.3197 	EXP 	window1: mouseVisible = True
12.3260 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.3277 	EXP 	Sound exp_end_bip set volume 1.000
12.3279 	EXP 	window1: mouseVisible = True
12.3335 	EXP 	Created text = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.3337 	EXP 	window1: mouseVisible = True
12.3370 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
12.3372 	EXP 	window1: mouseVisible = True
12.3487 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.3504 	EXP 	Sound exp_end_bip set volume 1.000
12.3505 	EXP 	window1: mouseVisible = True
12.3572 	EXP 	Created EndMessage = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='EndMessage', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='Thank you!', units='height', win=Window(...), wrapWidth=1)
0.0053 	EXP 	video: autoLog = True
0.0053 	EXP 	trial_counter: autoLog = True
0.0053 	EXP 	fbtxt: autoLog = True
0.0053 	EXP 	trial_counter_2: autoLog = True
0.0198 	EXP 	text: autoDraw = True
19.9443 	DATA 	Keypress: f1
19.9541 	EXP 	text: autoDraw = False
19.9541 	EXP 	cross: autoDraw = True
30.9618 	EXP 	Sound exp_end_bip started
30.9615 	EXP 	cross: autoDraw = False
30.9615 	EXP 	cross: autoDraw = False
30.9615 	EXP 	text_q_stress: autoDraw = True
32.1551 	EXP 	Sound exp_end_bip stopped
33.4829 	DATA 	Keypress: f1
33.4927 	EXP 	text_q_stress: autoDraw = False
33.4927 	EXP 	text_2: autoDraw = True
34.8933 	DATA 	Keypress: f1
35.0355 	EXP 	text_2: autoDraw = False
35.0355 	EXP 	video: autoDraw = True
37.3053 	EXP 	Sound exp_end_bip started
37.2977 	EXP 	video: autoDraw = False
37.2977 	EXP 	video: autoDraw = False
37.2977 	EXP 	text_q_stress: autoDraw = True
38.4922 	EXP 	Sound exp_end_bip stopped
38.5414 	DATA 	Keypress: f1
38.5546 	EXP 	text_q_stress: autoDraw = False
38.5546 	EXP 	text_3: autoDraw = True
39.4904 	EXP 	window1: mouseVisible = True
39.4945 	EXP 	Created unnamed ShapeStim = ShapeStim(__class__=<class 'psychopy.visual.shape.ShapeStim'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=False, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0, 0, 0]), fillColorSpace='rgb', fillRGB=array([0, 0, 0]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=3, name='unnamed ShapeStim', opacity=0.35, ori=0.0, pos=array([0., 0.]), size=array([1., 1.]), units='height', vertices=ndarray(...), win=Window(...), windingRule=None)
39.5027 	EXP 	window1: mouseVisible = True
39.5111 	EXP 	Created unnamed Circle = Circle(__class__=<class 'psychopy.visual.circle.Circle'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=2, name='unnamed Circle', opacity=1.0, ori=0.0, pos=array([0., 0.]), size=array([1., 1.]), units='height', vertices=ndarray(...), win=Window(...))
39.5113 	EXP 	window1: mouseVisible = True
39.5136 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.05882353, 0.61568627, 0.84313725]), fillColorSpace='rgb', fillRGB=array([0.05882353, 0.61568627, 0.84313725]), interpolate=True, lineColor=array([0., 0., 0.]), lineColorSpace='rgb', lineRGB=array([0, 0, 0]), lineWidth=1.5, name='unnamed Rect', opacity=0.1, ori=0.0, pos=array([0., 0.]), size=array([1.77777778, 0.5       ]), units='height', vertices=ndarray(...), win=Window(...))
39.5138 	EXP 	window1: mouseVisible = True
39.5221 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([ 0.  , -0.35]), rgb=UNKNOWN, text='Inhale', units='height', win=Window(...), wrapWidth=1)
39.5223 	EXP 	window1: mouseVisible = True
39.5241 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([0. , 0.4]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
39.5332 	DATA 	Keypress: f1
39.5373 	EXP 	text_3: autoDraw = False
39.5373 	EXP 	unnamed TextStim: text = 'Inhale'
39.5373 	EXP 	unnamed TextStim: height = 0.03
39.5373 	EXP 	unnamed TextStim: height = 0.03
39.5373 	EXP 	unnamed TextStim: height = 0.03
39.5373 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.5535 	EXP 	unnamed TextStim: text = 'Inhale'
39.5535 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.5704 	EXP 	unnamed TextStim: text = 'Inhale'
39.5704 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.5884 	EXP 	unnamed TextStim: text = 'Inhale'
39.5884 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.6036 	EXP 	unnamed TextStim: text = 'Inhale'
39.6036 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.6203 	EXP 	unnamed TextStim: text = 'Inhale'
39.6203 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.6368 	EXP 	unnamed TextStim: text = 'Inhale'
39.6368 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.6538 	EXP 	unnamed TextStim: text = 'Inhale'
39.6538 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.6704 	EXP 	unnamed TextStim: text = 'Inhale'
39.6704 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.6870 	EXP 	unnamed TextStim: text = 'Inhale'
39.6870 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.7039 	EXP 	unnamed TextStim: text = 'Inhale'
39.7039 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.7208 	EXP 	unnamed TextStim: text = 'Inhale'
39.7208 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.7370 	EXP 	unnamed TextStim: text = 'Inhale'
39.7370 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.7541 	EXP 	unnamed TextStim: text = 'Inhale'
39.7541 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.7711 	EXP 	unnamed TextStim: text = 'Inhale'
39.7711 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.7873 	EXP 	unnamed TextStim: text = 'Inhale'
39.7873 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.8039 	EXP 	unnamed TextStim: text = 'Inhale'
39.8039 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.8210 	EXP 	unnamed TextStim: text = 'Inhale'
39.8210 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.8373 	EXP 	unnamed TextStim: text = 'Inhale'
39.8373 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.8537 	EXP 	unnamed TextStim: text = 'Inhale'
39.8537 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.8708 	EXP 	unnamed TextStim: text = 'Inhale'
39.8708 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.8875 	EXP 	unnamed TextStim: text = 'Inhale'
39.8875 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.9040 	EXP 	unnamed TextStim: text = 'Inhale'
39.9040 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.9210 	EXP 	unnamed TextStim: text = 'Inhale'
39.9210 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.9376 	EXP 	unnamed TextStim: text = 'Inhale'
39.9376 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.9536 	EXP 	unnamed TextStim: text = 'Inhale'
39.9536 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.9706 	EXP 	unnamed TextStim: text = 'Inhale'
39.9706 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
39.9869 	EXP 	unnamed TextStim: text = 'Inhale'
39.9869 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.0037 	EXP 	unnamed TextStim: text = 'Inhale'
40.0037 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.0206 	EXP 	unnamed TextStim: text = 'Inhale'
40.0206 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.0370 	EXP 	unnamed TextStim: text = 'Inhale'
40.0370 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.0534 	EXP 	unnamed TextStim: text = 'Inhale'
40.0534 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.0709 	EXP 	unnamed TextStim: text = 'Inhale'
40.0709 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.0870 	EXP 	unnamed TextStim: text = 'Inhale'
40.0870 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.1034 	EXP 	unnamed TextStim: text = 'Inhale'
40.1034 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.1209 	EXP 	unnamed TextStim: text = 'Inhale'
40.1209 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.1373 	EXP 	unnamed TextStim: text = 'Inhale'
40.1373 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.1541 	EXP 	unnamed TextStim: text = 'Inhale'
40.1541 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.1705 	EXP 	unnamed TextStim: text = 'Inhale'
40.1705 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.1864 	EXP 	unnamed TextStim: text = 'Inhale'
40.1864 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.2030 	EXP 	unnamed TextStim: text = 'Inhale'
40.2030 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.2203 	EXP 	unnamed TextStim: text = 'Inhale'
40.2203 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.2370 	EXP 	unnamed TextStim: text = 'Inhale'
40.2370 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.2535 	EXP 	unnamed TextStim: text = 'Inhale'
40.2535 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.2706 	EXP 	unnamed TextStim: text = 'Inhale'
40.2706 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.2870 	EXP 	unnamed TextStim: text = 'Inhale'
40.2870 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.3032 	EXP 	unnamed TextStim: text = 'Inhale'
40.3032 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.3202 	EXP 	unnamed TextStim: text = 'Inhale'
40.3202 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.3371 	EXP 	unnamed TextStim: text = 'Inhale'
40.3371 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.3534 	EXP 	unnamed TextStim: text = 'Inhale'
40.3534 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.3705 	EXP 	unnamed TextStim: text = 'Inhale'
40.3705 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.3871 	EXP 	unnamed TextStim: text = 'Inhale'
40.3871 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.4035 	EXP 	unnamed TextStim: text = 'Inhale'
40.4035 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.4203 	EXP 	unnamed TextStim: text = 'Inhale'
40.4203 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.4364 	EXP 	unnamed TextStim: text = 'Inhale'
40.4364 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.4533 	EXP 	unnamed TextStim: text = 'Inhale'
40.4533 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.4703 	EXP 	unnamed TextStim: text = 'Inhale'
40.4703 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.4871 	EXP 	unnamed TextStim: text = 'Inhale'
40.4871 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.5040 	EXP 	unnamed TextStim: text = 'Inhale'
40.5040 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.5202 	EXP 	unnamed TextStim: text = 'Inhale'
40.5202 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.5371 	EXP 	unnamed TextStim: text = 'Inhale'
40.5371 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
40.5576 	EXP 	unnamed TextStim: text = 'Inhale'
40.5576 	EXP 	unnamed TextStim: height = 0.03
40.5576 	EXP 	unnamed TextStim: height = 0.03
40.5576 	EXP 	unnamed TextStim: height = 0.03
40.5576 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.5696 	EXP 	unnamed TextStim: text = 'Inhale'
40.5696 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.5871 	EXP 	unnamed TextStim: text = 'Inhale'
40.5871 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.6037 	EXP 	unnamed TextStim: text = 'Inhale'
40.6037 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.6198 	EXP 	unnamed TextStim: text = 'Inhale'
40.6198 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.6374 	EXP 	unnamed TextStim: text = 'Inhale'
40.6374 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.6536 	EXP 	unnamed TextStim: text = 'Inhale'
40.6536 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.6703 	EXP 	unnamed TextStim: text = 'Inhale'
40.6703 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.6870 	EXP 	unnamed TextStim: text = 'Inhale'
40.6870 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.7034 	EXP 	unnamed TextStim: text = 'Inhale'
40.7034 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.7202 	EXP 	unnamed TextStim: text = 'Inhale'
40.7202 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.7370 	EXP 	unnamed TextStim: text = 'Inhale'
40.7370 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.7529 	EXP 	unnamed TextStim: text = 'Inhale'
40.7529 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.7693 	EXP 	unnamed TextStim: text = 'Inhale'
40.7693 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.7860 	EXP 	unnamed TextStim: text = 'Inhale'
40.7860 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.8025 	EXP 	unnamed TextStim: text = 'Inhale'
40.8025 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.8194 	EXP 	unnamed TextStim: text = 'Inhale'
40.8194 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.8360 	EXP 	unnamed TextStim: text = 'Inhale'
40.8360 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.8530 	EXP 	unnamed TextStim: text = 'Inhale'
40.8530 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.8703 	EXP 	unnamed TextStim: text = 'Inhale'
40.8703 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.8865 	EXP 	unnamed TextStim: text = 'Inhale'
40.8865 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.9028 	EXP 	unnamed TextStim: text = 'Inhale'
40.9028 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.9199 	EXP 	unnamed TextStim: text = 'Inhale'
40.9199 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.9363 	EXP 	unnamed TextStim: text = 'Inhale'
40.9363 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.9528 	EXP 	unnamed TextStim: text = 'Inhale'
40.9528 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.9695 	EXP 	unnamed TextStim: text = 'Inhale'
40.9695 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
40.9865 	EXP 	unnamed TextStim: text = 'Inhale'
40.9865 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.0030 	EXP 	unnamed TextStim: text = 'Inhale'
41.0030 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.0196 	EXP 	unnamed TextStim: text = 'Inhale'
41.0196 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.0363 	EXP 	unnamed TextStim: text = 'Inhale'
41.0363 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.0527 	EXP 	unnamed TextStim: text = 'Inhale'
41.0527 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.0692 	EXP 	unnamed TextStim: text = 'Inhale'
41.0692 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.0860 	EXP 	unnamed TextStim: text = 'Inhale'
41.0860 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.1024 	EXP 	unnamed TextStim: text = 'Inhale'
41.1024 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.1196 	EXP 	unnamed TextStim: text = 'Inhale'
41.1196 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.1362 	EXP 	unnamed TextStim: text = 'Inhale'
41.1362 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.1531 	EXP 	unnamed TextStim: text = 'Inhale'
41.1531 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.1695 	EXP 	unnamed TextStim: text = 'Inhale'
41.1695 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.1867 	EXP 	unnamed TextStim: text = 'Inhale'
41.1867 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.2030 	EXP 	unnamed TextStim: text = 'Inhale'
41.2030 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.2200 	EXP 	unnamed TextStim: text = 'Inhale'
41.2200 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.2376 	EXP 	unnamed TextStim: text = 'Inhale'
41.2376 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.2530 	EXP 	unnamed TextStim: text = 'Inhale'
41.2530 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.2698 	EXP 	unnamed TextStim: text = 'Inhale'
41.2698 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.2861 	EXP 	unnamed TextStim: text = 'Inhale'
41.2861 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.3025 	EXP 	unnamed TextStim: text = 'Inhale'
41.3025 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.3189 	EXP 	unnamed TextStim: text = 'Inhale'
41.3189 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.3354 	EXP 	unnamed TextStim: text = 'Inhale'
41.3354 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.3522 	EXP 	unnamed TextStim: text = 'Inhale'
41.3522 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.3688 	EXP 	unnamed TextStim: text = 'Inhale'
41.3688 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.3857 	EXP 	unnamed TextStim: text = 'Inhale'
41.3857 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.4025 	EXP 	unnamed TextStim: text = 'Inhale'
41.4025 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.4191 	EXP 	unnamed TextStim: text = 'Inhale'
41.4191 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.4355 	EXP 	unnamed TextStim: text = 'Inhale'
41.4355 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.4522 	EXP 	unnamed TextStim: text = 'Inhale'
41.4522 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.4688 	EXP 	unnamed TextStim: text = 'Inhale'
41.4688 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.4862 	EXP 	unnamed TextStim: text = 'Inhale'
41.4862 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.5027 	EXP 	unnamed TextStim: text = 'Inhale'
41.5027 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.5190 	EXP 	unnamed TextStim: text = 'Inhale'
41.5190 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.5358 	EXP 	unnamed TextStim: text = 'Inhale'
41.5358 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
41.5521 	EXP 	unnamed TextStim: text = 'Inhale'
41.5521 	EXP 	unnamed TextStim: height = 0.03
41.5521 	EXP 	unnamed TextStim: height = 0.03
41.5521 	EXP 	unnamed TextStim: height = 0.03
41.5521 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.5689 	EXP 	unnamed TextStim: text = 'Inhale'
41.5689 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.5857 	EXP 	unnamed TextStim: text = 'Inhale'
41.5857 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.6027 	EXP 	unnamed TextStim: text = 'Inhale'
41.6027 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.6192 	EXP 	unnamed TextStim: text = 'Inhale'
41.6192 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.6357 	EXP 	unnamed TextStim: text = 'Inhale'
41.6357 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.6525 	EXP 	unnamed TextStim: text = 'Inhale'
41.6525 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.6692 	EXP 	unnamed TextStim: text = 'Inhale'
41.6692 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.6861 	EXP 	unnamed TextStim: text = 'Inhale'
41.6861 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.7023 	EXP 	unnamed TextStim: text = 'Inhale'
41.7023 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.7194 	EXP 	unnamed TextStim: text = 'Inhale'
41.7194 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.7358 	EXP 	unnamed TextStim: text = 'Inhale'
41.7358 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.7527 	EXP 	unnamed TextStim: text = 'Inhale'
41.7527 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.7692 	EXP 	unnamed TextStim: text = 'Inhale'
41.7692 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.7858 	EXP 	unnamed TextStim: text = 'Inhale'
41.7858 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.8026 	EXP 	unnamed TextStim: text = 'Inhale'
41.8026 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.8189 	EXP 	unnamed TextStim: text = 'Inhale'
41.8189 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.8356 	EXP 	unnamed TextStim: text = 'Inhale'
41.8356 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.8524 	EXP 	unnamed TextStim: text = 'Inhale'
41.8524 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.8693 	EXP 	unnamed TextStim: text = 'Inhale'
41.8693 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.8858 	EXP 	unnamed TextStim: text = 'Inhale'
41.8858 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.9021 	EXP 	unnamed TextStim: text = 'Inhale'
41.9021 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.9185 	EXP 	unnamed TextStim: text = 'Inhale'
41.9185 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.9350 	EXP 	unnamed TextStim: text = 'Inhale'
41.9350 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.9518 	EXP 	unnamed TextStim: text = 'Inhale'
41.9518 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.9687 	EXP 	unnamed TextStim: text = 'Inhale'
41.9687 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
41.9855 	EXP 	unnamed TextStim: text = 'Inhale'
41.9855 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.0021 	EXP 	unnamed TextStim: text = 'Inhale'
42.0021 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.0190 	EXP 	unnamed TextStim: text = 'Inhale'
42.0190 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.0356 	EXP 	unnamed TextStim: text = 'Inhale'
42.0356 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.0523 	EXP 	unnamed TextStim: text = 'Inhale'
42.0523 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.0694 	EXP 	unnamed TextStim: text = 'Inhale'
42.0694 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.0850 	EXP 	unnamed TextStim: text = 'Inhale'
42.0850 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.1018 	EXP 	unnamed TextStim: text = 'Inhale'
42.1018 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.1184 	EXP 	unnamed TextStim: text = 'Inhale'
42.1184 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.1353 	EXP 	unnamed TextStim: text = 'Inhale'
42.1353 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.1520 	EXP 	unnamed TextStim: text = 'Inhale'
42.1520 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.1686 	EXP 	unnamed TextStim: text = 'Inhale'
42.1686 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.1852 	EXP 	unnamed TextStim: text = 'Inhale'
42.1852 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.2024 	EXP 	unnamed TextStim: text = 'Inhale'
42.2024 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.2184 	EXP 	unnamed TextStim: text = 'Inhale'
42.2184 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.2351 	EXP 	unnamed TextStim: text = 'Inhale'
42.2351 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.2524 	EXP 	unnamed TextStim: text = 'Inhale'
42.2524 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.2689 	EXP 	unnamed TextStim: text = 'Inhale'
42.2689 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.2855 	EXP 	unnamed TextStim: text = 'Inhale'
42.2855 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.3026 	EXP 	unnamed TextStim: text = 'Inhale'
42.3026 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.3192 	EXP 	unnamed TextStim: text = 'Inhale'
42.3192 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.3353 	EXP 	unnamed TextStim: text = 'Inhale'
42.3353 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.3533 	EXP 	unnamed TextStim: text = 'Inhale'
42.3533 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.3685 	EXP 	unnamed TextStim: text = 'Inhale'
42.3685 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.3848 	EXP 	unnamed TextStim: text = 'Inhale'
42.3848 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.4018 	EXP 	unnamed TextStim: text = 'Inhale'
42.4018 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.4180 	EXP 	unnamed TextStim: text = 'Inhale'
42.4180 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.4350 	EXP 	unnamed TextStim: text = 'Inhale'
42.4350 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.4518 	EXP 	unnamed TextStim: text = 'Inhale'
42.4518 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.4677 	EXP 	unnamed TextStim: text = 'Inhale'
42.4677 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.4847 	EXP 	unnamed TextStim: text = 'Inhale'
42.4847 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.5014 	EXP 	unnamed TextStim: text = 'Inhale'
42.5014 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.5179 	EXP 	unnamed TextStim: text = 'Inhale'
42.5179 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.5342 	EXP 	unnamed TextStim: text = 'Inhale'
42.5342 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
42.5514 	EXP 	unnamed TextStim: text = 'Inhale'
42.5514 	EXP 	unnamed TextStim: height = 0.03
42.5514 	EXP 	unnamed TextStim: height = 0.03
42.5514 	EXP 	unnamed TextStim: height = 0.03
42.5514 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.5676 	EXP 	unnamed TextStim: text = 'Inhale'
42.5676 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.5843 	EXP 	unnamed TextStim: text = 'Inhale'
42.5843 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.6011 	EXP 	unnamed TextStim: text = 'Inhale'
42.6011 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.6178 	EXP 	unnamed TextStim: text = 'Inhale'
42.6178 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.6345 	EXP 	unnamed TextStim: text = 'Inhale'
42.6345 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.6509 	EXP 	unnamed TextStim: text = 'Inhale'
42.6509 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.6674 	EXP 	unnamed TextStim: text = 'Inhale'
42.6674 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.6839 	EXP 	unnamed TextStim: text = 'Inhale'
42.6839 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.7009 	EXP 	unnamed TextStim: text = 'Inhale'
42.7009 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.7175 	EXP 	unnamed TextStim: text = 'Inhale'
42.7175 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.7340 	EXP 	unnamed TextStim: text = 'Inhale'
42.7340 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.7507 	EXP 	unnamed TextStim: text = 'Inhale'
42.7507 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.7674 	EXP 	unnamed TextStim: text = 'Inhale'
42.7674 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.7841 	EXP 	unnamed TextStim: text = 'Inhale'
42.7841 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.8008 	EXP 	unnamed TextStim: text = 'Inhale'
42.8008 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.8173 	EXP 	unnamed TextStim: text = 'Inhale'
42.8173 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.8340 	EXP 	unnamed TextStim: text = 'Inhale'
42.8340 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.8508 	EXP 	unnamed TextStim: text = 'Inhale'
42.8508 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.8673 	EXP 	unnamed TextStim: text = 'Inhale'
42.8673 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.8839 	EXP 	unnamed TextStim: text = 'Inhale'
42.8839 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.9007 	EXP 	unnamed TextStim: text = 'Inhale'
42.9007 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.9171 	EXP 	unnamed TextStim: text = 'Inhale'
42.9171 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.9340 	EXP 	unnamed TextStim: text = 'Inhale'
42.9340 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.9513 	EXP 	unnamed TextStim: text = 'Inhale'
42.9513 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.9678 	EXP 	unnamed TextStim: text = 'Inhale'
42.9678 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
42.9847 	EXP 	unnamed TextStim: text = 'Inhale'
42.9847 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.0020 	EXP 	unnamed TextStim: text = 'Inhale'
43.0020 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.0183 	EXP 	unnamed TextStim: text = 'Inhale'
43.0183 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.0345 	EXP 	unnamed TextStim: text = 'Inhale'
43.0345 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.0520 	EXP 	unnamed TextStim: text = 'Inhale'
43.0520 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.0679 	EXP 	unnamed TextStim: text = 'Inhale'
43.0679 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.0844 	EXP 	unnamed TextStim: text = 'Inhale'
43.0844 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.1022 	EXP 	unnamed TextStim: text = 'Inhale'
43.1022 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.1179 	EXP 	unnamed TextStim: text = 'Inhale'
43.1179 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.1338 	EXP 	unnamed TextStim: text = 'Inhale'
43.1338 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.1508 	EXP 	unnamed TextStim: text = 'Inhale'
43.1508 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.1677 	EXP 	unnamed TextStim: text = 'Inhale'
43.1677 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.1839 	EXP 	unnamed TextStim: text = 'Inhale'
43.1839 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.2006 	EXP 	unnamed TextStim: text = 'Inhale'
43.2006 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.2172 	EXP 	unnamed TextStim: text = 'Inhale'
43.2172 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.2339 	EXP 	unnamed TextStim: text = 'Inhale'
43.2339 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.2510 	EXP 	unnamed TextStim: text = 'Inhale'
43.2510 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.2675 	EXP 	unnamed TextStim: text = 'Inhale'
43.2675 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.2852 	EXP 	unnamed TextStim: text = 'Inhale'
43.2852 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.3019 	EXP 	unnamed TextStim: text = 'Inhale'
43.3019 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.3181 	EXP 	unnamed TextStim: text = 'Inhale'
43.3181 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.3348 	EXP 	unnamed TextStim: text = 'Inhale'
43.3348 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.3512 	EXP 	unnamed TextStim: text = 'Inhale'
43.3512 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.3709 	EXP 	unnamed TextStim: text = 'Inhale'
43.3709 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.3843 	EXP 	unnamed TextStim: text = 'Inhale'
43.3843 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.4009 	EXP 	unnamed TextStim: text = 'Inhale'
43.4009 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.4175 	EXP 	unnamed TextStim: text = 'Inhale'
43.4175 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.4341 	EXP 	unnamed TextStim: text = 'Inhale'
43.4341 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.4508 	EXP 	unnamed TextStim: text = 'Inhale'
43.4508 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.4677 	EXP 	unnamed TextStim: text = 'Inhale'
43.4677 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.4841 	EXP 	unnamed TextStim: text = 'Inhale'
43.4841 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.5011 	EXP 	unnamed TextStim: text = 'Inhale'
43.5011 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.5175 	EXP 	unnamed TextStim: text = 'Inhale'
43.5175 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.5342 	EXP 	unnamed TextStim: text = 'Inhale'
43.5342 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
43.5517 	EXP 	unnamed TextStim: text = 'Inhale'
43.5517 	EXP 	unnamed TextStim: height = 0.03
43.5517 	EXP 	unnamed TextStim: height = 0.03
43.5517 	EXP 	unnamed TextStim: height = 0.03
43.5517 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.5677 	EXP 	unnamed TextStim: text = 'Inhale'
43.5677 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.5844 	EXP 	unnamed TextStim: text = 'Inhale'
43.5844 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.6014 	EXP 	unnamed TextStim: text = 'Inhale'
43.6014 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.6176 	EXP 	unnamed TextStim: text = 'Inhale'
43.6176 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.6339 	EXP 	unnamed TextStim: text = 'Inhale'
43.6339 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.6512 	EXP 	unnamed TextStim: text = 'Inhale'
43.6512 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.6674 	EXP 	unnamed TextStim: text = 'Inhale'
43.6674 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.6844 	EXP 	unnamed TextStim: text = 'Inhale'
43.6844 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.7012 	EXP 	unnamed TextStim: text = 'Inhale'
43.7012 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.7176 	EXP 	unnamed TextStim: text = 'Inhale'
43.7176 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.7336 	EXP 	unnamed TextStim: text = 'Inhale'
43.7336 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.7507 	EXP 	unnamed TextStim: text = 'Inhale'
43.7507 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.7672 	EXP 	unnamed TextStim: text = 'Inhale'
43.7672 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.7835 	EXP 	unnamed TextStim: text = 'Inhale'
43.7835 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.8002 	EXP 	unnamed TextStim: text = 'Inhale'
43.8002 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.8170 	EXP 	unnamed TextStim: text = 'Inhale'
43.8170 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.8340 	EXP 	unnamed TextStim: text = 'Inhale'
43.8340 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.8512 	EXP 	unnamed TextStim: text = 'Inhale'
43.8512 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.8669 	EXP 	unnamed TextStim: text = 'Inhale'
43.8669 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.8835 	EXP 	unnamed TextStim: text = 'Inhale'
43.8835 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.9010 	EXP 	unnamed TextStim: text = 'Inhale'
43.9010 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.9173 	EXP 	unnamed TextStim: text = 'Inhale'
43.9173 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.9337 	EXP 	unnamed TextStim: text = 'Inhale'
43.9337 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.9513 	EXP 	unnamed TextStim: text = 'Inhale'
43.9513 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.9683 	EXP 	unnamed TextStim: text = 'Inhale'
43.9683 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
43.9843 	EXP 	unnamed TextStim: text = 'Inhale'
43.9843 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
44.0012 	EXP 	unnamed TextStim: text = 'Inhale'
44.0012 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
44.0172 	EXP 	unnamed TextStim: text = 'Inhale'
44.0172 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
44.0339 	EXP 	unnamed TextStim: text = 'Inhale'
44.0339 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
44.2781 	EXP 	window1: mouseVisible = True
