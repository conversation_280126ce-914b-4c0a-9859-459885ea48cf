# This code is intended for a PsychoPy Builder Code Component.
# It is assumed that the following variables are defined by PsychoPy Builder
# or in a preceding Code Component:
# - win: The PsychoPy window object.
# - stress_duration: The total duration of the stress task in seconds.
# - outlet: An LSL outlet object (or similar) with a .push_sample() method for triggers.

# Import necessary PsychoPy modules and standard Python libraries
from psychopy import visual, event, core
import random

# --- Access the global wrong_sound ---
# Error checking to prevent crashes
if 'wrong_sound' not in globals():
    print("WARNING: wrong_sound not found in global scope, sound feedback will be disabled")

# --- Initialize variables ---
current_number = random.randint(1000, 1999) # Starting number for subtraction
duration = stress_duration  # Total duration from Builder variable
last_response_time = 0  # To keep track of the previous trial's response time for the speed warning
clock = core.Clock()
start_time = clock.getTime()

# --- Create a local wrong_sound specifically for this task ---
#try:
#    wrong_sound = sound.Sound('sound/wrong.wav')
#    print(f"DEBUG: Created local wrong_sound in arithmetic task: {wrong_sound}, Path: {getattr(wrong_sound, 'fileName', 'N/A')}")
#except Exception as e:
#    print(f"DEBUG: Error creating local wrong_sound in arithmetic task: {e}")
#    wrong_sound = None

# --- Define Screen Layout Parameters ---
screen_width = 2.0  # Width from -1 to 1 in normalized units
subject_proportion = 2/3  # Subject gets 2/3 of the screen
experimenter_proportion = 1/3  # Experimenter gets 1/3 of the screen

# Calculate section widths and positions
subject_width = subject_proportion * screen_width  # 4/3 normalized units
experimenter_width = experimenter_proportion * screen_width  # 2/3 normalized units
divider_x_pos = -1 + subject_width  # 1/3 normalized units
subject_section_center_x = -1 + (subject_width / 2)  # -1/3 normalized units
experimenter_section_center_x = divider_x_pos + (experimenter_width / 2)  # 2/3 normalized units

# Progress bars should take up 75% of their respective section width
subject_bar_width = 0.75 * subject_width  # 75% of subject section width
experimenter_bar_width = 0.75 * experimenter_width  # 75% of experimenter section width

# --- Create Stimuli ---
divider_line = visual.Line(
    win,
    start=(divider_x_pos, 1),
    end=(divider_x_pos, -1),
    lineColor="white",
    lineWidth=5
)
text_4digit_subject = visual.TextStim(
    win, 
    text=str(current_number), 
    pos=(subject_section_center_x, 0.2), 
    height=0.1, 
    color="white",
    font='Arial'
)
text_instruction_subject = visual.TextStim(
    win, 
    text="Keep subtracting 13", 
    pos=(subject_section_center_x, 0.1), 
    height=0.05, 
    color="white",
    font='Arial'
)
speed_warning_subject = visual.TextStim(
    win, 
    text="FASTER! You're Slow!", 
    pos=(subject_section_center_x, 0.3),
    height=0.08, 
    color="red", 
    bold=True, 
    italic=False, 
    font='Arial'
)
# New experimenter speed warning text
speed_warning_experimenter = visual.TextStim(
    win, 
    text="say: FASTER!!", 
    pos=(experimenter_section_center_x, 0.3), 
    height=0.08, 
    color="red", 
    bold=True, 
    italic=False, 
    font='Arial'
)
text_correct_answer_exp = visual.TextStim(
    win, 
    text="",
    pos=(experimenter_section_center_x, 0.2), 
    height=0.1, 
    color="lime",
    font='Arial'
)
# Create a combined instruction text that fits better in the experimenter section
# Position the elements further apart to prevent overlap
wrong_instruction = visual.TextStim(
    win, 
    text="← Wrong", 
    pos=(experimenter_section_center_x - 0.08, 0.1),  # Moved further left
    height=0.03,  # Keep the smaller text size
    color="red",
    font='Arial',
    alignText='right',  # Right-align to push text toward the left
    wrapWidth=experimenter_bar_width * 0.3  # Reduced wrap width
)
separator_text = visual.TextStim(
    win, 
    text="|", 
    pos=(experimenter_section_center_x, 0.1),  # Keep centered
    height=0.03,  # Match the other text height
    color="white",
    font='Arial',
    alignText='center'
)
correct_instruction = visual.TextStim(
    win, 
    text="Correct →", 
    pos=(experimenter_section_center_x + 0.08, 0.1),  # Moved further right
    height=0.03,  # Keep the smaller text size
    color="green",
    font='Arial',
    alignText='left',  # Left-align to push text toward the right
    wrapWidth=experimenter_bar_width * 0.3  # Reduced wrap width
)

# Feedback text for the experimenter side (right 1/3) - MOVED LOWER
text_exp_feedback = visual.TextStim(
    win, 
    text="", 
    pos=(experimenter_section_center_x, -0.15), # Moved lower from 0.0 to -0.15
    height=0.07, 
    color="white",
    font='Arial'
)
# Feedback text for the participant side (left 2/3)
text_subject_feedback = visual.TextStim(
    win, 
    text="", 
    pos=(subject_section_center_x, 0.0), 
    height=0.07, 
    color="white",
    font='Arial'
)
# Subject progress bar (left 2/3 section)
subject_progress_bar = visual.Rect(
    win, 
    width=subject_bar_width, 
    height=0.05, 
    fillColor="white", 
    lineColor=None, 
    pos=(subject_section_center_x, -0.4)
)
# Experimenter progress bar (right 1/3 section)
experimenter_progress_bar = visual.Rect(
    win, 
    width=experimenter_bar_width, 
    height=0.05, 
    fillColor="white", 
    lineColor=None, 
    pos=(experimenter_section_center_x, -0.4)
)
# Time text for subject section
time_left_text = visual.TextStim(
    win, 
    text="Time left:", 
    pos=(subject_section_center_x - (subject_bar_width / 2) - 0.05, -0.45), 
    height=0.04, 
    color="white", 
    alignHoriz='left',
    font='Arial'
)
time_value_text = visual.TextStim(
    win,
    text="",
    pos=(subject_section_center_x - (subject_bar_width / 2) + 0.15, -0.45),
    height=0.04,
    color="white",
    alignHoriz='left',
    font='Arial'
)
# Time text for experimenter section
time_left_text_exp = visual.TextStim(
    win, 
    text="Time left:", 
    pos=(experimenter_section_center_x - (experimenter_bar_width / 2) - 0.05, -0.45), 
    height=0.04, 
    color="white", 
    alignHoriz='left',
    font='Arial'
)
time_value_text_exp = visual.TextStim(
    win,
    text="",
    pos=(experimenter_section_center_x - (experimenter_bar_width / 2) + 0.15, -0.45),
    height=0.04,
    color="white",
    alignHoriz='left',
    font='Arial'
)

# --- Main Experiment Loop ---
#print("DEBUG: Starting main experiment loop for serial subtraction task.")
#print(f"DEBUG: At start of task, wrong_sound object: {wrong_sound}, Path: {getattr(wrong_sound, 'fileName', 'N/A') if wrong_sound else 'None'}")

while True:
    current_loop_time = clock.getTime()
    time_elapsed = current_loop_time - start_time
    time_left = duration - time_elapsed
    
    if time_left <= 0:
        time_left = 0 
        break 
    
    trial_start_time = current_loop_time
    
    if 'outlet' in globals() and outlet is not None:
        win.callOnFlip(outlet.push_sample, x=[40])
    
    correct_answer_for_this_trial = current_number - 13
    
    text_4digit_subject.text = str(current_number)
    text_correct_answer_exp.text = f"Ans: {correct_answer_for_this_trial}"
    text_exp_feedback.text = ""
    text_subject_feedback.text = ""  # Clear participant feedback text

    # Calculate progress ratio and update progress bars
    progress_ratio = time_left / duration
    
    # Update subject progress bar
    subject_current_bar_width = progress_ratio * subject_bar_width
    subject_progress_bar.width = subject_current_bar_width
    subject_progress_bar.pos = (
        subject_section_center_x - (subject_bar_width / 2) + (subject_current_bar_width / 2),
        -0.4
    )
    
    # Update experimenter progress bar
    experimenter_current_bar_width = progress_ratio * experimenter_bar_width
    experimenter_progress_bar.width = experimenter_current_bar_width
    experimenter_progress_bar.pos = (
        experimenter_section_center_x - (experimenter_bar_width / 2) + (experimenter_current_bar_width / 2),
        -0.4
    )
    
    # Update time text
    time_value_text.text = f"{max(0, int(time_left))}s"
    time_value_text_exp.text = f"{max(0, int(time_left))}s"

    responded_this_trial = False
    experimenter_response_key = None
    event.clearEvents()

    while not responded_this_trial:
        current_input_phase_time = clock.getTime()
        time_elapsed_input = current_input_phase_time - start_time
        time_left_input = duration - time_elapsed_input

        if time_left_input <= 0:
            time_left = 0
            break 

        # Calculate progress ratio for this frame
        progress_ratio_input = time_left_input / duration
        
        # Update subject progress bar
        subject_current_bar_width_input = progress_ratio_input * subject_bar_width
        subject_progress_bar.width = subject_current_bar_width_input
        subject_progress_bar.pos = (
            subject_section_center_x - (subject_bar_width / 2) + (subject_current_bar_width_input / 2),
            -0.4
        )
        
        # Update experimenter progress bar
        experimenter_current_bar_width_input = progress_ratio_input * experimenter_bar_width
        experimenter_progress_bar.width = experimenter_current_bar_width_input
        experimenter_progress_bar.pos = (
            experimenter_section_center_x - (experimenter_bar_width / 2) + (experimenter_current_bar_width_input / 2),
            -0.4
        )
        
        # Update time text for both sections
        time_value_text.text = f"{max(0, int(time_left_input))}s"
        time_value_text_exp.text = f"{max(0, int(time_left_input))}s"

        # Draw all components
        text_4digit_subject.draw()
        text_instruction_subject.draw()
        
        # Show speed warnings if needed
        if last_response_time >= 3: 
            speed_warning_subject.draw()
            speed_warning_experimenter.draw()  # Draw the experimenter speed warning
        
        text_correct_answer_exp.draw()
        
        # Draw the permanent instruction text
        wrong_instruction.draw()
        separator_text.draw()
        correct_instruction.draw()
        
        divider_line.draw()
        subject_progress_bar.draw()
        experimenter_progress_bar.draw()
        time_left_text.draw()
        time_value_text.draw()
        time_left_text_exp.draw()
        time_value_text_exp.draw()
        
        win.flip()

        keys = event.getKeys(keyList=['left', 'right', 'escape'])
        if 'escape' in keys:
            if 'outlet' in globals() and outlet is not None: 
                outlet.push_sample(x=[2])
            core.quit()
        
        if 'left' in keys:
            experimenter_response_key = 'left'
            responded_this_trial = True
        elif 'right' in keys:
            experimenter_response_key = 'right'
            responded_this_trial = True
        
        if time_left <= 0: break 

    if time_left <= 0: break 
    response_time_for_this_trial = clock.getTime() - trial_start_time

    # TODO: PPX
    # When playing the sound (in your left key press handler):
    if experimenter_response_key == 'left':
        # Update feedback for both sections
        text_exp_feedback.text = "WRONG"
        text_exp_feedback.color = "red"
        text_subject_feedback.text = "WRONG"
        text_subject_feedback.color = "red"
        
        # Play wrong sound - with better error handling
        if 'wrong_sound' in globals():
            try:
                wrong_sound.play()
            except Exception as e:
                print(f"Error playing wrong sound: {e}")

    elif experimenter_response_key == 'right':
        # Update feedback for both sections
        text_exp_feedback.text = "CORRECT"
        text_exp_feedback.color = "green"
        text_subject_feedback.text = "CORRECT"
        text_subject_feedback.color = "green"
    
    # Draw final state of this trial
    text_4digit_subject.draw() 
    text_instruction_subject.draw()
    if last_response_time >= 3: 
        speed_warning_subject.draw()
        speed_warning_experimenter.draw()  # Draw experimenter speed warning
        
    text_correct_answer_exp.draw()
    
    # Draw the permanent instruction text
    wrong_instruction.draw()
    separator_text.draw()
    correct_instruction.draw()
    
    text_exp_feedback.draw() 
    text_subject_feedback.draw()  # Draw participant feedback
    
    divider_line.draw()
    subject_progress_bar.draw() 
    experimenter_progress_bar.draw()
    time_left_text.draw()
    time_value_text.draw()
    time_left_text_exp.draw()
    time_value_text_exp.draw()
    
    win.flip()
    core.wait(0.75) 

    current_number = correct_answer_for_this_trial
    last_response_time = response_time_for_this_trial

# --- End of Experiment ---
if 'outlet' in globals() and outlet is not None:
    outlet.push_sample(x=[2])

# Add debugging info
#print("DEBUG: Arithmetic trial ending")
#print(f"DEBUG: Local sound module type: {type(sound)}")

# Just clear the screen and wait - no sound cleanup needed with pygame backend
win.flip()
core.wait(2.0)
