7.2472 	WARNING 	User requested fullscreen with size [1536  864], but screen is actually [1920, 1080]. Using actual size
7.8848 	EXP 	Created window1 = Window(allowGUI=True, allowStencil=True, autoLog=True, backendConf=UNKNOWN, backgroundFit=<method-wrapper '__getattribute__' of attributeSetter object at 0x0000022FBD5CBC70>, backgroundImage=<method-wrapper '__getattribute__' of attributeSetter object at 0x0000022FBD5CBC10>, bitsMode=UNKNOWN, blendMode='avg', bpc=(8, 8, 8), color=array([0, 0, 0]), colorSpace='rgb', depthBits=8, fullscr=<method-wrapper '__getattribute__' of attributeSetter object at 0x0000022FBD5CBA60>, gamma=None, gammaErrorPolicy='raise', lms=UNKNOWN, monitor=<psychopy.monitors.calibTools.Monitor object at 0x0000022FC86E2640>, multiSample=False, name='window1', numSamples=2, pos=(0, 0), screen=0, size=array([1920, 1080]), stencilBits=8, stereo=False, title='PsychoPy', units='height', useFBO=True, useRetina=False, viewOri=0.0, viewPos=None, viewScale=None, waitBlanking=True, winType='pyglet')
7.8858 	EXP 	window1: mouseVisible = True
7.8858 	EXP 	window1: backgroundImage = ''
7.8858 	EXP 	window1: backgroundFit = 'none'
7.8872 	EXP 	window1: Attempting to measure frame rate of screen (0) ...
7.8873 	EXP 	window1: recordFrameIntervals = False
8.0509 	EXP 	window1: recordFrameIntervals = True
8.2351 	EXP 	Screen (0) actual frame rate measured at 59.75Hz
8.2352 	EXP 	window1: recordFrameIntervals = False
8.2354 	EXP 	window1: mouseVisible = False
10.6328 	EXP 	01_resting_state: status = STARTED
10.7954 	EXP 	window1: mouseVisible = True
10.8592 	EXP 	Created text = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.8594 	EXP 	window1: mouseVisible = True
10.8651 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
10.8652 	EXP 	window1: mouseVisible = True
10.8790 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.8812 	EXP 	Sound exp_end_bip set volume 1.000
10.8814 	EXP 	window1: mouseVisible = True
10.8912 	EXP 	Created text_2 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_2', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.8985 	EXP 	window1: mouseVisible = True
10.9283 	EXP 	window1: mouseVisible = True
10.9360 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.9381 	EXP 	Sound exp_end_bip set volume 1.000
10.9383 	EXP 	window1: mouseVisible = True
10.9468 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.9470 	EXP 	window1: mouseVisible = True
10.9545 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.9566 	EXP 	Sound exp_end_bip set volume 1.000
10.9568 	EXP 	window1: mouseVisible = True
10.9646 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.9666 	EXP 	Sound exp_end_bip set volume 1.000
10.9668 	EXP 	window1: mouseVisible = True
10.9721 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.9723 	EXP 	window1: mouseVisible = True
10.9797 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.9817 	EXP 	Sound exp_end_bip set volume 1.000
10.9819 	EXP 	window1: mouseVisible = True
10.9980 	EXP 	Created stroop_instr = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='stroop_instr', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.9982 	EXP 	window1: mouseVisible = True
11.0115 	EXP 	Created instrText_4 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-2.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_4', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.0117 	EXP 	window1: mouseVisible = True
11.0134 	EXP 	Created stim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.15, italic=False, languageStyle='LTR', name='stim', opacity=1.0, ori=0, pos=array([0., 0.]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
11.0137 	EXP 	window1: mouseVisible = True
11.0139 	EXP 	window1: mouseVisible = True
11.0143 	EXP 	window1: mouseVisible = True
11.0148 	EXP 	window1: mouseVisible = True
11.0197 	EXP 	window1: mouseVisible = True
11.0199 	EXP 	window1: mouseVisible = True
11.0204 	EXP 	window1: mouseVisible = True
11.0208 	EXP 	window1: mouseVisible = True
11.0565 	EXP 	window1: mouseVisible = True
11.0616 	EXP 	Created instrText_2 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-4.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_2', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.0620 	EXP 	window1: mouseVisible = True
11.0621 	EXP 	window1: mouseVisible = True
11.0627 	EXP 	window1: mouseVisible = True
11.0634 	EXP 	window1: mouseVisible = True
11.0672 	EXP 	window1: mouseVisible = True
11.0673 	EXP 	window1: mouseVisible = True
11.0678 	EXP 	window1: mouseVisible = True
11.0681 	EXP 	window1: mouseVisible = True
11.0717 	EXP 	window1: mouseVisible = True
11.0719 	EXP 	window1: mouseVisible = True
11.0723 	EXP 	window1: mouseVisible = True
11.0727 	EXP 	window1: mouseVisible = True
11.0762 	EXP 	window1: mouseVisible = True
11.0763 	EXP 	window1: mouseVisible = True
11.0772 	EXP 	window1: mouseVisible = True
11.0776 	EXP 	window1: mouseVisible = True
11.0813 	EXP 	window1: mouseVisible = True
11.0856 	EXP 	Created instrText_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-3.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_3', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.0857 	EXP 	window1: mouseVisible = True
11.1012 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.1031 	EXP 	Sound exp_end_bip set volume 1.000
11.1033 	EXP 	window1: mouseVisible = True
11.1109 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.1111 	EXP 	window1: mouseVisible = True
11.1183 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.1202 	EXP 	Sound exp_end_bip set volume 1.000
11.1204 	EXP 	window1: mouseVisible = True
11.1287 	EXP 	Created text = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.1290 	EXP 	window1: mouseVisible = True
11.1337 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
11.1340 	EXP 	window1: mouseVisible = True
11.1494 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.1516 	EXP 	Sound exp_end_bip set volume 1.000
11.1518 	EXP 	window1: mouseVisible = True
11.1605 	EXP 	Created EndMessage = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='EndMessage', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='Thank you!', units='height', win=Window(...), wrapWidth=1)
0.0046 	EXP 	video: autoLog = True
0.0046 	EXP 	trial_counter: autoLog = True
0.0046 	EXP 	fbtxt: autoLog = True
0.0046 	EXP 	trial_counter_2: autoLog = True
0.0204 	EXP 	text: autoDraw = True
1.8716 	DATA 	Keypress: f1
1.8858 	EXP 	text: autoDraw = False
1.8858 	EXP 	cross: autoDraw = True
12.8932 	EXP 	Sound exp_end_bip started
12.8937 	EXP 	cross: autoDraw = False
12.8937 	EXP 	cross: autoDraw = False
12.8937 	EXP 	text_q_stress: autoDraw = True
13.6992 	EXP 	Sound exp_end_bip stopped
13.6992 	EXP 	Sound exp_end_bip paused
13.7013 	DATA 	Keypress: f1
13.7052 	EXP 	text_q_stress: autoDraw = False
13.7052 	EXP 	text_2: autoDraw = True
14.3282 	DATA 	Mouse: Left button down, pos=(783,641)
14.3947 	DATA 	Mouse:  Left button up, pos=(783,641)
16.3363 	DATA 	Keypress: f1
16.3445 	EXP 	text_2: autoDraw = False
16.3445 	EXP 	video: autoDraw = True
18.6061 	EXP 	Sound exp_end_bip reached end of file
18.6219 	EXP 	Sound exp_end_bip started
18.6127 	EXP 	video: autoDraw = False
18.6127 	EXP 	video: autoDraw = False
18.6127 	EXP 	text_q_stress: autoDraw = True
19.7787 	EXP 	Sound exp_end_bip stopped
19.7787 	EXP 	Sound exp_end_bip paused
19.7807 	DATA 	Keypress: f1
19.7848 	EXP 	text_q_stress: autoDraw = False
19.7848 	EXP 	text_3: autoDraw = True
20.4550 	EXP 	window1: mouseVisible = True
20.4568 	EXP 	Created unnamed ShapeStim = ShapeStim(__class__=<class 'psychopy.visual.shape.ShapeStim'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=False, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0, 0, 0]), fillColorSpace='rgb', fillRGB=array([0, 0, 0]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=3, name='unnamed ShapeStim', opacity=0.35, ori=0.0, pos=array([0., 0.]), size=array([1., 1.]), units='height', vertices=ndarray(...), win=Window(...), windingRule=None)
20.4600 	EXP 	window1: mouseVisible = True
20.4636 	EXP 	Created unnamed Circle = Circle(__class__=<class 'psychopy.visual.circle.Circle'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=2, name='unnamed Circle', opacity=1.0, ori=0.0, pos=array([0., 0.]), size=array([1., 1.]), units='height', vertices=ndarray(...), win=Window(...))
20.4638 	EXP 	window1: mouseVisible = True
20.4648 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.05882353, 0.61568627, 0.84313725]), fillColorSpace='rgb', fillRGB=array([0.05882353, 0.61568627, 0.84313725]), interpolate=True, lineColor=array([0., 0., 0.]), lineColorSpace='rgb', lineRGB=array([0, 0, 0]), lineWidth=1.5, name='unnamed Rect', opacity=0.1, ori=0.0, pos=array([0., 0.]), size=array([1.77777778, 0.5       ]), units='height', vertices=ndarray(...), win=Window(...))
20.4649 	EXP 	window1: mouseVisible = True
20.4720 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([ 0.  , -0.35]), rgb=UNKNOWN, text='Inhale', units='height', win=Window(...), wrapWidth=1)
20.4721 	EXP 	window1: mouseVisible = True
20.4730 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([0. , 0.4]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
20.4796 	DATA 	Keypress: f1
20.4834 	EXP 	text_3: autoDraw = False
20.4834 	EXP 	unnamed TextStim: text = 'Inhale'
20.4834 	EXP 	unnamed TextStim: height = 0.03
20.4834 	EXP 	unnamed TextStim: height = 0.03
20.4834 	EXP 	unnamed TextStim: height = 0.03
20.4834 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.5040 	EXP 	unnamed TextStim: text = 'Inhale'
20.5040 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.5202 	EXP 	unnamed TextStim: text = 'Inhale'
20.5202 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.5374 	EXP 	unnamed TextStim: text = 'Inhale'
20.5374 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.5539 	EXP 	unnamed TextStim: text = 'Inhale'
20.5539 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.5720 	EXP 	unnamed TextStim: text = 'Inhale'
20.5720 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.5873 	EXP 	unnamed TextStim: text = 'Inhale'
20.5873 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.6037 	EXP 	unnamed TextStim: text = 'Inhale'
20.6037 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.6203 	EXP 	unnamed TextStim: text = 'Inhale'
20.6203 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.6372 	EXP 	unnamed TextStim: text = 'Inhale'
20.6372 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.6537 	EXP 	unnamed TextStim: text = 'Inhale'
20.6537 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.6705 	EXP 	unnamed TextStim: text = 'Inhale'
20.6705 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.6874 	EXP 	unnamed TextStim: text = 'Inhale'
20.6874 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.7039 	EXP 	unnamed TextStim: text = 'Inhale'
20.7039 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.7205 	EXP 	unnamed TextStim: text = 'Inhale'
20.7205 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.7369 	EXP 	unnamed TextStim: text = 'Inhale'
20.7369 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.7535 	EXP 	unnamed TextStim: text = 'Inhale'
20.7535 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.7702 	EXP 	unnamed TextStim: text = 'Inhale'
20.7702 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.7872 	EXP 	unnamed TextStim: text = 'Inhale'
20.7872 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.8032 	EXP 	unnamed TextStim: text = 'Inhale'
20.8032 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.8202 	EXP 	unnamed TextStim: text = 'Inhale'
20.8202 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.8367 	EXP 	unnamed TextStim: text = 'Inhale'
20.8367 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.8538 	EXP 	unnamed TextStim: text = 'Inhale'
20.8538 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.8703 	EXP 	unnamed TextStim: text = 'Inhale'
20.8703 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.8869 	EXP 	unnamed TextStim: text = 'Inhale'
20.8869 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.9036 	EXP 	unnamed TextStim: text = 'Inhale'
20.9036 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.9205 	EXP 	unnamed TextStim: text = 'Inhale'
20.9205 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.9370 	EXP 	unnamed TextStim: text = 'Inhale'
20.9370 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.9536 	EXP 	unnamed TextStim: text = 'Inhale'
20.9536 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.9709 	EXP 	unnamed TextStim: text = 'Inhale'
20.9709 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
20.9876 	EXP 	unnamed TextStim: text = 'Inhale'
20.9876 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.0034 	EXP 	unnamed TextStim: text = 'Inhale'
21.0034 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.0203 	EXP 	unnamed TextStim: text = 'Inhale'
21.0203 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.0364 	EXP 	unnamed TextStim: text = 'Inhale'
21.0364 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.0535 	EXP 	unnamed TextStim: text = 'Inhale'
21.0535 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.0701 	EXP 	unnamed TextStim: text = 'Inhale'
21.0701 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.0869 	EXP 	unnamed TextStim: text = 'Inhale'
21.0869 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.1033 	EXP 	unnamed TextStim: text = 'Inhale'
21.1033 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.1199 	EXP 	unnamed TextStim: text = 'Inhale'
21.1199 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.1384 	EXP 	unnamed TextStim: text = 'Inhale'
21.1384 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.1538 	EXP 	unnamed TextStim: text = 'Inhale'
21.1538 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.1699 	EXP 	unnamed TextStim: text = 'Inhale'
21.1699 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.1868 	EXP 	unnamed TextStim: text = 'Inhale'
21.1868 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.2037 	EXP 	unnamed TextStim: text = 'Inhale'
21.2037 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.2200 	EXP 	unnamed TextStim: text = 'Inhale'
21.2200 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.2365 	EXP 	unnamed TextStim: text = 'Inhale'
21.2365 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.2532 	EXP 	unnamed TextStim: text = 'Inhale'
21.2532 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.2700 	EXP 	unnamed TextStim: text = 'Inhale'
21.2700 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.2868 	EXP 	unnamed TextStim: text = 'Inhale'
21.2868 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.3034 	EXP 	unnamed TextStim: text = 'Inhale'
21.3034 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.3197 	EXP 	unnamed TextStim: text = 'Inhale'
21.3197 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.3362 	EXP 	unnamed TextStim: text = 'Inhale'
21.3362 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.3528 	EXP 	unnamed TextStim: text = 'Inhale'
21.3528 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.3697 	EXP 	unnamed TextStim: text = 'Inhale'
21.3697 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.3862 	EXP 	unnamed TextStim: text = 'Inhale'
21.3862 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.4026 	EXP 	unnamed TextStim: text = 'Inhale'
21.4026 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.4201 	EXP 	unnamed TextStim: text = 'Inhale'
21.4201 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.4364 	EXP 	unnamed TextStim: text = 'Inhale'
21.4364 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.4531 	EXP 	unnamed TextStim: text = 'Inhale'
21.4531 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.4697 	EXP 	unnamed TextStim: text = 'Inhale'
21.4697 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.4864 	EXP 	unnamed TextStim: text = 'Inhale'
21.4864 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
21.5028 	EXP 	unnamed TextStim: text = 'Inhale'
21.5028 	EXP 	unnamed TextStim: height = 0.03
21.5028 	EXP 	unnamed TextStim: height = 0.03
21.5028 	EXP 	unnamed TextStim: height = 0.03
21.5028 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.5195 	EXP 	unnamed TextStim: text = 'Inhale'
21.5195 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.5361 	EXP 	unnamed TextStim: text = 'Inhale'
21.5361 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.5527 	EXP 	unnamed TextStim: text = 'Inhale'
21.5527 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.5694 	EXP 	unnamed TextStim: text = 'Inhale'
21.5694 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.5859 	EXP 	unnamed TextStim: text = 'Inhale'
21.5859 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.6027 	EXP 	unnamed TextStim: text = 'Inhale'
21.6027 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.6197 	EXP 	unnamed TextStim: text = 'Inhale'
21.6197 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.6363 	EXP 	unnamed TextStim: text = 'Inhale'
21.6363 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.6528 	EXP 	unnamed TextStim: text = 'Inhale'
21.6528 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.6699 	EXP 	unnamed TextStim: text = 'Inhale'
21.6699 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.6864 	EXP 	unnamed TextStim: text = 'Inhale'
21.6864 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.7035 	EXP 	unnamed TextStim: text = 'Inhale'
21.7035 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.7199 	EXP 	unnamed TextStim: text = 'Inhale'
21.7199 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.7358 	EXP 	unnamed TextStim: text = 'Inhale'
21.7358 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.7525 	EXP 	unnamed TextStim: text = 'Inhale'
21.7525 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.7697 	EXP 	unnamed TextStim: text = 'Inhale'
21.7697 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.7860 	EXP 	unnamed TextStim: text = 'Inhale'
21.7860 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.8023 	EXP 	unnamed TextStim: text = 'Inhale'
21.8023 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.8194 	EXP 	unnamed TextStim: text = 'Inhale'
21.8194 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.8359 	EXP 	unnamed TextStim: text = 'Inhale'
21.8359 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.8526 	EXP 	unnamed TextStim: text = 'Inhale'
21.8526 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.8694 	EXP 	unnamed TextStim: text = 'Inhale'
21.8694 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.8857 	EXP 	unnamed TextStim: text = 'Inhale'
21.8857 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.9025 	EXP 	unnamed TextStim: text = 'Inhale'
21.9025 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.9196 	EXP 	unnamed TextStim: text = 'Inhale'
21.9196 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.9361 	EXP 	unnamed TextStim: text = 'Inhale'
21.9361 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.9525 	EXP 	unnamed TextStim: text = 'Inhale'
21.9525 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.9697 	EXP 	unnamed TextStim: text = 'Inhale'
21.9697 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
21.9858 	EXP 	unnamed TextStim: text = 'Inhale'
21.9858 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.0028 	EXP 	unnamed TextStim: text = 'Inhale'
22.0028 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.0194 	EXP 	unnamed TextStim: text = 'Inhale'
22.0194 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.0362 	EXP 	unnamed TextStim: text = 'Inhale'
22.0362 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.0522 	EXP 	unnamed TextStim: text = 'Inhale'
22.0522 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.0690 	EXP 	unnamed TextStim: text = 'Inhale'
22.0690 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.0861 	EXP 	unnamed TextStim: text = 'Inhale'
22.0861 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.1024 	EXP 	unnamed TextStim: text = 'Inhale'
22.1024 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.1193 	EXP 	unnamed TextStim: text = 'Inhale'
22.1193 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.1359 	EXP 	unnamed TextStim: text = 'Inhale'
22.1359 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.1530 	EXP 	unnamed TextStim: text = 'Inhale'
22.1530 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.1699 	EXP 	unnamed TextStim: text = 'Inhale'
22.1699 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.1864 	EXP 	unnamed TextStim: text = 'Inhale'
22.1864 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.2023 	EXP 	unnamed TextStim: text = 'Inhale'
22.2023 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.2193 	EXP 	unnamed TextStim: text = 'Inhale'
22.2193 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.2359 	EXP 	unnamed TextStim: text = 'Inhale'
22.2359 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.2540 	EXP 	unnamed TextStim: text = 'Inhale'
22.2540 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.2717 	EXP 	unnamed TextStim: text = 'Inhale'
22.2717 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.2860 	EXP 	unnamed TextStim: text = 'Inhale'
22.2860 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.3027 	EXP 	unnamed TextStim: text = 'Inhale'
22.3027 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.3195 	EXP 	unnamed TextStim: text = 'Inhale'
22.3195 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.3359 	EXP 	unnamed TextStim: text = 'Inhale'
22.3359 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.3525 	EXP 	unnamed TextStim: text = 'Inhale'
22.3525 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.3691 	EXP 	unnamed TextStim: text = 'Inhale'
22.3691 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.3858 	EXP 	unnamed TextStim: text = 'Inhale'
22.3858 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.4026 	EXP 	unnamed TextStim: text = 'Inhale'
22.4026 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.4192 	EXP 	unnamed TextStim: text = 'Inhale'
22.4192 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.4354 	EXP 	unnamed TextStim: text = 'Inhale'
22.4354 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.4520 	EXP 	unnamed TextStim: text = 'Inhale'
22.4520 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.4689 	EXP 	unnamed TextStim: text = 'Inhale'
22.4689 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.4854 	EXP 	unnamed TextStim: text = 'Inhale'
22.4854 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
22.5024 	EXP 	unnamed TextStim: text = 'Inhale'
22.5024 	EXP 	unnamed TextStim: height = 0.03
22.5024 	EXP 	unnamed TextStim: height = 0.03
22.5024 	EXP 	unnamed TextStim: height = 0.03
22.5024 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.5192 	EXP 	unnamed TextStim: text = 'Inhale'
22.5192 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.5355 	EXP 	unnamed TextStim: text = 'Inhale'
22.5355 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.5523 	EXP 	unnamed TextStim: text = 'Inhale'
22.5523 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.5689 	EXP 	unnamed TextStim: text = 'Inhale'
22.5689 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.5855 	EXP 	unnamed TextStim: text = 'Inhale'
22.5855 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.6023 	EXP 	unnamed TextStim: text = 'Inhale'
22.6023 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.6194 	EXP 	unnamed TextStim: text = 'Inhale'
22.6194 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.6351 	EXP 	unnamed TextStim: text = 'Inhale'
22.6351 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.6515 	EXP 	unnamed TextStim: text = 'Inhale'
22.6515 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.6688 	EXP 	unnamed TextStim: text = 'Inhale'
22.6688 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.6853 	EXP 	unnamed TextStim: text = 'Inhale'
22.6853 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.7019 	EXP 	unnamed TextStim: text = 'Inhale'
22.7019 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.7188 	EXP 	unnamed TextStim: text = 'Inhale'
22.7188 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.7347 	EXP 	unnamed TextStim: text = 'Inhale'
22.7347 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.7516 	EXP 	unnamed TextStim: text = 'Inhale'
22.7516 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.7691 	EXP 	unnamed TextStim: text = 'Inhale'
22.7691 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.7865 	EXP 	unnamed TextStim: text = 'Inhale'
22.7865 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.8018 	EXP 	unnamed TextStim: text = 'Inhale'
22.8018 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.8187 	EXP 	unnamed TextStim: text = 'Inhale'
22.8187 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.8353 	EXP 	unnamed TextStim: text = 'Inhale'
22.8353 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.8519 	EXP 	unnamed TextStim: text = 'Inhale'
22.8519 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.8691 	EXP 	unnamed TextStim: text = 'Inhale'
22.8691 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.8854 	EXP 	unnamed TextStim: text = 'Inhale'
22.8854 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.9020 	EXP 	unnamed TextStim: text = 'Inhale'
22.9020 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.9195 	EXP 	unnamed TextStim: text = 'Inhale'
22.9195 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.9351 	EXP 	unnamed TextStim: text = 'Inhale'
22.9351 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.9515 	EXP 	unnamed TextStim: text = 'Inhale'
22.9515 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.9684 	EXP 	unnamed TextStim: text = 'Inhale'
22.9684 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
22.9848 	EXP 	unnamed TextStim: text = 'Inhale'
22.9848 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.0020 	EXP 	unnamed TextStim: text = 'Inhale'
23.0020 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.0186 	EXP 	unnamed TextStim: text = 'Inhale'
23.0186 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.0348 	EXP 	unnamed TextStim: text = 'Inhale'
23.0348 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.0514 	EXP 	unnamed TextStim: text = 'Inhale'
23.0514 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.0683 	EXP 	unnamed TextStim: text = 'Inhale'
23.0683 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.0853 	EXP 	unnamed TextStim: text = 'Inhale'
23.0853 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.1020 	EXP 	unnamed TextStim: text = 'Inhale'
23.1020 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.1191 	EXP 	unnamed TextStim: text = 'Inhale'
23.1191 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.1350 	EXP 	unnamed TextStim: text = 'Inhale'
23.1350 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.1520 	EXP 	unnamed TextStim: text = 'Inhale'
23.1520 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.1687 	EXP 	unnamed TextStim: text = 'Inhale'
23.1687 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.1852 	EXP 	unnamed TextStim: text = 'Inhale'
23.1852 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.2015 	EXP 	unnamed TextStim: text = 'Inhale'
23.2015 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.2183 	EXP 	unnamed TextStim: text = 'Inhale'
23.2183 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.2346 	EXP 	unnamed TextStim: text = 'Inhale'
23.2346 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.2513 	EXP 	unnamed TextStim: text = 'Inhale'
23.2513 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.2688 	EXP 	unnamed TextStim: text = 'Inhale'
23.2688 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.2854 	EXP 	unnamed TextStim: text = 'Inhale'
23.2854 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.3015 	EXP 	unnamed TextStim: text = 'Inhale'
23.3015 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.3191 	EXP 	unnamed TextStim: text = 'Inhale'
23.3191 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.3373 	EXP 	unnamed TextStim: text = 'Inhale'
23.3373 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.3518 	EXP 	unnamed TextStim: text = 'Inhale'
23.3518 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.3687 	EXP 	unnamed TextStim: text = 'Inhale'
23.3687 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.3853 	EXP 	unnamed TextStim: text = 'Inhale'
23.3853 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.4015 	EXP 	unnamed TextStim: text = 'Inhale'
23.4015 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.4181 	EXP 	unnamed TextStim: text = 'Inhale'
23.4181 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.4346 	EXP 	unnamed TextStim: text = 'Inhale'
23.4346 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.4513 	EXP 	unnamed TextStim: text = 'Inhale'
23.4513 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.4687 	EXP 	unnamed TextStim: text = 'Inhale'
23.4687 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.4849 	EXP 	unnamed TextStim: text = 'Inhale'
23.4849 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
23.5015 	EXP 	unnamed TextStim: text = 'Inhale'
23.5015 	EXP 	unnamed TextStim: height = 0.03
23.5015 	EXP 	unnamed TextStim: height = 0.03
23.5015 	EXP 	unnamed TextStim: height = 0.03
23.5015 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.5186 	EXP 	unnamed TextStim: text = 'Inhale'
23.5186 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.5350 	EXP 	unnamed TextStim: text = 'Inhale'
23.5350 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.5514 	EXP 	unnamed TextStim: text = 'Inhale'
23.5514 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.5681 	EXP 	unnamed TextStim: text = 'Inhale'
23.5681 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.5847 	EXP 	unnamed TextStim: text = 'Inhale'
23.5847 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.6015 	EXP 	unnamed TextStim: text = 'Inhale'
23.6015 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.6182 	EXP 	unnamed TextStim: text = 'Inhale'
23.6182 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.6346 	EXP 	unnamed TextStim: text = 'Inhale'
23.6346 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.6514 	EXP 	unnamed TextStim: text = 'Inhale'
23.6514 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.6682 	EXP 	unnamed TextStim: text = 'Inhale'
23.6682 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.6846 	EXP 	unnamed TextStim: text = 'Inhale'
23.6846 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.7014 	EXP 	unnamed TextStim: text = 'Inhale'
23.7014 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.7185 	EXP 	unnamed TextStim: text = 'Inhale'
23.7185 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.7344 	EXP 	unnamed TextStim: text = 'Inhale'
23.7344 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.7512 	EXP 	unnamed TextStim: text = 'Inhale'
23.7512 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.7680 	EXP 	unnamed TextStim: text = 'Inhale'
23.7680 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.7845 	EXP 	unnamed TextStim: text = 'Inhale'
23.7845 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.8011 	EXP 	unnamed TextStim: text = 'Inhale'
23.8011 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.8179 	EXP 	unnamed TextStim: text = 'Inhale'
23.8179 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.8345 	EXP 	unnamed TextStim: text = 'Inhale'
23.8345 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.8514 	EXP 	unnamed TextStim: text = 'Inhale'
23.8514 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.8681 	EXP 	unnamed TextStim: text = 'Inhale'
23.8681 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.8845 	EXP 	unnamed TextStim: text = 'Inhale'
23.8845 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.9024 	EXP 	unnamed TextStim: text = 'Inhale'
23.9024 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.9181 	EXP 	unnamed TextStim: text = 'Inhale'
23.9181 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.9346 	EXP 	unnamed TextStim: text = 'Inhale'
23.9346 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.9512 	EXP 	unnamed TextStim: text = 'Inhale'
23.9512 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.9681 	EXP 	unnamed TextStim: text = 'Inhale'
23.9681 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
23.9848 	EXP 	unnamed TextStim: text = 'Inhale'
23.9848 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.0024 	EXP 	unnamed TextStim: text = 'Inhale'
24.0024 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.0181 	EXP 	unnamed TextStim: text = 'Inhale'
24.0181 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.0363 	EXP 	unnamed TextStim: text = 'Inhale'
24.0363 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.0520 	EXP 	unnamed TextStim: text = 'Inhale'
24.0520 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.0680 	EXP 	unnamed TextStim: text = 'Inhale'
24.0680 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.0843 	EXP 	unnamed TextStim: text = 'Inhale'
24.0843 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.1011 	EXP 	unnamed TextStim: text = 'Inhale'
24.1011 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.1177 	EXP 	unnamed TextStim: text = 'Inhale'
24.1177 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.1342 	EXP 	unnamed TextStim: text = 'Inhale'
24.1342 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.1512 	EXP 	unnamed TextStim: text = 'Inhale'
24.1512 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.1682 	EXP 	unnamed TextStim: text = 'Inhale'
24.1682 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.1844 	EXP 	unnamed TextStim: text = 'Inhale'
24.1844 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.2010 	EXP 	unnamed TextStim: text = 'Inhale'
24.2010 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.2178 	EXP 	unnamed TextStim: text = 'Inhale'
24.2178 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.2343 	EXP 	unnamed TextStim: text = 'Inhale'
24.2343 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.2504 	EXP 	unnamed TextStim: text = 'Inhale'
24.2504 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.2677 	EXP 	unnamed TextStim: text = 'Inhale'
24.2677 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.2842 	EXP 	unnamed TextStim: text = 'Inhale'
24.2842 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.3006 	EXP 	unnamed TextStim: text = 'Inhale'
24.3006 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.3180 	EXP 	unnamed TextStim: text = 'Inhale'
24.3180 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.3345 	EXP 	unnamed TextStim: text = 'Inhale'
24.3345 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.3508 	EXP 	unnamed TextStim: text = 'Inhale'
24.3508 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.3674 	EXP 	unnamed TextStim: text = 'Inhale'
24.3674 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.3847 	EXP 	unnamed TextStim: text = 'Inhale'
24.3847 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.4009 	EXP 	unnamed TextStim: text = 'Inhale'
24.4009 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.4174 	EXP 	unnamed TextStim: text = 'Inhale'
24.4174 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.4355 	EXP 	unnamed TextStim: text = 'Inhale'
24.4355 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.4507 	EXP 	unnamed TextStim: text = 'Inhale'
24.4507 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.4673 	EXP 	unnamed TextStim: text = 'Inhale'
24.4673 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.4842 	EXP 	unnamed TextStim: text = 'Inhale'
24.4842 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
24.5007 	EXP 	unnamed TextStim: text = 'Inhale'
24.5007 	EXP 	unnamed TextStim: height = 0.03
24.5007 	EXP 	unnamed TextStim: height = 0.03
24.5007 	EXP 	unnamed TextStim: height = 0.03
24.5007 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.5177 	EXP 	unnamed TextStim: text = 'Inhale'
24.5177 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.5335 	EXP 	unnamed TextStim: text = 'Inhale'
24.5335 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.5501 	EXP 	unnamed TextStim: text = 'Inhale'
24.5501 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.5676 	EXP 	unnamed TextStim: text = 'Inhale'
24.5676 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.5841 	EXP 	unnamed TextStim: text = 'Inhale'
24.5841 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.6006 	EXP 	unnamed TextStim: text = 'Inhale'
24.6006 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.6169 	EXP 	unnamed TextStim: text = 'Inhale'
24.6169 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.6341 	EXP 	unnamed TextStim: text = 'Inhale'
24.6341 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.6502 	EXP 	unnamed TextStim: text = 'Inhale'
24.6502 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.6677 	EXP 	unnamed TextStim: text = 'Inhale'
24.6677 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.6836 	EXP 	unnamed TextStim: text = 'Inhale'
24.6836 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.7002 	EXP 	unnamed TextStim: text = 'Inhale'
24.7002 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.7171 	EXP 	unnamed TextStim: text = 'Inhale'
24.7171 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.7332 	EXP 	unnamed TextStim: text = 'Inhale'
24.7332 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.7500 	EXP 	unnamed TextStim: text = 'Inhale'
24.7500 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.7670 	EXP 	unnamed TextStim: text = 'Inhale'
24.7670 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.7837 	EXP 	unnamed TextStim: text = 'Inhale'
24.7837 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.7999 	EXP 	unnamed TextStim: text = 'Inhale'
24.7999 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.8168 	EXP 	unnamed TextStim: text = 'Inhale'
24.8168 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.8337 	EXP 	unnamed TextStim: text = 'Inhale'
24.8337 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.8508 	EXP 	unnamed TextStim: text = 'Inhale'
24.8508 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.8680 	EXP 	unnamed TextStim: text = 'Inhale'
24.8680 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.8836 	EXP 	unnamed TextStim: text = 'Inhale'
24.8836 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.9002 	EXP 	unnamed TextStim: text = 'Inhale'
24.9002 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.9167 	EXP 	unnamed TextStim: text = 'Inhale'
24.9167 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.9336 	EXP 	unnamed TextStim: text = 'Inhale'
24.9336 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.9502 	EXP 	unnamed TextStim: text = 'Inhale'
24.9502 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.9669 	EXP 	unnamed TextStim: text = 'Inhale'
24.9669 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
24.9832 	EXP 	unnamed TextStim: text = 'Inhale'
24.9832 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.0015 	EXP 	unnamed TextStim: height = 0.05
25.0015 	EXP 	unnamed TextStim: height = 0.05
25.0015 	EXP 	unnamed TextStim: height = 0.05
25.0015 	EXP 	unnamed TextStim: text = 'Hold'
25.0015 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.0172 	EXP 	unnamed TextStim: text = 'Hold'
25.0172 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.0336 	EXP 	unnamed TextStim: text = 'Hold'
25.0336 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.0505 	EXP 	unnamed TextStim: text = 'Hold'
25.0505 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.0672 	EXP 	unnamed TextStim: text = 'Hold'
25.0672 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.0837 	EXP 	unnamed TextStim: text = 'Hold'
25.0837 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.1006 	EXP 	unnamed TextStim: text = 'Hold'
25.1006 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.1166 	EXP 	unnamed TextStim: text = 'Hold'
25.1166 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.1336 	EXP 	unnamed TextStim: text = 'Hold'
25.1336 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.1501 	EXP 	unnamed TextStim: text = 'Hold'
25.1501 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.1666 	EXP 	unnamed TextStim: text = 'Hold'
25.1666 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.1833 	EXP 	unnamed TextStim: text = 'Hold'
25.1833 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.2003 	EXP 	unnamed TextStim: text = 'Hold'
25.2003 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.2167 	EXP 	unnamed TextStim: text = 'Hold'
25.2167 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.2335 	EXP 	unnamed TextStim: text = 'Hold'
25.2335 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.2502 	EXP 	unnamed TextStim: text = 'Hold'
25.2502 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.2667 	EXP 	unnamed TextStim: text = 'Hold'
25.2667 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.2832 	EXP 	unnamed TextStim: text = 'Hold'
25.2832 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.3001 	EXP 	unnamed TextStim: text = 'Hold'
25.3001 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.3165 	EXP 	unnamed TextStim: text = 'Hold'
25.3165 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.3336 	EXP 	unnamed TextStim: text = 'Hold'
25.3336 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.3507 	EXP 	unnamed TextStim: text = 'Hold'
25.3507 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.3675 	EXP 	unnamed TextStim: text = 'Hold'
25.3675 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.3830 	EXP 	unnamed TextStim: text = 'Hold'
25.3830 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.3997 	EXP 	unnamed TextStim: text = 'Hold'
25.3997 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.4163 	EXP 	unnamed TextStim: text = 'Hold'
25.4163 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.4330 	EXP 	unnamed TextStim: text = 'Hold'
25.4330 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.4501 	EXP 	unnamed TextStim: text = 'Hold'
25.4501 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.4664 	EXP 	unnamed TextStim: text = 'Hold'
25.4664 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.4830 	EXP 	unnamed TextStim: text = 'Hold'
25.4830 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
25.5002 	EXP 	unnamed TextStim: height = 0.05
25.5002 	EXP 	unnamed TextStim: height = 0.05
25.5002 	EXP 	unnamed TextStim: height = 0.05
25.5002 	EXP 	unnamed TextStim: text = 'Exhale'
25.5002 	EXP 	unnamed TextStim: height = 0.03
25.5002 	EXP 	unnamed TextStim: height = 0.03
25.5002 	EXP 	unnamed TextStim: height = 0.03
25.5002 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.5163 	EXP 	unnamed TextStim: text = 'Exhale'
25.5163 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.5333 	EXP 	unnamed TextStim: text = 'Exhale'
25.5333 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.5516 	EXP 	unnamed TextStim: text = 'Exhale'
25.5516 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.5672 	EXP 	unnamed TextStim: text = 'Exhale'
25.5672 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.5833 	EXP 	unnamed TextStim: text = 'Exhale'
25.5833 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.6001 	EXP 	unnamed TextStim: text = 'Exhale'
25.6001 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.6166 	EXP 	unnamed TextStim: text = 'Exhale'
25.6166 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.6329 	EXP 	unnamed TextStim: text = 'Exhale'
25.6329 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.6497 	EXP 	unnamed TextStim: text = 'Exhale'
25.6497 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.6660 	EXP 	unnamed TextStim: text = 'Exhale'
25.6660 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.6827 	EXP 	unnamed TextStim: text = 'Exhale'
25.6827 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.7004 	EXP 	unnamed TextStim: text = 'Exhale'
25.7004 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.7164 	EXP 	unnamed TextStim: text = 'Exhale'
25.7164 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.7330 	EXP 	unnamed TextStim: text = 'Exhale'
25.7330 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.7500 	EXP 	unnamed TextStim: text = 'Exhale'
25.7500 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.7663 	EXP 	unnamed TextStim: text = 'Exhale'
25.7663 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.7831 	EXP 	unnamed TextStim: text = 'Exhale'
25.7831 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.8001 	EXP 	unnamed TextStim: text = 'Exhale'
25.8001 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.8163 	EXP 	unnamed TextStim: text = 'Exhale'
25.8163 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.8333 	EXP 	unnamed TextStim: text = 'Exhale'
25.8333 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.8508 	EXP 	unnamed TextStim: text = 'Exhale'
25.8508 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.8667 	EXP 	unnamed TextStim: text = 'Exhale'
25.8667 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.8828 	EXP 	unnamed TextStim: text = 'Exhale'
25.8828 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.8993 	EXP 	unnamed TextStim: text = 'Exhale'
25.8993 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.9157 	EXP 	unnamed TextStim: text = 'Exhale'
25.9157 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.9326 	EXP 	unnamed TextStim: text = 'Exhale'
25.9326 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.9492 	EXP 	unnamed TextStim: text = 'Exhale'
25.9492 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.9657 	EXP 	unnamed TextStim: text = 'Exhale'
25.9657 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.9823 	EXP 	unnamed TextStim: text = 'Exhale'
25.9823 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
25.9994 	EXP 	unnamed TextStim: text = 'Exhale'
25.9994 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.0162 	EXP 	unnamed TextStim: text = 'Exhale'
26.0162 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.0327 	EXP 	unnamed TextStim: text = 'Exhale'
26.0327 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.0499 	EXP 	unnamed TextStim: text = 'Exhale'
26.0499 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.0665 	EXP 	unnamed TextStim: text = 'Exhale'
26.0665 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.0831 	EXP 	unnamed TextStim: text = 'Exhale'
26.0831 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.1017 	EXP 	unnamed TextStim: text = 'Exhale'
26.1017 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.1163 	EXP 	unnamed TextStim: text = 'Exhale'
26.1163 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.1330 	EXP 	unnamed TextStim: text = 'Exhale'
26.1330 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.1493 	EXP 	unnamed TextStim: text = 'Exhale'
26.1493 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.1658 	EXP 	unnamed TextStim: text = 'Exhale'
26.1658 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.1820 	EXP 	unnamed TextStim: text = 'Exhale'
26.1820 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.1994 	EXP 	unnamed TextStim: text = 'Exhale'
26.1994 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.2161 	EXP 	unnamed TextStim: text = 'Exhale'
26.2161 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.2341 	EXP 	unnamed TextStim: text = 'Exhale'
26.2341 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.2492 	EXP 	unnamed TextStim: text = 'Exhale'
26.2492 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.2658 	EXP 	unnamed TextStim: text = 'Exhale'
26.2658 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.2825 	EXP 	unnamed TextStim: text = 'Exhale'
26.2825 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.2985 	EXP 	unnamed TextStim: text = 'Exhale'
26.2985 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.3158 	EXP 	unnamed TextStim: text = 'Exhale'
26.3158 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.3324 	EXP 	unnamed TextStim: text = 'Exhale'
26.3324 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.3496 	EXP 	unnamed TextStim: text = 'Exhale'
26.3496 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.3657 	EXP 	unnamed TextStim: text = 'Exhale'
26.3657 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.3826 	EXP 	unnamed TextStim: text = 'Exhale'
26.3826 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.3995 	EXP 	unnamed TextStim: text = 'Exhale'
26.3995 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.4157 	EXP 	unnamed TextStim: text = 'Exhale'
26.4157 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.4322 	EXP 	unnamed TextStim: text = 'Exhale'
26.4322 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.4494 	EXP 	unnamed TextStim: text = 'Exhale'
26.4494 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.4657 	EXP 	unnamed TextStim: text = 'Exhale'
26.4657 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.4822 	EXP 	unnamed TextStim: text = 'Exhale'
26.4822 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
26.4988 	EXP 	unnamed TextStim: text = 'Exhale'
26.4988 	EXP 	unnamed TextStim: height = 0.03
26.4988 	EXP 	unnamed TextStim: height = 0.03
26.4988 	EXP 	unnamed TextStim: height = 0.03
26.4988 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.5157 	EXP 	unnamed TextStim: text = 'Exhale'
26.5157 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.5322 	EXP 	unnamed TextStim: text = 'Exhale'
26.5322 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.5494 	EXP 	unnamed TextStim: text = 'Exhale'
26.5494 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.5657 	EXP 	unnamed TextStim: text = 'Exhale'
26.5657 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.5824 	EXP 	unnamed TextStim: text = 'Exhale'
26.5824 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.5995 	EXP 	unnamed TextStim: text = 'Exhale'
26.5995 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.6160 	EXP 	unnamed TextStim: text = 'Exhale'
26.6160 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.6320 	EXP 	unnamed TextStim: text = 'Exhale'
26.6320 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.6504 	EXP 	unnamed TextStim: text = 'Exhale'
26.6504 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.6656 	EXP 	unnamed TextStim: text = 'Exhale'
26.6656 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.6823 	EXP 	unnamed TextStim: text = 'Exhale'
26.6823 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.6991 	EXP 	unnamed TextStim: text = 'Exhale'
26.6991 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.7157 	EXP 	unnamed TextStim: text = 'Exhale'
26.7157 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.7321 	EXP 	unnamed TextStim: text = 'Exhale'
26.7321 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.7491 	EXP 	unnamed TextStim: text = 'Exhale'
26.7491 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.7656 	EXP 	unnamed TextStim: text = 'Exhale'
26.7656 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.7816 	EXP 	unnamed TextStim: text = 'Exhale'
26.7816 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.7991 	EXP 	unnamed TextStim: text = 'Exhale'
26.7991 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.8153 	EXP 	unnamed TextStim: text = 'Exhale'
26.8153 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.8322 	EXP 	unnamed TextStim: text = 'Exhale'
26.8322 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.8496 	EXP 	unnamed TextStim: text = 'Exhale'
26.8496 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.8658 	EXP 	unnamed TextStim: text = 'Exhale'
26.8658 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.8819 	EXP 	unnamed TextStim: text = 'Exhale'
26.8819 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.8988 	EXP 	unnamed TextStim: text = 'Exhale'
26.8988 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.9149 	EXP 	unnamed TextStim: text = 'Exhale'
26.9149 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.9318 	EXP 	unnamed TextStim: text = 'Exhale'
26.9318 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.9490 	EXP 	unnamed TextStim: text = 'Exhale'
26.9490 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.9651 	EXP 	unnamed TextStim: text = 'Exhale'
26.9651 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.9817 	EXP 	unnamed TextStim: text = 'Exhale'
26.9817 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.9989 	EXP 	unnamed TextStim: text = 'Exhale'
26.9989 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.0153 	EXP 	unnamed TextStim: text = 'Exhale'
27.0153 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.0319 	EXP 	unnamed TextStim: text = 'Exhale'
27.0319 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.0486 	EXP 	unnamed TextStim: text = 'Exhale'
27.0486 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.0654 	EXP 	unnamed TextStim: text = 'Exhale'
27.0654 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.0819 	EXP 	unnamed TextStim: text = 'Exhale'
27.0819 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.0991 	EXP 	unnamed TextStim: text = 'Exhale'
27.0991 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.1150 	EXP 	unnamed TextStim: text = 'Exhale'
27.1150 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.1317 	EXP 	unnamed TextStim: text = 'Exhale'
27.1317 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.1489 	EXP 	unnamed TextStim: text = 'Exhale'
27.1489 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.1656 	EXP 	unnamed TextStim: text = 'Exhale'
27.1656 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.1821 	EXP 	unnamed TextStim: text = 'Exhale'
27.1821 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.2001 	EXP 	unnamed TextStim: text = 'Exhale'
27.2001 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.2156 	EXP 	unnamed TextStim: text = 'Exhale'
27.2156 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.2318 	EXP 	unnamed TextStim: text = 'Exhale'
27.2318 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.2485 	EXP 	unnamed TextStim: text = 'Exhale'
27.2485 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.2650 	EXP 	unnamed TextStim: text = 'Exhale'
27.2650 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.2817 	EXP 	unnamed TextStim: text = 'Exhale'
27.2817 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.2984 	EXP 	unnamed TextStim: text = 'Exhale'
27.2984 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.3146 	EXP 	unnamed TextStim: text = 'Exhale'
27.3146 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.3315 	EXP 	unnamed TextStim: text = 'Exhale'
27.3315 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.3485 	EXP 	unnamed TextStim: text = 'Exhale'
27.3485 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.3649 	EXP 	unnamed TextStim: text = 'Exhale'
27.3649 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.3819 	EXP 	unnamed TextStim: text = 'Exhale'
27.3819 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.3990 	EXP 	unnamed TextStim: text = 'Exhale'
27.3990 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.4155 	EXP 	unnamed TextStim: text = 'Exhale'
27.4155 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.4317 	EXP 	unnamed TextStim: text = 'Exhale'
27.4317 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.4485 	EXP 	unnamed TextStim: text = 'Exhale'
27.4485 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.4649 	EXP 	unnamed TextStim: text = 'Exhale'
27.4649 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.4812 	EXP 	unnamed TextStim: text = 'Exhale'
27.4812 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
27.4981 	EXP 	unnamed TextStim: text = 'Exhale'
27.4981 	EXP 	unnamed TextStim: height = 0.03
27.4981 	EXP 	unnamed TextStim: height = 0.03
27.4981 	EXP 	unnamed TextStim: height = 0.03
27.4981 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.5146 	EXP 	unnamed TextStim: text = 'Exhale'
27.5146 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.5310 	EXP 	unnamed TextStim: text = 'Exhale'
27.5310 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.5482 	EXP 	unnamed TextStim: text = 'Exhale'
27.5482 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.5649 	EXP 	unnamed TextStim: text = 'Exhale'
27.5649 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.5818 	EXP 	unnamed TextStim: text = 'Exhale'
27.5818 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.5983 	EXP 	unnamed TextStim: text = 'Exhale'
27.5983 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.6145 	EXP 	unnamed TextStim: text = 'Exhale'
27.6145 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.6313 	EXP 	unnamed TextStim: text = 'Exhale'
27.6313 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.6486 	EXP 	unnamed TextStim: text = 'Exhale'
27.6486 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.6649 	EXP 	unnamed TextStim: text = 'Exhale'
27.6649 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.6812 	EXP 	unnamed TextStim: text = 'Exhale'
27.6812 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.6980 	EXP 	unnamed TextStim: text = 'Exhale'
27.6980 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.7148 	EXP 	unnamed TextStim: text = 'Exhale'
27.7148 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.7311 	EXP 	unnamed TextStim: text = 'Exhale'
27.7311 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.7489 	EXP 	unnamed TextStim: text = 'Exhale'
27.7489 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.7645 	EXP 	unnamed TextStim: text = 'Exhale'
27.7645 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.7811 	EXP 	unnamed TextStim: text = 'Exhale'
27.7811 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.7976 	EXP 	unnamed TextStim: text = 'Exhale'
27.7976 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.8144 	EXP 	unnamed TextStim: text = 'Exhale'
27.8144 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.8313 	EXP 	unnamed TextStim: text = 'Exhale'
27.8313 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.8481 	EXP 	unnamed TextStim: text = 'Exhale'
27.8481 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.8645 	EXP 	unnamed TextStim: text = 'Exhale'
27.8645 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.8811 	EXP 	unnamed TextStim: text = 'Exhale'
27.8811 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.8981 	EXP 	unnamed TextStim: text = 'Exhale'
27.8981 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.9147 	EXP 	unnamed TextStim: text = 'Exhale'
27.9147 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.9311 	EXP 	unnamed TextStim: text = 'Exhale'
27.9311 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.9481 	EXP 	unnamed TextStim: text = 'Exhale'
27.9481 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.9641 	EXP 	unnamed TextStim: text = 'Exhale'
27.9641 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.9811 	EXP 	unnamed TextStim: text = 'Exhale'
27.9811 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.9979 	EXP 	unnamed TextStim: text = 'Exhale'
27.9979 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.0145 	EXP 	unnamed TextStim: text = 'Exhale'
28.0145 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.0311 	EXP 	unnamed TextStim: text = 'Exhale'
28.0311 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.0479 	EXP 	unnamed TextStim: text = 'Exhale'
28.0479 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.0646 	EXP 	unnamed TextStim: text = 'Exhale'
28.0646 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.0811 	EXP 	unnamed TextStim: text = 'Exhale'
28.0811 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.0978 	EXP 	unnamed TextStim: text = 'Exhale'
28.0978 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.1146 	EXP 	unnamed TextStim: text = 'Exhale'
28.1146 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.1310 	EXP 	unnamed TextStim: text = 'Exhale'
28.1310 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.1479 	EXP 	unnamed TextStim: text = 'Exhale'
28.1479 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.1645 	EXP 	unnamed TextStim: text = 'Exhale'
28.1645 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.1809 	EXP 	unnamed TextStim: text = 'Exhale'
28.1809 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.1979 	EXP 	unnamed TextStim: text = 'Exhale'
28.1979 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.2142 	EXP 	unnamed TextStim: text = 'Exhale'
28.2142 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.2308 	EXP 	unnamed TextStim: text = 'Exhale'
28.2308 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.2477 	EXP 	unnamed TextStim: text = 'Exhale'
28.2477 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.2639 	EXP 	unnamed TextStim: text = 'Exhale'
28.2639 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.2807 	EXP 	unnamed TextStim: text = 'Exhale'
28.2807 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.2986 	EXP 	unnamed TextStim: text = 'Exhale'
28.2986 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.3138 	EXP 	unnamed TextStim: text = 'Exhale'
28.3138 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.3306 	EXP 	unnamed TextStim: text = 'Exhale'
28.3306 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.3470 	EXP 	unnamed TextStim: text = 'Exhale'
28.3470 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.3642 	EXP 	unnamed TextStim: text = 'Exhale'
28.3642 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.3810 	EXP 	unnamed TextStim: text = 'Exhale'
28.3810 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.3986 	EXP 	unnamed TextStim: text = 'Exhale'
28.3986 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.4150 	EXP 	unnamed TextStim: text = 'Exhale'
28.4150 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.4307 	EXP 	unnamed TextStim: text = 'Exhale'
28.4307 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.4473 	EXP 	unnamed TextStim: text = 'Exhale'
28.4473 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.4640 	EXP 	unnamed TextStim: text = 'Exhale'
28.4640 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.4810 	EXP 	unnamed TextStim: text = 'Exhale'
28.4810 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
28.4977 	EXP 	unnamed TextStim: text = 'Exhale'
28.4977 	EXP 	unnamed TextStim: height = 0.03
28.4977 	EXP 	unnamed TextStim: height = 0.03
28.4977 	EXP 	unnamed TextStim: height = 0.03
28.4977 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.5137 	EXP 	unnamed TextStim: text = 'Exhale'
28.5137 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.5304 	EXP 	unnamed TextStim: text = 'Exhale'
28.5304 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.5474 	EXP 	unnamed TextStim: text = 'Exhale'
28.5474 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.5641 	EXP 	unnamed TextStim: text = 'Exhale'
28.5641 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.5810 	EXP 	unnamed TextStim: text = 'Exhale'
28.5810 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.5976 	EXP 	unnamed TextStim: text = 'Exhale'
28.5976 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.6140 	EXP 	unnamed TextStim: text = 'Exhale'
28.6140 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.6305 	EXP 	unnamed TextStim: text = 'Exhale'
28.6305 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.6474 	EXP 	unnamed TextStim: text = 'Exhale'
28.6474 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.6644 	EXP 	unnamed TextStim: text = 'Exhale'
28.6644 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.6804 	EXP 	unnamed TextStim: text = 'Exhale'
28.6804 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.6972 	EXP 	unnamed TextStim: text = 'Exhale'
28.6972 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.7137 	EXP 	unnamed TextStim: text = 'Exhale'
28.7137 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.7305 	EXP 	unnamed TextStim: text = 'Exhale'
28.7305 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.7471 	EXP 	unnamed TextStim: text = 'Exhale'
28.7471 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.7635 	EXP 	unnamed TextStim: text = 'Exhale'
28.7635 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.7803 	EXP 	unnamed TextStim: text = 'Exhale'
28.7803 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.7969 	EXP 	unnamed TextStim: text = 'Exhale'
28.7969 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.8136 	EXP 	unnamed TextStim: text = 'Exhale'
28.8136 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.8304 	EXP 	unnamed TextStim: text = 'Exhale'
28.8304 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.8489 	EXP 	unnamed TextStim: text = 'Exhale'
28.8489 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.8636 	EXP 	unnamed TextStim: text = 'Exhale'
28.8636 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.8803 	EXP 	unnamed TextStim: text = 'Exhale'
28.8803 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.8972 	EXP 	unnamed TextStim: text = 'Exhale'
28.8972 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.9138 	EXP 	unnamed TextStim: text = 'Exhale'
28.9138 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.9304 	EXP 	unnamed TextStim: text = 'Exhale'
28.9304 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.9471 	EXP 	unnamed TextStim: text = 'Exhale'
28.9471 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.9635 	EXP 	unnamed TextStim: text = 'Exhale'
28.9635 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.9803 	EXP 	unnamed TextStim: text = 'Exhale'
28.9803 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.9967 	EXP 	unnamed TextStim: text = 'Exhale'
28.9967 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.0133 	EXP 	unnamed TextStim: text = 'Exhale'
29.0133 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.0302 	EXP 	unnamed TextStim: text = 'Exhale'
29.0302 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.0468 	EXP 	unnamed TextStim: text = 'Exhale'
29.0468 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.0637 	EXP 	unnamed TextStim: text = 'Exhale'
29.0637 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.0804 	EXP 	unnamed TextStim: text = 'Exhale'
29.0804 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.0970 	EXP 	unnamed TextStim: text = 'Exhale'
29.0970 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.1134 	EXP 	unnamed TextStim: text = 'Exhale'
29.1134 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.1302 	EXP 	unnamed TextStim: text = 'Exhale'
29.1302 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.1467 	EXP 	unnamed TextStim: text = 'Exhale'
29.1467 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.1635 	EXP 	unnamed TextStim: text = 'Exhale'
29.1635 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.1801 	EXP 	unnamed TextStim: text = 'Exhale'
29.1801 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.1969 	EXP 	unnamed TextStim: text = 'Exhale'
29.1969 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.2134 	EXP 	unnamed TextStim: text = 'Exhale'
29.2134 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.2302 	EXP 	unnamed TextStim: text = 'Exhale'
29.2302 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.2465 	EXP 	unnamed TextStim: text = 'Exhale'
29.2465 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.2629 	EXP 	unnamed TextStim: text = 'Exhale'
29.2629 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.2797 	EXP 	unnamed TextStim: text = 'Exhale'
29.2797 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.2963 	EXP 	unnamed TextStim: text = 'Exhale'
29.2963 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.3131 	EXP 	unnamed TextStim: text = 'Exhale'
29.3131 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.3300 	EXP 	unnamed TextStim: text = 'Exhale'
29.3300 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.3466 	EXP 	unnamed TextStim: text = 'Exhale'
29.3466 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.3630 	EXP 	unnamed TextStim: text = 'Exhale'
29.3630 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.3805 	EXP 	unnamed TextStim: text = 'Exhale'
29.3805 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.3968 	EXP 	unnamed TextStim: text = 'Exhale'
29.3968 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.4152 	EXP 	unnamed TextStim: text = 'Exhale'
29.4152 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.4300 	EXP 	unnamed TextStim: text = 'Exhale'
29.4300 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.4466 	EXP 	unnamed TextStim: text = 'Exhale'
29.4466 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.4636 	EXP 	unnamed TextStim: text = 'Exhale'
29.4636 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.4799 	EXP 	unnamed TextStim: text = 'Exhale'
29.4799 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
29.4962 	EXP 	unnamed TextStim: text = 'Exhale'
29.4962 	EXP 	unnamed TextStim: height = 0.03
29.4962 	EXP 	unnamed TextStim: height = 0.03
29.4962 	EXP 	unnamed TextStim: height = 0.03
29.4962 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.5129 	EXP 	unnamed TextStim: text = 'Exhale'
29.5129 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.5301 	EXP 	unnamed TextStim: text = 'Exhale'
29.5301 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.5465 	EXP 	unnamed TextStim: text = 'Exhale'
29.5465 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.5630 	EXP 	unnamed TextStim: text = 'Exhale'
29.5630 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.5799 	EXP 	unnamed TextStim: text = 'Exhale'
29.5799 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.5961 	EXP 	unnamed TextStim: text = 'Exhale'
29.5961 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.6127 	EXP 	unnamed TextStim: text = 'Exhale'
29.6127 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.6299 	EXP 	unnamed TextStim: text = 'Exhale'
29.6299 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.6464 	EXP 	unnamed TextStim: text = 'Exhale'
29.6464 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.6631 	EXP 	unnamed TextStim: text = 'Exhale'
29.6631 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.6812 	EXP 	unnamed TextStim: text = 'Exhale'
29.6812 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.6964 	EXP 	unnamed TextStim: text = 'Exhale'
29.6964 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.7127 	EXP 	unnamed TextStim: text = 'Exhale'
29.7127 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.7297 	EXP 	unnamed TextStim: text = 'Exhale'
29.7297 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.7462 	EXP 	unnamed TextStim: text = 'Exhale'
29.7462 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.7627 	EXP 	unnamed TextStim: text = 'Exhale'
29.7627 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.7799 	EXP 	unnamed TextStim: text = 'Exhale'
29.7799 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.7962 	EXP 	unnamed TextStim: text = 'Exhale'
29.7962 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.8124 	EXP 	unnamed TextStim: text = 'Exhale'
29.8124 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.8297 	EXP 	unnamed TextStim: text = 'Exhale'
29.8297 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.8461 	EXP 	unnamed TextStim: text = 'Exhale'
29.8461 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.8625 	EXP 	unnamed TextStim: text = 'Exhale'
29.8625 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.8795 	EXP 	unnamed TextStim: text = 'Exhale'
29.8795 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.8962 	EXP 	unnamed TextStim: text = 'Exhale'
29.8962 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.9131 	EXP 	unnamed TextStim: text = 'Exhale'
29.9131 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.9300 	EXP 	unnamed TextStim: text = 'Exhale'
29.9300 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.9459 	EXP 	unnamed TextStim: text = 'Exhale'
29.9459 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.9624 	EXP 	unnamed TextStim: text = 'Exhale'
29.9624 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.9809 	EXP 	unnamed TextStim: text = 'Exhale'
29.9809 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.9964 	EXP 	unnamed TextStim: height = 0.05
29.9964 	EXP 	unnamed TextStim: height = 0.05
29.9964 	EXP 	unnamed TextStim: height = 0.05
29.9964 	EXP 	unnamed TextStim: text = 'Hold'
29.9964 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.0122 	EXP 	unnamed TextStim: text = 'Hold'
30.0122 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.0294 	EXP 	unnamed TextStim: text = 'Hold'
30.0294 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.0456 	EXP 	unnamed TextStim: text = 'Hold'
30.0456 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.0626 	EXP 	unnamed TextStim: text = 'Hold'
30.0626 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.0800 	EXP 	unnamed TextStim: text = 'Hold'
30.0800 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.0962 	EXP 	unnamed TextStim: text = 'Hold'
30.0962 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.1126 	EXP 	unnamed TextStim: text = 'Hold'
30.1126 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.1294 	EXP 	unnamed TextStim: text = 'Hold'
30.1294 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.1461 	EXP 	unnamed TextStim: text = 'Hold'
30.1461 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.1628 	EXP 	unnamed TextStim: text = 'Hold'
30.1628 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.1793 	EXP 	unnamed TextStim: text = 'Hold'
30.1793 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.1964 	EXP 	unnamed TextStim: text = 'Hold'
30.1964 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.2123 	EXP 	unnamed TextStim: text = 'Hold'
30.2123 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.2289 	EXP 	unnamed TextStim: text = 'Hold'
30.2289 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.2456 	EXP 	unnamed TextStim: text = 'Hold'
30.2456 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.2624 	EXP 	unnamed TextStim: text = 'Hold'
30.2624 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.2791 	EXP 	unnamed TextStim: text = 'Hold'
30.2791 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.2955 	EXP 	unnamed TextStim: text = 'Hold'
30.2955 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.3125 	EXP 	unnamed TextStim: text = 'Hold'
30.3125 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.3293 	EXP 	unnamed TextStim: text = 'Hold'
30.3293 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.3455 	EXP 	unnamed TextStim: text = 'Hold'
30.3455 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.3622 	EXP 	unnamed TextStim: text = 'Hold'
30.3622 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.3790 	EXP 	unnamed TextStim: text = 'Hold'
30.3790 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.3958 	EXP 	unnamed TextStim: text = 'Hold'
30.3958 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.4123 	EXP 	unnamed TextStim: text = 'Hold'
30.4123 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.4293 	EXP 	unnamed TextStim: text = 'Hold'
30.4293 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.4457 	EXP 	unnamed TextStim: text = 'Hold'
30.4457 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.4624 	EXP 	unnamed TextStim: text = 'Hold'
30.4624 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.4788 	EXP 	unnamed TextStim: text = 'Hold'
30.4788 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
30.4954 	EXP 	unnamed TextStim: height = 0.05
30.4954 	EXP 	unnamed TextStim: height = 0.05
30.4954 	EXP 	unnamed TextStim: height = 0.05
30.4954 	EXP 	unnamed TextStim: text = 'Inhale'
30.4954 	EXP 	unnamed TextStim: height = 0.03
30.4954 	EXP 	unnamed TextStim: height = 0.03
30.4954 	EXP 	unnamed TextStim: height = 0.03
30.4954 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.5122 	EXP 	unnamed TextStim: text = 'Inhale'
30.5122 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.5306 	EXP 	unnamed TextStim: text = 'Inhale'
30.5306 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.5457 	EXP 	unnamed TextStim: text = 'Inhale'
30.5457 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.5623 	EXP 	unnamed TextStim: text = 'Inhale'
30.5623 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.5790 	EXP 	unnamed TextStim: text = 'Inhale'
30.5790 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.5956 	EXP 	unnamed TextStim: text = 'Inhale'
30.5956 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.6122 	EXP 	unnamed TextStim: text = 'Inhale'
30.6122 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.6292 	EXP 	unnamed TextStim: text = 'Inhale'
30.6292 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.6453 	EXP 	unnamed TextStim: text = 'Inhale'
30.6453 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.6621 	EXP 	unnamed TextStim: text = 'Inhale'
30.6621 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.6793 	EXP 	unnamed TextStim: text = 'Inhale'
30.6793 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.6951 	EXP 	unnamed TextStim: text = 'Inhale'
30.6951 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.7120 	EXP 	unnamed TextStim: text = 'Inhale'
30.7120 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.7287 	EXP 	unnamed TextStim: text = 'Inhale'
30.7287 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.7451 	EXP 	unnamed TextStim: text = 'Inhale'
30.7451 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.7623 	EXP 	unnamed TextStim: text = 'Inhale'
30.7623 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.7791 	EXP 	unnamed TextStim: text = 'Inhale'
30.7791 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.7954 	EXP 	unnamed TextStim: text = 'Inhale'
30.7954 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.8119 	EXP 	unnamed TextStim: text = 'Inhale'
30.8119 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.8287 	EXP 	unnamed TextStim: text = 'Inhale'
30.8287 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.8452 	EXP 	unnamed TextStim: text = 'Inhale'
30.8452 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.8621 	EXP 	unnamed TextStim: text = 'Inhale'
30.8621 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.8788 	EXP 	unnamed TextStim: text = 'Inhale'
30.8788 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.8952 	EXP 	unnamed TextStim: text = 'Inhale'
30.8952 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.9120 	EXP 	unnamed TextStim: text = 'Inhale'
30.9120 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.9287 	EXP 	unnamed TextStim: text = 'Inhale'
30.9287 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.9452 	EXP 	unnamed TextStim: text = 'Inhale'
30.9452 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.9617 	EXP 	unnamed TextStim: text = 'Inhale'
30.9617 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.9786 	EXP 	unnamed TextStim: text = 'Inhale'
30.9786 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.9952 	EXP 	unnamed TextStim: text = 'Inhale'
30.9952 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.0119 	EXP 	unnamed TextStim: text = 'Inhale'
31.0119 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.0289 	EXP 	unnamed TextStim: text = 'Inhale'
31.0289 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.0448 	EXP 	unnamed TextStim: text = 'Inhale'
31.0448 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.0618 	EXP 	unnamed TextStim: text = 'Inhale'
31.0618 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.0805 	EXP 	unnamed TextStim: text = 'Inhale'
31.0805 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.0952 	EXP 	unnamed TextStim: text = 'Inhale'
31.0952 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.1119 	EXP 	unnamed TextStim: text = 'Inhale'
31.1119 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.1290 	EXP 	unnamed TextStim: text = 'Inhale'
31.1290 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.1452 	EXP 	unnamed TextStim: text = 'Inhale'
31.1452 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.1618 	EXP 	unnamed TextStim: text = 'Inhale'
31.1618 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.1785 	EXP 	unnamed TextStim: text = 'Inhale'
31.1785 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.1951 	EXP 	unnamed TextStim: text = 'Inhale'
31.1951 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.2116 	EXP 	unnamed TextStim: text = 'Inhale'
31.2116 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.2286 	EXP 	unnamed TextStim: text = 'Inhale'
31.2286 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.2450 	EXP 	unnamed TextStim: text = 'Inhale'
31.2450 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.2614 	EXP 	unnamed TextStim: text = 'Inhale'
31.2614 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.2792 	EXP 	unnamed TextStim: text = 'Inhale'
31.2792 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.2954 	EXP 	unnamed TextStim: text = 'Inhale'
31.2954 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.3115 	EXP 	unnamed TextStim: text = 'Inhale'
31.3115 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.3287 	EXP 	unnamed TextStim: text = 'Inhale'
31.3287 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.3449 	EXP 	unnamed TextStim: text = 'Inhale'
31.3449 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.3612 	EXP 	unnamed TextStim: text = 'Inhale'
31.3612 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.3786 	EXP 	unnamed TextStim: text = 'Inhale'
31.3786 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.3945 	EXP 	unnamed TextStim: text = 'Inhale'
31.3945 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.4114 	EXP 	unnamed TextStim: text = 'Inhale'
31.4114 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.4283 	EXP 	unnamed TextStim: text = 'Inhale'
31.4283 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.4448 	EXP 	unnamed TextStim: text = 'Inhale'
31.4448 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.4615 	EXP 	unnamed TextStim: text = 'Inhale'
31.4615 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
31.4782 	EXP 	unnamed TextStim: text = 'Inhale'
31.4782 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
32.9954 	DATA 	Mouse: Left button down, pos=(1332,690)
33.0616 	DATA 	Mouse:  Left button up, pos=(1331,690)
33.1146 	DATA 	Keypress: escape
33.4697 	DATA 	Keypress: escape
33.5415 	EXP 	01_resting_state: status = STOPPED
33.5466 	EXP 	01_resting_state: status = STOPPED
33.6245 	EXP 	window1: mouseVisible = True
