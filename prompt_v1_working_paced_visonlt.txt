create a paced breathing code for PsychoPy for pasting it into a code block in a routine, as an experimental task.
It should look like e.g. the image.png which is a screenshot of the android paced breathing app. the way it works is it can be set how long is the: inhale, inhale hold, exhale, exhale hold in x.x seconds.
It displays the line line segments for the set breathing pattern, fixed: going up for inhale, staying up flat for inhale hold, going down for exhale, keeping down flat for exhale hold.
a ball travels on the line showing what to do, and in the background a semi-transparent semi-opaque shading also goes up-down together with the ball, at the level of the ball. the horizontal total length is normalized to the screen's width for the sum of total time set for all 4 segments. the ball travel through in the total time for 1 cycle, then repeats, starts from thel left again, making it a cyclical fix paced breathing patter visual guidance. 
implement this for a psychopy code block.
the default settings: inhale=4.5s, inhale_hold = 0.5s, exhale=4.5s, exhale_hold =0.5s.
if you have any questions before writing the full code, ask me. 