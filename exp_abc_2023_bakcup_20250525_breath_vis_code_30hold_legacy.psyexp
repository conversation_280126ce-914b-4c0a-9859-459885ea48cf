﻿<?xml version="1.0" ?>
<PsychoPy2experiment encoding="utf-8" version="2023.2.3">
  <Settings>
    <Param val="0" valType="str" updates="None" name="Audio latency priority"/>
    <Param val="ptb" valType="str" updates="None" name="Audio lib"/>
    <Param val="" valType="str" updates="None" name="Completed URL"/>
    <Param val="auto" valType="str" updates="None" name="Data file delimiter"/>
    <Param val="u'data/%s_%s_%s' % (expInfo['participant'], expName, expInfo['date'])" valType="code" updates="None" name="Data filename"/>
    <Param val="True" valType="bool" updates="None" name="Enable Escape"/>
    <Param val="" valType="str" updates="None" name="End Message"/>
    <Param val="{'participant': '99', 'session': '01'}" valType="code" updates="None" name="Experiment info"/>
    <Param val="False" valType="bool" updates="None" name="Force stereo"/>
    <Param val="True" valType="bool" updates="None" name="Full-screen window"/>
    <Param val="" valType="str" updates="None" name="HTML path"/>
    <Param val="" valType="str" updates="None" name="Incomplete URL"/>
    <Param val="testMonitor" valType="str" updates="None" name="Monitor"/>
    <Param val="[]" valType="list" updates="None" name="Resources"/>
    <Param val="False" valType="bool" updates="None" name="Save csv file"/>
    <Param val="False" valType="bool" updates="None" name="Save excel file"/>
    <Param val="False" valType="bool" updates="None" name="Save hdf5 file"/>
    <Param val="True" valType="bool" updates="None" name="Save log file"/>
    <Param val="True" valType="bool" updates="None" name="Save psydat file"/>
    <Param val="True" valType="bool" updates="None" name="Save wide csv file"/>
    <Param val="1" valType="num" updates="None" name="Screen"/>
    <Param val="True" valType="bool" updates="None" name="Show info dlg"/>
    <Param val="False" valType="bool" updates="None" name="Show mouse"/>
    <Param val="height" valType="str" updates="None" name="Units"/>
    <Param val="2023.2.3" valType="str" updates="None" name="Use version"/>
    <Param val="[1536, 864]" valType="list" updates="None" name="Window size (pixels)"/>
    <Param val="none" valType="str" updates="None" name="backgroundFit"/>
    <Param val="" valType="str" updates="None" name="backgroundImg"/>
    <Param val="avg" valType="str" updates="None" name="blendMode"/>
    <Param val="{'thisRow.t': 'priority.CRITICAL', 'expName': 'priority.LOW'}" valType="dict" updates="None" name="colPriority"/>
    <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
    <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
    <Param val="*********" valType="str" updates="None" name="elAddress"/>
    <Param val="FILTER_LEVEL_2" valType="str" updates="None" name="elDataFiltering"/>
    <Param val="FILTER_LEVEL_OFF" valType="str" updates="None" name="elLiveFiltering"/>
    <Param val="EYELINK 1000 DESKTOP" valType="str" updates="None" name="elModel"/>
    <Param val="ELLIPSE_FIT" valType="str" updates="None" name="elPupilAlgorithm"/>
    <Param val="PUPIL_AREA" valType="str" updates="None" name="elPupilMeasure"/>
    <Param val="1000" valType="num" updates="None" name="elSampleRate"/>
    <Param val="False" valType="bool" updates="None" name="elSimMode"/>
    <Param val="RIGHT_EYE" valType="str" updates="None" name="elTrackEyes"/>
    <Param val="PUPIL_CR_TRACKING" valType="str" updates="None" name="elTrackingMode"/>
    <Param val="01_resting_state" valType="str" updates="None" name="expName"/>
    <Param val="on Sync" valType="str" updates="None" name="exportHTML"/>
    <Param val="None" valType="str" updates="None" name="eyetracker"/>
    <Param val="127.0.0.1" valType="str" updates="None" name="gpAddress"/>
    <Param val="4242" valType="num" updates="None" name="gpPort"/>
    <Param val="ioHub" valType="str" updates="None" name="keyboardBackend"/>
    <Param val="exp" valType="code" updates="None" name="logging level"/>
    <Param val="('MIDDLE_BUTTON',)" valType="list" updates="None" name="mgBlink"/>
    <Param val="CONTINUOUS" valType="str" updates="None" name="mgMove"/>
    <Param val="0.5" valType="num" updates="None" name="mgSaccade"/>
    <Param val="neon.local" valType="str" updates="None" name="plCompanionAddress"/>
    <Param val="scene_camera.json" valType="file" updates="None" name="plCompanionCameraCalibration"/>
    <Param val="8080" valType="num" updates="None" name="plCompanionPort"/>
    <Param val="True" valType="bool" updates="None" name="plCompanionRecordingEnabled"/>
    <Param val="0.6" valType="num" updates="None" name="plConfidenceThreshold"/>
    <Param val="True" valType="bool" updates="None" name="plPupilCaptureRecordingEnabled"/>
    <Param val="" valType="str" updates="None" name="plPupilCaptureRecordingLocation"/>
    <Param val="127.0.0.1" valType="str" updates="None" name="plPupilRemoteAddress"/>
    <Param val="50020" valType="num" updates="None" name="plPupilRemotePort"/>
    <Param val="1000" valType="num" updates="None" name="plPupilRemoteTimeoutMs"/>
    <Param val="False" valType="bool" updates="None" name="plPupillometryOnly"/>
    <Param val="psychopy_iohub_surface" valType="str" updates="None" name="plSurfaceName"/>
    <Param val="time" valType="str" updates="None" name="sortColumns"/>
    <Param val="" valType="str" updates="None" name="tbLicenseFile"/>
    <Param val="" valType="str" updates="None" name="tbModel"/>
    <Param val="60" valType="num" updates="None" name="tbSampleRate"/>
    <Param val="" valType="str" updates="None" name="tbSerialNo"/>
    <Param val="pyglet" valType="str" updates="None" name="winBackend"/>
  </Settings>
  <Routines>
    <Routine name="Load_LSL">
      <RoutineSettingsComponent name="Load_LSL" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="Load_LSL" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <CodeComponent name="Load_LSL" plugin="None">
        <Param val="" valType="extendedCode" updates="constant" name="Before Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Before JS Experiment"/>
        <Param val="from pylsl import StreamInfo, StreamOutlet # import required classes&amp;#10;&amp;#10;info = StreamInfo(name='Trigger',type='Markers', channel_count=1,&amp;#10;channel_format='int32', source_id='Example') # sets variables for object info&amp;#10;&amp;#10;outlet = StreamOutlet(info) # initialise stream." valType="extendedCode" updates="constant" name="Begin Experiment"/>
        <Param val="import {StreamInfo, StreamOutlet} from 'pylsl';&amp;#10;var info, outlet;&amp;#10;info = new StreamInfo({&quot;name&quot;: &quot;Trigger&quot;, &quot;type&quot;: &quot;Markers&quot;, &quot;channel_count&quot;: 1, &quot;channel_format&quot;: &quot;int32&quot;, &quot;source_id&quot;: &quot;Example&quot;});&amp;#10;outlet = new StreamOutlet(info);&amp;#10;" valType="extendedCode" updates="constant" name="Begin JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin Routine"/>
        <Param val="Auto-&gt;JS" valType="str" updates="None" name="Code Type"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each JS Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Routine"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="Load_LSL" valType="code" updates="None" name="name"/>
      </CodeComponent>
    </Routine>
    <Routine name="End">
      <RoutineSettingsComponent name="End" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="End" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <CodeComponent name="t_end" plugin="None">
        <Param val="" valType="extendedCode" updates="constant" name="Before Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Before JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Experiment"/>
        <Param val="import * as gc from 'gc';&amp;#10;outlet.push_sample({&quot;x&quot;: [2]});&amp;#10;gc.collect();&amp;#10;psychoJS.window.flip();&amp;#10;core.wait(2.0);&amp;#10;" valType="extendedCode" updates="constant" name="Begin JS Routine"/>
        <Param val="outlet.push_sample(x=[2])&amp;#10;&amp;#10;#Clean up&amp;#10;import gc&amp;#10;gc.collect()  # Force garbage collection&amp;#10;&amp;#10;# Just clear the screen and wait&amp;#10;win.flip()&amp;#10;core.wait(2.0)" valType="extendedCode" updates="constant" name="Begin Routine"/>
        <Param val="Auto-&gt;JS" valType="str" updates="None" name="Code Type"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each JS Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Routine"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="t_end" valType="code" updates="None" name="name"/>
      </CodeComponent>
      <TextComponent name="EndMessage" plugin="None">
        <Param val="white" valType="color" updates="constant" name="color"/>
        <Param val="rgb" valType="str" updates="constant" name="colorSpace"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="None" valType="str" updates="constant" name="flip"/>
        <Param val="Open Sans" valType="str" updates="constant" name="font"/>
        <Param val="LTR" valType="str" updates="None" name="languageStyle"/>
        <Param val="0.05" valType="num" updates="constant" name="letterHeight"/>
        <Param val="EndMessage" valType="code" updates="None" name="name"/>
        <Param val="" valType="num" updates="constant" name="opacity"/>
        <Param val="0" valType="num" updates="constant" name="ori"/>
        <Param val="(0, 0)" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="1.0" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="Thank you!" valType="str" updates="constant" name="text"/>
        <Param val="from exp settings" valType="str" updates="None" name="units"/>
        <Param val="" valType="num" updates="constant" name="wrapWidth"/>
      </TextComponent>
      <SoundComponent name="thankyou" plugin="None">
        <Param val="True" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="True" valType="bool" updates="constant" name="hamming"/>
        <Param val="thankyou" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="sound/thankyou.wav" valType="str" updates="constant" name="sound"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="1.0" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="constant" name="stopWithRoutine"/>
        <Param val="True" valType="bool" updates="constant" name="syncScreenRefresh"/>
        <Param val="1" valType="num" updates="constant" name="volume"/>
      </SoundComponent>
    </Routine>
    <Routine name="_04_arithmetic_instr">
      <RoutineSettingsComponent name="_04_arithmetic_instr" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="_04_arithmetic_instr" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <CodeComponent name="instr_arithmetic" plugin="None">
        <Param val="" valType="extendedCode" updates="constant" name="Before Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Before JS Experiment"/>
        <Param val="from psychopy import visual&amp;#10;from psychopy import event # For event.waitKeys()&amp;#10;" valType="extendedCode" updates="constant" name="Begin Experiment"/>
        <Param val="import {visual} from 'psychopy';&amp;#10;import {event} from 'psychopy';&amp;#10;" valType="extendedCode" updates="constant" name="Begin JS Experiment"/>
        <Param val="instructions = new visual.TextStim(psychoJS.window, {&quot;text&quot;: &quot;Keep subtracting 13 from the number shown. (count backwards by 13)\nType and press Enter.\nPress F1 to start.&quot;, &quot;height&quot;: 0.03, &quot;color&quot;: &quot;white&quot;, &quot;pos&quot;: [0, (- 0.3)], &quot;wrapWidth&quot;: 1.5});&amp;#10;instructions.draw();&amp;#10;psychoJS.window.flip();&amp;#10;psychoJS.eventManager.waitKeys({&quot;keyList&quot;: [&quot;f1&quot;]});&amp;#10;outlet.push_sample({&quot;x&quot;: [1]});&amp;#10;" valType="extendedCode" updates="constant" name="Begin JS Routine"/>
        <Param val="# Initialize components&amp;#10;instructions = visual.TextStim(win, &amp;#10;    text=&quot;Keep subtracting 13 from the number shown. (count backwards by 13)\nType and press Enter.\nPress F1 to start.&quot;,&amp;#10;    height=0.03, color=&quot;white&quot;, pos=(0, -0.3), wrapWidth=1.5)  # Added wrapWidth&amp;#10;&amp;#10;# Show instructions&amp;#10;instructions.draw()&amp;#10;win.flip()&amp;#10;&amp;#10;# Wait for F1 key press&amp;#10;event.waitKeys(keyList=['f1'])&amp;#10;&amp;#10;# Send trigger for initial instructions&amp;#10;outlet.push_sample(x=[1])" valType="extendedCode" updates="constant" name="Begin Routine"/>
        <Param val="Auto-&gt;JS" valType="str" updates="None" name="Code Type"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each JS Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Routine"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="instr_arithmetic" valType="code" updates="None" name="name"/>
      </CodeComponent>
    </Routine>
    <Routine name="_04_arithmetic_trial">
      <RoutineSettingsComponent name="_04_arithmetic_trial" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="_04_arithmetic_trial" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <CodeComponent name="code_arithmetic" plugin="None">
        <Param val="" valType="extendedCode" updates="constant" name="Before Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Before JS Experiment"/>
        <Param val="# And in your Begin Experiment section&amp;#10;#print(&quot;DEBUG: Experiment starting&quot;)&amp;#10;#print(&quot;DEBUG: Global sound module type:&quot;, type(sound))&amp;#10;" valType="extendedCode" updates="constant" name="Begin Experiment"/>
        <Param val="/* Syntax Error: Fix Python code */" valType="extendedCode" updates="constant" name="Begin JS Experiment"/>
        <Param val="import * as random from 'random';&amp;#10;import * as gc from 'gc';&amp;#10;function _pj_snippets(container) {&amp;#10;    function in_es6(left, right) {&amp;#10;        if (((right instanceof Array) || ((typeof right) === &quot;string&quot;))) {&amp;#10;            return (right.indexOf(left) &gt; (- 1));&amp;#10;        } else {&amp;#10;            if (((right instanceof Map) || (right instanceof Set) || (right instanceof WeakMap) || (right instanceof WeakSet))) {&amp;#10;                return right.has(left);&amp;#10;            } else {&amp;#10;                return (left in right);&amp;#10;            }&amp;#10;        }&amp;#10;    }&amp;#10;    container[&quot;in_es6&quot;] = in_es6;&amp;#10;    return container;&amp;#10;}&amp;#10;_pj = {};&amp;#10;_pj_snippets(_pj);&amp;#10;current_number = Math.random.randint(1000, 1999);&amp;#10;duration = stress_duration;&amp;#10;last_response_time = 0;&amp;#10;clock = new util.Clock();&amp;#10;start_time = clock.getTime();&amp;#10;screen_width = 2.0;&amp;#10;subject_proportion = (2 / 3);&amp;#10;experimenter_proportion = (1 / 3);&amp;#10;subject_width = (subject_proportion * screen_width);&amp;#10;experimenter_width = (experimenter_proportion * screen_width);&amp;#10;divider_x_pos = ((- 1) + subject_width);&amp;#10;subject_section_center_x = ((- 1) + (subject_width / 2));&amp;#10;experimenter_section_center_x = (divider_x_pos + (experimenter_width / 2));&amp;#10;subject_bar_width = (0.75 * subject_width);&amp;#10;experimenter_bar_width = (0.75 * experimenter_width);&amp;#10;divider_line = new visual.Line(psychoJS.window, {&quot;start&quot;: [divider_x_pos, 1], &quot;end&quot;: [divider_x_pos, (- 1)], &quot;lineColor&quot;: &quot;white&quot;, &quot;lineWidth&quot;: 5});&amp;#10;text_4digit_subject = new visual.TextStim(psychoJS.window, {&quot;text&quot;: current_number.toString(), &quot;pos&quot;: [subject_section_center_x, 0.2], &quot;height&quot;: 0.1, &quot;color&quot;: &quot;white&quot;, &quot;font&quot;: &quot;Arial&quot;});&amp;#10;text_instruction_subject = new visual.TextStim(psychoJS.window, {&quot;text&quot;: &quot;Subtract 13&quot;, &quot;pos&quot;: [subject_section_center_x, 0.1], &quot;height&quot;: 0.05, &quot;color&quot;: &quot;white&quot;, &quot;font&quot;: &quot;Arial&quot;});&amp;#10;speed_warning_subject = new visual.TextStim(psychoJS.window, {&quot;text&quot;: &quot;FASTER! You're Slow!&quot;, &quot;pos&quot;: [subject_section_center_x, 0.3], &quot;height&quot;: 0.08, &quot;color&quot;: &quot;red&quot;, &quot;bold&quot;: true, &quot;italic&quot;: false, &quot;font&quot;: &quot;Arial&quot;});&amp;#10;speed_warning_experimenter = new visual.TextStim(psychoJS.window, {&quot;text&quot;: &quot;say: FASTER!!&quot;, &quot;pos&quot;: [experimenter_section_center_x, 0.3], &quot;height&quot;: 0.08, &quot;color&quot;: &quot;red&quot;, &quot;bold&quot;: true, &quot;italic&quot;: false, &quot;font&quot;: &quot;Arial&quot;});&amp;#10;text_correct_answer_exp = new visual.TextStim(psychoJS.window, {&quot;text&quot;: &quot;&quot;, &quot;pos&quot;: [experimenter_section_center_x, 0.2], &quot;height&quot;: 0.1, &quot;color&quot;: &quot;white&quot;, &quot;font&quot;: &quot;Arial&quot;});&amp;#10;wrong_instruction = new visual.TextStim(psychoJS.window, {&quot;text&quot;: &quot;\u2190 Wrong&quot;, &quot;pos&quot;: [(experimenter_section_center_x - 0.08), 0.1], &quot;height&quot;: 0.03, &quot;color&quot;: &quot;red&quot;, &quot;font&quot;: &quot;Arial&quot;, &quot;alignText&quot;: &quot;right&quot;, &quot;wrapWidth&quot;: (experimenter_bar_width * 0.3)});&amp;#10;separator_text = new visual.TextStim(psychoJS.window, {&quot;text&quot;: &quot;|&quot;, &quot;pos&quot;: [experimenter_section_center_x, 0.1], &quot;height&quot;: 0.03, &quot;color&quot;: &quot;white&quot;, &quot;font&quot;: &quot;Arial&quot;, &quot;alignText&quot;: &quot;center&quot;});&amp;#10;correct_instruction = new visual.TextStim(psychoJS.window, {&quot;text&quot;: &quot;Correct \u2192&quot;, &quot;pos&quot;: [(experimenter_section_center_x + 0.08), 0.1], &quot;height&quot;: 0.03, &quot;color&quot;: &quot;green&quot;, &quot;font&quot;: &quot;Arial&quot;, &quot;alignText&quot;: &quot;left&quot;, &quot;wrapWidth&quot;: (experimenter_bar_width * 0.3)});&amp;#10;text_exp_feedback = new visual.TextStim(psychoJS.window, {&quot;text&quot;: &quot;&quot;, &quot;pos&quot;: [experimenter_section_center_x, 0.0], &quot;height&quot;: 0.07, &quot;color&quot;: &quot;white&quot;, &quot;font&quot;: &quot;Arial&quot;});&amp;#10;text_subject_feedback = new visual.TextStim(psychoJS.window, {&quot;text&quot;: &quot;&quot;, &quot;pos&quot;: [subject_section_center_x, 0.0], &quot;height&quot;: 0.07, &quot;color&quot;: &quot;white&quot;, &quot;font&quot;: &quot;Arial&quot;});&amp;#10;subject_progress_bar = new visual.Rect(psychoJS.window, {&quot;width&quot;: subject_bar_width, &quot;height&quot;: 0.05, &quot;fillColor&quot;: &quot;white&quot;, &quot;lineColor&quot;: null, &quot;pos&quot;: [subject_section_center_x, (- 0.4)]});&amp;#10;experimenter_progress_bar = new visual.Rect(psychoJS.window, {&quot;width&quot;: experimenter_bar_width, &quot;height&quot;: 0.05, &quot;fillColor&quot;: &quot;white&quot;, &quot;lineColor&quot;: null, &quot;pos&quot;: [experimenter_section_center_x, (- 0.4)]});&amp;#10;time_left_text = new visual.TextStim(psychoJS.window, {&quot;text&quot;: &quot;Time left:&quot;, &quot;pos&quot;: [((subject_section_center_x - (subject_bar_width / 2)) - 0.05), (- 0.45)], &quot;height&quot;: 0.04, &quot;color&quot;: &quot;white&quot;, &quot;alignHoriz&quot;: &quot;left&quot;, &quot;font&quot;: &quot;Arial&quot;});&amp;#10;time_value_text = new visual.TextStim(psychoJS.window, {&quot;text&quot;: &quot;&quot;, &quot;pos&quot;: [((subject_section_center_x - (subject_bar_width / 2)) + 0.15), (- 0.45)], &quot;height&quot;: 0.04, &quot;color&quot;: &quot;white&quot;, &quot;alignHoriz&quot;: &quot;left&quot;, &quot;font&quot;: &quot;Arial&quot;});&amp;#10;time_left_text_exp = new visual.TextStim(psychoJS.window, {&quot;text&quot;: &quot;Time left:&quot;, &quot;pos&quot;: [((experimenter_section_center_x - (experimenter_bar_width / 2)) - 0.05), (- 0.45)], &quot;height&quot;: 0.04, &quot;color&quot;: &quot;white&quot;, &quot;alignHoriz&quot;: &quot;left&quot;, &quot;font&quot;: &quot;Arial&quot;});&amp;#10;time_value_text_exp = new visual.TextStim(psychoJS.window, {&quot;text&quot;: &quot;&quot;, &quot;pos&quot;: [((experimenter_section_center_x - (experimenter_bar_width / 2)) + 0.15), (- 0.45)], &quot;height&quot;: 0.04, &quot;color&quot;: &quot;white&quot;, &quot;alignHoriz&quot;: &quot;left&quot;, &quot;font&quot;: &quot;Arial&quot;});&amp;#10;while (true) {&amp;#10;    current_loop_time = clock.getTime();&amp;#10;    time_elapsed = (current_loop_time - start_time);&amp;#10;    time_left = (duration - time_elapsed);&amp;#10;    if ((time_left &lt;= 0)) {&amp;#10;        time_left = 0;&amp;#10;        break;&amp;#10;    }&amp;#10;    trial_start_time = current_loop_time;&amp;#10;    if ((_pj.in_es6(&quot;outlet&quot;, globals()) &amp;&amp; (outlet !== null))) {&amp;#10;        psychoJS.window.callOnFlip(outlet.push_sample, {&quot;x&quot;: [40]});&amp;#10;    }&amp;#10;    correct_answer_for_this_trial = (current_number - 13);&amp;#10;    text_4digit_subject.text = current_number.toString();&amp;#10;    text_correct_answer_exp.text = `Ans: ${correct_answer_for_this_trial}`;&amp;#10;    text_exp_feedback.text = &quot;&quot;;&amp;#10;    text_subject_feedback.text = &quot;&quot;;&amp;#10;    progress_ratio = (time_left / duration);&amp;#10;    subject_current_bar_width = (progress_ratio * subject_bar_width);&amp;#10;    subject_progress_bar.width = subject_current_bar_width;&amp;#10;    subject_progress_bar.pos = [((subject_section_center_x - (subject_bar_width / 2)) + (subject_current_bar_width / 2)), (- 0.4)];&amp;#10;    experimenter_current_bar_width = (progress_ratio * experimenter_bar_width);&amp;#10;    experimenter_progress_bar.width = experimenter_current_bar_width;&amp;#10;    experimenter_progress_bar.pos = [((experimenter_section_center_x - (experimenter_bar_width / 2)) + (experimenter_current_bar_width / 2)), (- 0.4)];&amp;#10;    time_value_text.text = `${max(0, Number.parseInt(time_left))}s`;&amp;#10;    time_value_text_exp.text = `${max(0, Number.parseInt(time_left))}s`;&amp;#10;    responded_this_trial = false;&amp;#10;    experimenter_response_key = null;&amp;#10;    psychoJS.eventManager.clearEvents();&amp;#10;    while ((! responded_this_trial)) {&amp;#10;        current_input_phase_time = clock.getTime();&amp;#10;        time_elapsed_input = (current_input_phase_time - start_time);&amp;#10;        time_left_input = (duration - time_elapsed_input);&amp;#10;        if ((time_left_input &lt;= 0)) {&amp;#10;            time_left = 0;&amp;#10;            break;&amp;#10;        }&amp;#10;        progress_ratio_input = (time_left_input / duration);&amp;#10;        subject_current_bar_width_input = (progress_ratio_input * subject_bar_width);&amp;#10;        subject_progress_bar.width = subject_current_bar_width_input;&amp;#10;        subject_progress_bar.pos = [((subject_section_center_x - (subject_bar_width / 2)) + (subject_current_bar_width_input / 2)), (- 0.4)];&amp;#10;        experimenter_current_bar_width_input = (progress_ratio_input * experimenter_bar_width);&amp;#10;        experimenter_progress_bar.width = experimenter_current_bar_width_input;&amp;#10;        experimenter_progress_bar.pos = [((experimenter_section_center_x - (experimenter_bar_width / 2)) + (experimenter_current_bar_width_input / 2)), (- 0.4)];&amp;#10;        time_value_text.text = `${max(0, Number.parseInt(time_left_input))}s`;&amp;#10;        time_value_text_exp.text = `${max(0, Number.parseInt(time_left_input))}s`;&amp;#10;        text_4digit_subject.draw();&amp;#10;        text_instruction_subject.draw();&amp;#10;        if ((last_response_time &gt;= 3)) {&amp;#10;            speed_warning_subject.draw();&amp;#10;            speed_warning_experimenter.draw();&amp;#10;        }&amp;#10;        text_correct_answer_exp.draw();&amp;#10;        wrong_instruction.draw();&amp;#10;        separator_text.draw();&amp;#10;        correct_instruction.draw();&amp;#10;        divider_line.draw();&amp;#10;        subject_progress_bar.draw();&amp;#10;        experimenter_progress_bar.draw();&amp;#10;        time_left_text.draw();&amp;#10;        time_value_text.draw();&amp;#10;        time_left_text_exp.draw();&amp;#10;        time_value_text_exp.draw();&amp;#10;        psychoJS.window.flip();&amp;#10;        keys = psychoJS.eventManager.getKeys({&quot;keyList&quot;: [&quot;left&quot;, &quot;right&quot;, &quot;escape&quot;]});&amp;#10;        if (_pj.in_es6(&quot;escape&quot;, keys)) {&amp;#10;            if ((_pj.in_es6(&quot;outlet&quot;, globals()) &amp;&amp; (outlet !== null))) {&amp;#10;                outlet.push_sample({&quot;x&quot;: [2]});&amp;#10;            }&amp;#10;            core.quit();&amp;#10;        }&amp;#10;        if (_pj.in_es6(&quot;left&quot;, keys)) {&amp;#10;            experimenter_response_key = &quot;left&quot;;&amp;#10;            responded_this_trial = true;&amp;#10;        } else {&amp;#10;            if (_pj.in_es6(&quot;right&quot;, keys)) {&amp;#10;                experimenter_response_key = &quot;right&quot;;&amp;#10;                responded_this_trial = true;&amp;#10;            }&amp;#10;        }&amp;#10;        if ((time_left &lt;= 0)) {&amp;#10;            break;&amp;#10;        }&amp;#10;    }&amp;#10;    if ((time_left &lt;= 0)) {&amp;#10;        break;&amp;#10;    }&amp;#10;    response_time_for_this_trial = (clock.getTime() - trial_start_time);&amp;#10;    if ((experimenter_response_key === &quot;left&quot;)) {&amp;#10;        text_exp_feedback.text = &quot;WRONG&quot;;&amp;#10;        text_exp_feedback.color = &quot;red&quot;;&amp;#10;        text_subject_feedback.text = &quot;WRONG&quot;;&amp;#10;        text_subject_feedback.color = &quot;red&quot;;&amp;#10;        try {&amp;#10;            wrong_sound.stop();&amp;#10;            core.wait(0.01);&amp;#10;            wrong_sound.play();&amp;#10;        } catch(e) {&amp;#10;            console.log(`DEBUG: Error with sound: ${e}`);&amp;#10;        }&amp;#10;    } else {&amp;#10;        if ((experimenter_response_key === &quot;right&quot;)) {&amp;#10;            text_exp_feedback.text = &quot;CORRECT&quot;;&amp;#10;            text_exp_feedback.color = &quot;green&quot;;&amp;#10;            text_subject_feedback.text = &quot;CORRECT&quot;;&amp;#10;            text_subject_feedback.color = &quot;green&quot;;&amp;#10;        }&amp;#10;    }&amp;#10;    text_4digit_subject.draw();&amp;#10;    text_instruction_subject.draw();&amp;#10;    if ((last_response_time &gt;= 3)) {&amp;#10;        speed_warning_subject.draw();&amp;#10;        speed_warning_experimenter.draw();&amp;#10;    }&amp;#10;    text_correct_answer_exp.draw();&amp;#10;    wrong_instruction.draw();&amp;#10;    separator_text.draw();&amp;#10;    correct_instruction.draw();&amp;#10;    text_exp_feedback.draw();&amp;#10;    text_subject_feedback.draw();&amp;#10;    divider_line.draw();&amp;#10;    subject_progress_bar.draw();&amp;#10;    experimenter_progress_bar.draw();&amp;#10;    time_left_text.draw();&amp;#10;    time_value_text.draw();&amp;#10;    time_left_text_exp.draw();&amp;#10;    time_value_text_exp.draw();&amp;#10;    psychoJS.window.flip();&amp;#10;    core.wait(0.75);&amp;#10;    current_number = correct_answer_for_this_trial;&amp;#10;    last_response_time = response_time_for_this_trial;&amp;#10;}&amp;#10;if ((_pj.in_es6(&quot;outlet&quot;, globals()) &amp;&amp; (outlet !== null))) {&amp;#10;    outlet.push_sample({&quot;x&quot;: [2]});&amp;#10;}&amp;#10;if (_pj.in_es6(&quot;wrong_sound&quot;, globals())) {&amp;#10;    try {&amp;#10;        wrong_sound.stop();&amp;#10;        core.wait(0.01);&amp;#10;    } catch(e) {&amp;#10;        console.log(`Error stopping wrong_sound: ${e}`);&amp;#10;    }&amp;#10;}&amp;#10;gc.collect();&amp;#10;psychoJS.window.flip();&amp;#10;core.wait(2.0);&amp;#10;" valType="extendedCode" updates="constant" name="Begin JS Routine"/>
        <Param val="import random&amp;#10;&amp;#10;# Initialize variables&amp;#10;current_number = random.randint(1000, 1999)&amp;#10;duration = stress_duration  # Total duration from Builder variable&amp;#10;last_response_time = 0&amp;#10;clock = core.Clock()&amp;#10;start_time = clock.getTime()&amp;#10;&amp;#10;# --- Define Screen Layout Parameters ---&amp;#10;screen_width = 2.0  # Width from -1 to 1 in normalized units&amp;#10;subject_proportion = 2/3  # Subject gets 2/3 of the screen&amp;#10;experimenter_proportion = 1/3  # Experimenter gets 1/3 of the screen&amp;#10;&amp;#10;# Calculate section widths and positions&amp;#10;subject_width = subject_proportion * screen_width  # 4/3 normalized units&amp;#10;experimenter_width = experimenter_proportion * screen_width  # 2/3 normalized units&amp;#10;divider_x_pos = -1 + subject_width  # 1/3 normalized units&amp;#10;subject_section_center_x = -1 + (subject_width / 2)  # -1/3 normalized units&amp;#10;experimenter_section_center_x = divider_x_pos + (experimenter_width / 2)  # 2/3 normalized units&amp;#10;&amp;#10;# Progress bars should take up 75% of their respective section width&amp;#10;subject_bar_width = 0.75 * subject_width  # 75% of subject section width&amp;#10;experimenter_bar_width = 0.75 * experimenter_width  # 75% of experimenter section width&amp;#10;&amp;#10;# --- Create Stimuli ---&amp;#10;divider_line = visual.Line(&amp;#10;    win,&amp;#10;    start=(divider_x_pos, 1),&amp;#10;    end=(divider_x_pos, -1),&amp;#10;    lineColor=&quot;white&quot;,&amp;#10;    lineWidth=5&amp;#10;)&amp;#10;&amp;#10;text_4digit_subject = visual.TextStim(&amp;#10;    win, &amp;#10;    text=str(current_number), &amp;#10;    pos=(subject_section_center_x, 0.2), &amp;#10;    height=0.1, &amp;#10;    color=&quot;white&quot;,&amp;#10;    font='Arial'&amp;#10;)&amp;#10;&amp;#10;text_instruction_subject = visual.TextStim(&amp;#10;    win, &amp;#10;    text=&quot;Subtract 13&quot;, &amp;#10;    pos=(subject_section_center_x, 0.1), &amp;#10;    height=0.05, &amp;#10;    color=&quot;white&quot;,&amp;#10;    font='Arial'&amp;#10;)&amp;#10;&amp;#10;speed_warning_subject = visual.TextStim(&amp;#10;    win, &amp;#10;    text=&quot;FASTER! You're Slow!&quot;, &amp;#10;    pos=(subject_section_center_x, 0.3),&amp;#10;    height=0.08, &amp;#10;    color=&quot;red&quot;, &amp;#10;    bold=True, &amp;#10;    italic=False, &amp;#10;    font='Arial'&amp;#10;)&amp;#10;&amp;#10;# New experimenter speed warning text&amp;#10;speed_warning_experimenter = visual.TextStim(&amp;#10;    win, &amp;#10;    text=&quot;say: FASTER!!&quot;, &amp;#10;    pos=(experimenter_section_center_x, 0.3), &amp;#10;    height=0.08, &amp;#10;    color=&quot;red&quot;, &amp;#10;    bold=True, &amp;#10;    italic=False, &amp;#10;    font='Arial'&amp;#10;)&amp;#10;&amp;#10;text_correct_answer_exp = visual.TextStim(&amp;#10;    win, &amp;#10;    text=&quot;&quot;,&amp;#10;    pos=(experimenter_section_center_x, 0.2), &amp;#10;    height=0.1, &amp;#10;    color=&quot;white&quot;,&amp;#10;    font='Arial'&amp;#10;)&amp;#10;&amp;#10;# Create instruction text with arrows&amp;#10;wrong_instruction = visual.TextStim(&amp;#10;    win, &amp;#10;    text=&quot;← Wrong&quot;, &amp;#10;    pos=(experimenter_section_center_x - 0.08, 0.1),&amp;#10;    height=0.03,&amp;#10;    color=&quot;red&quot;,&amp;#10;    font='Arial',&amp;#10;    alignText='right',&amp;#10;    wrapWidth=experimenter_bar_width * 0.3&amp;#10;)&amp;#10;&amp;#10;separator_text = visual.TextStim(&amp;#10;    win, &amp;#10;    text=&quot;|&quot;, &amp;#10;    pos=(experimenter_section_center_x, 0.1),&amp;#10;    height=0.03,&amp;#10;    color=&quot;white&quot;,&amp;#10;    font='Arial',&amp;#10;    alignText='center'&amp;#10;)&amp;#10;&amp;#10;correct_instruction = visual.TextStim(&amp;#10;    win, &amp;#10;    text=&quot;Correct →&quot;, &amp;#10;    pos=(experimenter_section_center_x + 0.08, 0.1),&amp;#10;    height=0.03,&amp;#10;    color=&quot;green&quot;,&amp;#10;    font='Arial',&amp;#10;    alignText='left',&amp;#10;    wrapWidth=experimenter_bar_width * 0.3&amp;#10;)&amp;#10;&amp;#10;# Feedback text for experimenter and participant sides&amp;#10;text_exp_feedback = visual.TextStim(&amp;#10;    win, &amp;#10;    text=&quot;&quot;, &amp;#10;    pos=(experimenter_section_center_x, 0.0),&amp;#10;    height=0.07, &amp;#10;    color=&quot;white&quot;,&amp;#10;    font='Arial'&amp;#10;)&amp;#10;&amp;#10;text_subject_feedback = visual.TextStim(&amp;#10;    win, &amp;#10;    text=&quot;&quot;, &amp;#10;    pos=(subject_section_center_x, 0.0), &amp;#10;    height=0.07, &amp;#10;    color=&quot;white&quot;,&amp;#10;    font='Arial'&amp;#10;)&amp;#10;&amp;#10;# Subject progress bar (left 2/3 section)&amp;#10;subject_progress_bar = visual.Rect(&amp;#10;    win, &amp;#10;    width=subject_bar_width, &amp;#10;    height=0.05, &amp;#10;    fillColor=&quot;white&quot;, &amp;#10;    lineColor=None, &amp;#10;    pos=(subject_section_center_x, -0.4)&amp;#10;)&amp;#10;&amp;#10;# Experimenter progress bar (right 1/3 section)&amp;#10;experimenter_progress_bar = visual.Rect(&amp;#10;    win, &amp;#10;    width=experimenter_bar_width, &amp;#10;    height=0.05, &amp;#10;    fillColor=&quot;white&quot;, &amp;#10;    lineColor=None, &amp;#10;    pos=(experimenter_section_center_x, -0.4)&amp;#10;)&amp;#10;&amp;#10;# Time text for subject section&amp;#10;time_left_text = visual.TextStim(&amp;#10;    win, &amp;#10;    text=&quot;Time left:&quot;, &amp;#10;    pos=(subject_section_center_x - (subject_bar_width / 2) - 0.05, -0.45), &amp;#10;    height=0.04, &amp;#10;    color=&quot;white&quot;, &amp;#10;    alignHoriz='left',&amp;#10;    font='Arial'&amp;#10;)&amp;#10;&amp;#10;time_value_text = visual.TextStim(&amp;#10;    win,&amp;#10;    text=&quot;&quot;,&amp;#10;    pos=(subject_section_center_x - (subject_bar_width / 2) + 0.15, -0.45),&amp;#10;    height=0.04,&amp;#10;    color=&quot;white&quot;,&amp;#10;    alignHoriz='left',&amp;#10;    font='Arial'&amp;#10;)&amp;#10;&amp;#10;# Time text for experimenter section&amp;#10;time_left_text_exp = visual.TextStim(&amp;#10;    win, &amp;#10;    text=&quot;Time left:&quot;, &amp;#10;    pos=(experimenter_section_center_x - (experimenter_bar_width / 2) - 0.05, -0.45), &amp;#10;    height=0.04, &amp;#10;    color=&quot;white&quot;, &amp;#10;    alignHoriz='left',&amp;#10;    font='Arial'&amp;#10;)&amp;#10;&amp;#10;time_value_text_exp = visual.TextStim(&amp;#10;    win,&amp;#10;    text=&quot;&quot;,&amp;#10;    pos=(experimenter_section_center_x - (experimenter_bar_width / 2) + 0.15, -0.45),&amp;#10;    height=0.04,&amp;#10;    color=&quot;white&quot;,&amp;#10;    alignHoriz='left',&amp;#10;    font='Arial'&amp;#10;)&amp;#10;&amp;#10;# Main experiment loop&amp;#10;while True:&amp;#10;    current_loop_time = clock.getTime()&amp;#10;    time_elapsed = current_loop_time - start_time&amp;#10;    time_left = duration - time_elapsed&amp;#10;    &amp;#10;    if time_left &lt;= 0:&amp;#10;        time_left = 0&amp;#10;        break&amp;#10;    &amp;#10;    trial_start_time = current_loop_time&amp;#10;    &amp;#10;    if 'outlet' in globals() and outlet is not None:&amp;#10;        win.callOnFlip(outlet.push_sample, x=[40])&amp;#10;    &amp;#10;    correct_answer_for_this_trial = current_number - 13&amp;#10;    &amp;#10;    text_4digit_subject.text = str(current_number)&amp;#10;    text_correct_answer_exp.text = f&quot;Ans: {correct_answer_for_this_trial}&quot;&amp;#10;    text_exp_feedback.text = &quot;&quot;&amp;#10;    text_subject_feedback.text = &quot;&quot;  # Clear feedback text&amp;#10;    &amp;#10;    # Calculate progress ratio and update progress bars&amp;#10;    progress_ratio = time_left / duration&amp;#10;    &amp;#10;    # Update subject progress bar&amp;#10;    subject_current_bar_width = progress_ratio * subject_bar_width&amp;#10;    subject_progress_bar.width = subject_current_bar_width&amp;#10;    subject_progress_bar.pos = (&amp;#10;        subject_section_center_x - (subject_bar_width / 2) + (subject_current_bar_width / 2),&amp;#10;        -0.4&amp;#10;    )&amp;#10;    &amp;#10;    # Update experimenter progress bar&amp;#10;    experimenter_current_bar_width = progress_ratio * experimenter_bar_width&amp;#10;    experimenter_progress_bar.width = experimenter_current_bar_width&amp;#10;    experimenter_progress_bar.pos = (&amp;#10;        experimenter_section_center_x - (experimenter_bar_width / 2) + (experimenter_current_bar_width / 2),&amp;#10;        -0.4&amp;#10;    )&amp;#10;    &amp;#10;    # Update time text&amp;#10;    time_value_text.text = f&quot;{max(0, int(time_left))}s&quot;&amp;#10;    time_value_text_exp.text = f&quot;{max(0, int(time_left))}s&quot;&amp;#10;    &amp;#10;    responded_this_trial = False&amp;#10;    experimenter_response_key = None&amp;#10;    event.clearEvents()&amp;#10;    &amp;#10;    while not responded_this_trial:&amp;#10;        current_input_phase_time = clock.getTime()&amp;#10;        time_elapsed_input = current_input_phase_time - start_time&amp;#10;        time_left_input = duration - time_elapsed_input&amp;#10;        &amp;#10;        if time_left_input &lt;= 0:&amp;#10;            time_left = 0&amp;#10;            break&amp;#10;        &amp;#10;        # Calculate progress ratio for this frame&amp;#10;        progress_ratio_input = time_left_input / duration&amp;#10;        &amp;#10;        # Update subject progress bar&amp;#10;        subject_current_bar_width_input = progress_ratio_input * subject_bar_width&amp;#10;        subject_progress_bar.width = subject_current_bar_width_input&amp;#10;        subject_progress_bar.pos = (&amp;#10;            subject_section_center_x - (subject_bar_width / 2) + (subject_current_bar_width_input / 2),&amp;#10;            -0.4&amp;#10;        )&amp;#10;        &amp;#10;        # Update experimenter progress bar&amp;#10;        experimenter_current_bar_width_input = progress_ratio_input * experimenter_bar_width&amp;#10;        experimenter_progress_bar.width = experimenter_current_bar_width_input&amp;#10;        experimenter_progress_bar.pos = (&amp;#10;            experimenter_section_center_x - (experimenter_bar_width / 2) + (experimenter_current_bar_width_input / 2),&amp;#10;            -0.4&amp;#10;        )&amp;#10;        &amp;#10;        # Update time text for both sections&amp;#10;        time_value_text.text = f&quot;{max(0, int(time_left_input))}s&quot;&amp;#10;        time_value_text_exp.text = f&quot;{max(0, int(time_left_input))}s&quot;&amp;#10;        &amp;#10;        # Draw all components&amp;#10;        text_4digit_subject.draw()&amp;#10;        text_instruction_subject.draw()&amp;#10;        &amp;#10;        # Show speed warnings if needed&amp;#10;        if last_response_time &gt;= 3:&amp;#10;            speed_warning_subject.draw()&amp;#10;            speed_warning_experimenter.draw()  # Draw speed warning for experimenter&amp;#10;        &amp;#10;        text_correct_answer_exp.draw()&amp;#10;        &amp;#10;        # Draw the instruction text with arrows&amp;#10;        wrong_instruction.draw()&amp;#10;        separator_text.draw()&amp;#10;        correct_instruction.draw()&amp;#10;        &amp;#10;        divider_line.draw()&amp;#10;        subject_progress_bar.draw()&amp;#10;        experimenter_progress_bar.draw()&amp;#10;        time_left_text.draw()&amp;#10;        time_value_text.draw()&amp;#10;        time_left_text_exp.draw()&amp;#10;        time_value_text_exp.draw()&amp;#10;        &amp;#10;        win.flip()&amp;#10;        &amp;#10;        keys = event.getKeys(keyList=['left', 'right', 'escape'])&amp;#10;        if 'escape' in keys:&amp;#10;            if 'outlet' in globals() and outlet is not None:&amp;#10;                outlet.push_sample(x=[2])&amp;#10;            core.quit()&amp;#10;        &amp;#10;        if 'left' in keys:&amp;#10;            experimenter_response_key = 'left'&amp;#10;            responded_this_trial = True&amp;#10;        elif 'right' in keys:&amp;#10;            experimenter_response_key = 'right'&amp;#10;            responded_this_trial = True&amp;#10;        &amp;#10;        if time_left &lt;= 0:&amp;#10;            break&amp;#10;    &amp;#10;    if time_left &lt;= 0:&amp;#10;        break&amp;#10;    &amp;#10;    response_time_for_this_trial = clock.getTime() - trial_start_time&amp;#10;    &amp;#10;    if experimenter_response_key == 'left':&amp;#10;        # Update feedback for both sections&amp;#10;        text_exp_feedback.text = &quot;WRONG&quot;&amp;#10;        text_exp_feedback.color = &quot;red&quot;&amp;#10;        text_subject_feedback.text = &quot;WRONG&quot;&amp;#10;        text_subject_feedback.color = &quot;red&quot;&amp;#10;        &amp;#10;        # Stop sound before playing it again&amp;#10;        try:&amp;#10;            wrong_sound.stop()  # This is critical - must stop before playing again&amp;#10;            core.wait(0.01)     # Brief pause to let the stop take effect&amp;#10;            wrong_sound.play()  # Now play the sound&amp;#10;        except Exception as e:&amp;#10;            print(f&quot;DEBUG: Error with sound: {e}&quot;)       &amp;#10;    elif experimenter_response_key == 'right':&amp;#10;        # Update feedback for both sections - CORRECT&amp;#10;        text_exp_feedback.text = &quot;CORRECT&quot;&amp;#10;        text_exp_feedback.color = &quot;green&quot;&amp;#10;        text_subject_feedback.text = &quot;CORRECT&quot;&amp;#10;        text_subject_feedback.color = &quot;green&quot;&amp;#10;    &amp;#10;    # Draw final state of this trial&amp;#10;    text_4digit_subject.draw()&amp;#10;    text_instruction_subject.draw()&amp;#10;    if last_response_time &gt;= 3:&amp;#10;        speed_warning_subject.draw()&amp;#10;        speed_warning_experimenter.draw()&amp;#10;        &amp;#10;    text_correct_answer_exp.draw()&amp;#10;    &amp;#10;    # Draw the instruction text with arrows&amp;#10;    wrong_instruction.draw()&amp;#10;    separator_text.draw()&amp;#10;    correct_instruction.draw()&amp;#10;    &amp;#10;    text_exp_feedback.draw()&amp;#10;    text_subject_feedback.draw()&amp;#10;    &amp;#10;    divider_line.draw()&amp;#10;    subject_progress_bar.draw()&amp;#10;    experimenter_progress_bar.draw()&amp;#10;    time_left_text.draw()&amp;#10;    time_value_text.draw()&amp;#10;    time_left_text_exp.draw()&amp;#10;    time_value_text_exp.draw()&amp;#10;    &amp;#10;    win.flip()&amp;#10;    core.wait(0.75)&amp;#10;    &amp;#10;    current_number = correct_answer_for_this_trial&amp;#10;    last_response_time = response_time_for_this_trial&amp;#10;&amp;#10;# --- End of Experiment ---&amp;#10;if 'outlet' in globals() and outlet is not None:&amp;#10;    outlet.push_sample(x=[2])&amp;#10;&amp;#10;# At the end of your arithmetic task&amp;#10;if 'wrong_sound' in globals():&amp;#10;    try:&amp;#10;        # Stop the sound but keep the reference for later routines&amp;#10;        wrong_sound.stop()&amp;#10;        # Ensure it's fully stopped&amp;#10;        core.wait(0.01)&amp;#10;    except Exception as e:&amp;#10;        print(f&quot;Error stopping wrong_sound: {e}&quot;)&amp;#10;        pass&amp;#10;&amp;#10;# At the end of your arithmetic task&amp;#10;import gc&amp;#10;gc.collect()  # Force garbage collection&amp;#10;&amp;#10;# Just clear the screen and wait&amp;#10;win.flip()&amp;#10;core.wait(2.0)&amp;#10;" valType="extendedCode" updates="constant" name="Begin Routine"/>
        <Param val="Auto-&gt;JS" valType="str" updates="None" name="Code Type"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each JS Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Routine"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="code_arithmetic" valType="code" updates="None" name="name"/>
      </CodeComponent>
    </Routine>
    <Routine name="_06_stroop_instr">
      <RoutineSettingsComponent name="_06_stroop_instr" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="_06_stroop_instr" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <TextComponent name="stroop_instr" plugin="None">
        <Param val="white" valType="color" updates="constant" name="color"/>
        <Param val="rgb" valType="str" updates="constant" name="colorSpace"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="None" valType="str" updates="constant" name="flip"/>
        <Param val="Arial" valType="str" updates="constant" name="font"/>
        <Param val="LTR" valType="str" updates="None" name="languageStyle"/>
        <Param val="0.03" valType="num" updates="constant" name="letterHeight"/>
        <Param val="stroop_instr" valType="code" updates="None" name="name"/>
        <Param val="" valType="num" updates="constant" name="opacity"/>
        <Param val="0" valType="num" updates="constant" name="ori"/>
        <Param val="(0, 0)" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="Stroop task&amp;#10;Use the arrow keys to identify the text color. Ignore what the word says.&amp;#10;&amp;#10;Press F1 to start.&amp;#10;" valType="str" updates="constant" name="text"/>
        <Param val="from exp settings" valType="str" updates="None" name="units"/>
        <Param val="" valType="num" updates="constant" name="wrapWidth"/>
      </TextComponent>
      <KeyboardComponent name="block_resp" plugin="None">
        <Param val="'f1'" valType="list" updates="constant" name="allowedKeys"/>
        <Param val="" valType="str" updates="constant" name="correctAns"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="True" valType="bool" updates="constant" name="discard previous"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="True" valType="bool" updates="constant" name="forceEndRoutine"/>
        <Param val="block_resp" valType="code" updates="None" name="name"/>
        <Param val="press" valType="str" updates="constant" name="registerOn"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="last key" valType="str" updates="constant" name="store"/>
        <Param val="False" valType="bool" updates="constant" name="storeCorrect"/>
        <Param val="True" valType="bool" updates="constant" name="syncScreenRefresh"/>
      </KeyboardComponent>
      <TextComponent name="instrText_4" plugin="None">
        <Param val="white" valType="str" updates="constant" name="color"/>
        <Param val="rgb" valType="str" updates="constant" name="colorSpace"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="" valType="str" updates="constant" name="flip"/>
        <Param val="Arial" valType="str" updates="constant" name="font"/>
        <Param val="LTR" valType="str" updates="None" name="languageStyle"/>
        <Param val="0.03" valType="code" updates="constant" name="letterHeight"/>
        <Param val="instrText_4" valType="code" updates="None" name="name"/>
        <Param val="1" valType="code" updates="constant" name="opacity"/>
        <Param val="0" valType="code" updates="constant" name="ori"/>
        <Param val="(0, 0.4)" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="red = ←, green = ↓, blue = → &amp;#10;Identify the COLOR of the text. Ignore what the word says" valType="str" updates="constant" name="text"/>
        <Param val="height" valType="str" updates="None" name="units"/>
        <Param val="" valType="code" updates="constant" name="wrapWidth"/>
      </TextComponent>
      <CodeComponent name="t_begin_4" plugin="None">
        <Param val="" valType="extendedCode" updates="constant" name="Before Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Before JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin Routine"/>
        <Param val="Auto-&gt;JS" valType="str" updates="None" name="Code Type"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each JS Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Experiment"/>
        <Param val="outlet.push_sample({&quot;x&quot;: [1]});&amp;#10;" valType="extendedCode" updates="constant" name="End JS Routine"/>
        <Param val="outlet.push_sample(x=[1])" valType="extendedCode" updates="constant" name="End Routine"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="t_begin_4" valType="code" updates="None" name="name"/>
      </CodeComponent>
    </Routine>
    <Routine name="_06_stroop_trial">
      <RoutineSettingsComponent name="_06_stroop_trial" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="_06_stroop_trial" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <CodeComponent name="t_trial_stroop" plugin="None">
        <Param val="" valType="extendedCode" updates="constant" name="Before Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Before JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Experiment"/>
        <Param val="outlet.push_sample({&quot;x&quot;: [60]});&amp;#10;" valType="extendedCode" updates="constant" name="Begin JS Routine"/>
        <Param val="outlet.push_sample(x=[60])" valType="extendedCode" updates="constant" name="Begin Routine"/>
        <Param val="Auto-&gt;JS" valType="str" updates="None" name="Code Type"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each JS Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Routine"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="t_trial_stroop" valType="code" updates="None" name="name"/>
      </CodeComponent>
      <TextComponent name="stim" plugin="None">
        <Param val="$wordColor" valType="str" updates="set every repeat" name="color"/>
        <Param val="rgb" valType="str" updates="constant" name="colorSpace"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="" valType="str" updates="constant" name="flip"/>
        <Param val="Arial" valType="str" updates="constant" name="font"/>
        <Param val="LTR" valType="str" updates="None" name="languageStyle"/>
        <Param val="0.15" valType="code" updates="constant" name="letterHeight"/>
        <Param val="stim" valType="code" updates="None" name="name"/>
        <Param val="1" valType="code" updates="constant" name="opacity"/>
        <Param val="0" valType="code" updates="constant" name="ori"/>
        <Param val="(0, 0)" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.5" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="1.0" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="$word" valType="str" updates="set every repeat" name="text"/>
        <Param val="height" valType="str" updates="None" name="units"/>
        <Param val="" valType="code" updates="constant" name="wrapWidth"/>
      </TextComponent>
      <KeyboardComponent name="resp" plugin="None">
        <Param val="'left','down','right'" valType="list" updates="constant" name="allowedKeys"/>
        <Param val="$corrAns" valType="str" updates="constant" name="correctAns"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="True" valType="bool" updates="constant" name="discard previous"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="True" valType="bool" updates="constant" name="forceEndRoutine"/>
        <Param val="resp" valType="code" updates="None" name="name"/>
        <Param val="press" valType="str" updates="constant" name="registerOn"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val=".5" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="1.0" valType="code" updates="constant" name="stopVal"/>
        <Param val="last key" valType="str" updates="constant" name="store"/>
        <Param val="True" valType="bool" updates="constant" name="storeCorrect"/>
        <Param val="True" valType="bool" updates="constant" name="syncScreenRefresh"/>
      </KeyboardComponent>
      <TextboxComponent name="trial_counter" plugin="None">
        <Param val="center" valType="str" updates="constant" name="alignment"/>
        <Param val="center" valType="str" updates="constant" name="anchor"/>
        <Param val="True" valType="bool" updates="constant" name="autoLog"/>
        <Param val="False" valType="bool" updates="constant" name="bold"/>
        <Param val="black" valType="color" updates="constant" name="borderColor"/>
        <Param val="2" valType="num" updates="constant" name="borderWidth"/>
        <Param val="black" valType="color" updates="constant" name="color"/>
        <Param val="rgb" valType="str" updates="constant" name="colorSpace"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="False" valType="bool" updates="constant" name="editable"/>
        <Param val="white" valType="color" updates="constant" name="fillColor"/>
        <Param val="False" valType="bool" updates="constant" name="flipHoriz"/>
        <Param val="False" valType="bool" updates="constant" name="flipVert"/>
        <Param val="Arial" valType="str" updates="constant" name="font"/>
        <Param val="False" valType="bool" updates="constant" name="italic"/>
        <Param val="LTR" valType="str" updates="None" name="languageStyle"/>
        <Param val="0.05" valType="num" updates="constant" name="letterHeight"/>
        <Param val="1.0" valType="num" updates="constant" name="lineSpacing"/>
        <Param val="trial_counter" valType="code" updates="None" name="name"/>
        <Param val="0.8" valType="num" updates="constant" name="opacity"/>
        <Param val="0" valType="num" updates="constant" name="ori"/>
        <Param val="visible" valType="str" updates="constant" name="overflow"/>
        <Param val="0" valType="num" updates="constant" name="padding"/>
        <Param val="Type here..." valType="str" updates="constant" name="placeholder"/>
        <Param val="(0, -.4)" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="(0.5, 0.1)" valType="list" updates="constant" name="size"/>
        <Param val="" valType="list" updates="constant" name="speechPoint"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="1.5" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="$'Trial ' + str(trials.thisN+1) +'/' +str(trials.nTotal)" valType="str" updates="set every repeat" name="text"/>
        <Param val="from exp settings" valType="str" updates="None" name="units"/>
      </TextboxComponent>
      <TextComponent name="instrText_2" plugin="None">
        <Param val="white" valType="str" updates="constant" name="color"/>
        <Param val="rgb" valType="str" updates="constant" name="colorSpace"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="" valType="str" updates="constant" name="flip"/>
        <Param val="Arial" valType="str" updates="constant" name="font"/>
        <Param val="LTR" valType="str" updates="None" name="languageStyle"/>
        <Param val="0.03" valType="code" updates="constant" name="letterHeight"/>
        <Param val="instrText_2" valType="code" updates="None" name="name"/>
        <Param val="1" valType="code" updates="constant" name="opacity"/>
        <Param val="0" valType="code" updates="constant" name="ori"/>
        <Param val="(0, 0.4)" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="1.5" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="red = ←, green = ↓, blue = → &amp;#10;Identify the COLOR of the text. Ignore what the word says" valType="str" updates="constant" name="text"/>
        <Param val="height" valType="str" updates="None" name="units"/>
        <Param val="" valType="code" updates="constant" name="wrapWidth"/>
      </TextComponent>
    </Routine>
    <Routine name="_06_stroop_feedback">
      <RoutineSettingsComponent name="_06_stroop_feedback" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="_06_stroop_feedback" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <CodeComponent name="code_2" plugin="None">
        <Param val="" valType="extendedCode" updates="constant" name="Before Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Before JS Experiment"/>
        <Param val="english_accuracy = []&amp;#10;#maori_accuracy = []" valType="extendedCode" updates="constant" name="Begin Experiment"/>
        <Param val="english_accuracy = [];&amp;#10;" valType="extendedCode" updates="constant" name="Begin JS Experiment"/>
        <Param val="if (resp.corr) {&amp;#10;    fb = &quot;Correct!&quot;;&amp;#10;    fbcol = &quot;green&quot;;&amp;#10;} else {&amp;#10;    fb = &quot;Incorrect&quot;;&amp;#10;    fbcol = &quot;red&quot;;&amp;#10;    try {&amp;#10;        wrong_sound.stop();&amp;#10;        core.wait(0.01);&amp;#10;        wrong_sound.play();&amp;#10;    } catch(e) {&amp;#10;        console.log(`DEBUG: Error with sound: ${e}`);&amp;#10;    }&amp;#10;}&amp;#10;english_accuracy.push(resp.corr);&amp;#10;" valType="extendedCode" updates="constant" name="Begin JS Routine"/>
        <Param val="if resp.corr:&amp;#10;    fb = 'Correct!'&amp;#10;    fbcol = 'green'&amp;#10;else:&amp;#10;    fb = 'Incorrect'&amp;#10;    fbcol = 'red'&amp;#10;    #wrong_sound.play()  # Play the wrong answer sound&amp;#10;    try:&amp;#10;        wrong_sound.stop()  # This is critical - must stop before playing again&amp;#10;        core.wait(0.01)     # Brief pause to let the stop take effect&amp;#10;        wrong_sound.play()  # Now play the sound&amp;#10;    except Exception as e:&amp;#10;        print(f&quot;DEBUG: Error with sound: {e}&quot;)      &amp;#10;# track accuracy for each condition by adding a 1 or 0 to a list&amp;#10;english_accuracy.append(resp.corr)&amp;#10;" valType="extendedCode" updates="constant" name="Begin Routine"/>
        <Param val="Auto-&gt;JS" valType="str" updates="None" name="Code Type"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each JS Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Routine"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="code_2" valType="code" updates="None" name="name"/>
      </CodeComponent>
      <TextboxComponent name="fbtxt" plugin="None">
        <Param val="center" valType="str" updates="constant" name="alignment"/>
        <Param val="center" valType="str" updates="constant" name="anchor"/>
        <Param val="True" valType="bool" updates="constant" name="autoLog"/>
        <Param val="False" valType="bool" updates="constant" name="bold"/>
        <Param val="None" valType="color" updates="constant" name="borderColor"/>
        <Param val="2" valType="num" updates="constant" name="borderWidth"/>
        <Param val="$fbcol" valType="color" updates="set every repeat" name="color"/>
        <Param val="rgb" valType="str" updates="constant" name="colorSpace"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="False" valType="bool" updates="constant" name="editable"/>
        <Param val="None" valType="color" updates="constant" name="fillColor"/>
        <Param val="False" valType="bool" updates="constant" name="flipHoriz"/>
        <Param val="False" valType="bool" updates="constant" name="flipVert"/>
        <Param val="Arial" valType="str" updates="constant" name="font"/>
        <Param val="False" valType="bool" updates="constant" name="italic"/>
        <Param val="LTR" valType="str" updates="None" name="languageStyle"/>
        <Param val="0.05" valType="num" updates="constant" name="letterHeight"/>
        <Param val="1.0" valType="num" updates="constant" name="lineSpacing"/>
        <Param val="fbtxt" valType="code" updates="None" name="name"/>
        <Param val="" valType="num" updates="constant" name="opacity"/>
        <Param val="0" valType="num" updates="constant" name="ori"/>
        <Param val="visible" valType="str" updates="constant" name="overflow"/>
        <Param val="0" valType="num" updates="constant" name="padding"/>
        <Param val="Type here..." valType="str" updates="constant" name="placeholder"/>
        <Param val="(0, 0)" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="(0.5, 0.5)" valType="list" updates="constant" name="size"/>
        <Param val="" valType="list" updates="constant" name="speechPoint"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="0.5" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="$fb" valType="str" updates="set every repeat" name="text"/>
        <Param val="from exp settings" valType="str" updates="None" name="units"/>
      </TextboxComponent>
      <TextboxComponent name="trial_counter_2" plugin="None">
        <Param val="center" valType="str" updates="constant" name="alignment"/>
        <Param val="center" valType="str" updates="constant" name="anchor"/>
        <Param val="True" valType="bool" updates="constant" name="autoLog"/>
        <Param val="False" valType="bool" updates="constant" name="bold"/>
        <Param val="black" valType="color" updates="constant" name="borderColor"/>
        <Param val="2" valType="num" updates="constant" name="borderWidth"/>
        <Param val="black" valType="color" updates="constant" name="color"/>
        <Param val="rgb" valType="str" updates="constant" name="colorSpace"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="False" valType="bool" updates="constant" name="editable"/>
        <Param val="white" valType="color" updates="constant" name="fillColor"/>
        <Param val="False" valType="bool" updates="constant" name="flipHoriz"/>
        <Param val="False" valType="bool" updates="constant" name="flipVert"/>
        <Param val="Arial" valType="str" updates="constant" name="font"/>
        <Param val="False" valType="bool" updates="constant" name="italic"/>
        <Param val="LTR" valType="str" updates="None" name="languageStyle"/>
        <Param val="0.05" valType="num" updates="constant" name="letterHeight"/>
        <Param val="1.0" valType="num" updates="constant" name="lineSpacing"/>
        <Param val="trial_counter_2" valType="code" updates="None" name="name"/>
        <Param val="0.8" valType="num" updates="constant" name="opacity"/>
        <Param val="0" valType="num" updates="constant" name="ori"/>
        <Param val="visible" valType="str" updates="constant" name="overflow"/>
        <Param val="0" valType="num" updates="constant" name="padding"/>
        <Param val="Type here..." valType="str" updates="constant" name="placeholder"/>
        <Param val="(0, -.4)" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="(0.5, 0.1)" valType="list" updates="constant" name="size"/>
        <Param val="" valType="list" updates="constant" name="speechPoint"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="0.5" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="$'Trial ' + str(trials.thisN+1) +'/' +str(trials.nTotal)" valType="str" updates="set every repeat" name="text"/>
        <Param val="from exp settings" valType="str" updates="None" name="units"/>
      </TextboxComponent>
      <TextComponent name="instrText_3" plugin="None">
        <Param val="white" valType="str" updates="constant" name="color"/>
        <Param val="rgb" valType="str" updates="constant" name="colorSpace"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="" valType="str" updates="constant" name="flip"/>
        <Param val="Arial" valType="str" updates="constant" name="font"/>
        <Param val="LTR" valType="str" updates="None" name="languageStyle"/>
        <Param val="0.03" valType="code" updates="constant" name="letterHeight"/>
        <Param val="instrText_3" valType="code" updates="None" name="name"/>
        <Param val="1" valType="code" updates="constant" name="opacity"/>
        <Param val="0" valType="code" updates="constant" name="ori"/>
        <Param val="(0, 0.4)" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="0.5" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="red = ←, green = ↓, blue = → &amp;#10;Identify the COLOR of the text. Ignore what the word says" valType="str" updates="constant" name="text"/>
        <Param val="height" valType="str" updates="None" name="units"/>
        <Param val="" valType="code" updates="constant" name="wrapWidth"/>
      </TextComponent>
    </Routine>
    <Routine name="Experiment_Setup">
      <RoutineSettingsComponent name="Experiment_Setup" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="Experiment_Setup" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <CodeComponent name="load_data" plugin="None">
        <Param val="" valType="extendedCode" updates="constant" name="Before Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Before JS Experiment"/>
        <Param val="import pandas as pd&amp;#10;from psychopy import core, visual, event, sound&amp;#10;&amp;#10;# Read the Excel file&amp;#10;schedule_data = pd.read_excel('participant_schedule_vis.xlsx')&amp;#10;&amp;#10;# Get current participant and session numbers&amp;#10;participant_num = int(expInfo['participant'])&amp;#10;session_num = int(expInfo['session'])&amp;#10;&amp;#10;# Find the row for the current participant and session&amp;#10;current_row = schedule_data[(schedule_data['participant'] == participant_num) &amp; &amp;#10;                            (schedule_data['session'] == session_num)]&amp;#10;&amp;#10;if not current_row.empty:&amp;#10;    # Get video filenames for the current participant and session&amp;#10;    interview_video = current_row['interview_video'].values[0]&amp;#10;    breathing_video = current_row['breathing_protocol'].values[0]&amp;#10;    &amp;#10;    # Store these in expInfo for easier access&amp;#10;    expInfo['interview_video'] = interview_video&amp;#10;    expInfo['breathing_video'] = breathing_video&amp;#10;else:&amp;#10;    # Handle case where participant/session combination is not in the Excel file&amp;#10;    print(f&quot;Warning: Participant {participant_num}, Session {session_num} not found in participant_schedule.xlsx&quot;)&amp;#10;    # Set default video filenames&amp;#10;    expInfo['interview_video'] = 'default_interview.mp4'&amp;#10;    expInfo['breathing_video'] = 'resting_state'&amp;#10;&amp;#10;instructed_breathing_instruction = &quot; task: 6 min. \n Just follow the video for inhale and exhale, and relax. \n Press F1 to start.&quot;&amp;#10;resting_state_instruction = &quot; task: 6 min. \n Please keep looking at the cross for 6 minutes. \n Press F1 to start.&quot;&amp;#10;&amp;#10;breathing_task_text = ''&amp;#10;if breathing_video == 'paced_breathing':&amp;#10;    breathing_task_text = 'Paced Breathing' + instructed_breathing_instruction&amp;#10;elif breathing_video == 'fast_with_breath_hold':&amp;#10;    breathing_task_text = 'Fast Paced Breathing with Breath Hold' + instructed_breathing_instruction&amp;#10;elif breathing_video == 'resting_state':&amp;#10;    breathing_task_text = 'Resting State' + resting_state_instruction&amp;#10;#breathing_task_text = breathing_task_text + &quot; task: 6 min. \n Just follow the video for inhale and exhale, and relax. \n Press F1 to start.&quot;&amp;#10;&amp;#10;# Print debug information&amp;#10;print(f&quot;Participant: {participant_num}, Session: {session_num}&quot;)&amp;#10;print(f&quot;Current row: {current_row}&quot;)&amp;#10;print(f&quot;Breathing video: {expInfo['breathing_video']}&quot;)&amp;#10;print(f&quot;Interview video: {expInfo['interview_video']}&quot;)&amp;#10;&amp;#10;# Print all data stored in expInfo&amp;#10;print(f&quot;All data in expInfo: {expInfo}&quot;)&amp;#10;&amp;#10;# Initialize the wrong answer sound&amp;#10;#wrong_sound = sound.Sound('sound/wrong.wav')&amp;#10;# Replace the current wrong_sound initialization with:&amp;#10;wrong_sound = sound.Sound('sound/wrong.wav')" valType="extendedCode" updates="constant" name="Begin Experiment"/>
        <Param val="import * as pd from 'pandas';&amp;#10;import {core, event, sound, visual} from 'psychopy';&amp;#10;schedule_data = pd.read_excel(&quot;participant_schedule_vis.xlsx&quot;);&amp;#10;participant_num = Number.parseInt(expInfo[&quot;participant&quot;]);&amp;#10;session_num = Number.parseInt(expInfo[&quot;session&quot;]);&amp;#10;current_row = schedule_data[((schedule_data[&quot;participant&quot;] === participant_num) &amp; (schedule_data[&quot;session&quot;] === session_num))];&amp;#10;if ((! current_row.empty)) {&amp;#10;    interview_video = current_row[&quot;interview_video&quot;].values[0];&amp;#10;    breathing_video = current_row[&quot;breathing_protocol&quot;].values[0];&amp;#10;    expInfo[&quot;interview_video&quot;] = interview_video;&amp;#10;    expInfo[&quot;breathing_video&quot;] = breathing_video;&amp;#10;} else {&amp;#10;    console.log(`Warning: Participant ${participant_num}, Session ${session_num} not found in participant_schedule.xlsx`);&amp;#10;    expInfo[&quot;interview_video&quot;] = &quot;default_interview.mp4&quot;;&amp;#10;    expInfo[&quot;breathing_video&quot;] = &quot;resting_state&quot;;&amp;#10;}&amp;#10;instructed_breathing_instruction = &quot; task: 6 min. \n Just follow the video for inhale and exhale, and relax. \n Press F1 to start.&quot;;&amp;#10;resting_state_instruction = &quot; task: 6 min. \n Please keep looking at the cross for 6 minutes. \n Press F1 to start.&quot;;&amp;#10;breathing_task_text = &quot;&quot;;&amp;#10;if ((breathing_video === &quot;paced_breathing&quot;)) {&amp;#10;    breathing_task_text = (&quot;Paced Breathing&quot; + instructed_breathing_instruction);&amp;#10;} else {&amp;#10;    if ((breathing_video === &quot;fast_with_breath_hold&quot;)) {&amp;#10;        breathing_task_text = (&quot;Fast Paced Breathing with Breath Hold&quot; + instructed_breathing_instruction);&amp;#10;    } else {&amp;#10;        if ((breathing_video === &quot;resting_state&quot;)) {&amp;#10;            breathing_task_text = (&quot;Resting State&quot; + resting_state_instruction);&amp;#10;        }&amp;#10;    }&amp;#10;}&amp;#10;console.log(`Participant: ${participant_num}, Session: ${session_num}`);&amp;#10;console.log(`Current row: ${current_row}`);&amp;#10;console.log(`Breathing video: ${expInfo[&quot;breathing_video&quot;]}`);&amp;#10;console.log(`Interview video: ${expInfo[&quot;interview_video&quot;]}`);&amp;#10;console.log(`All data in expInfo: ${expInfo}`);&amp;#10;wrong_sound = new sound.Sound(&quot;sound/wrong.wav&quot;);&amp;#10;" valType="extendedCode" updates="constant" name="Begin JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin Routine"/>
        <Param val="Auto-&gt;JS" valType="str" updates="None" name="Code Type"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each JS Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Routine"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="load_data" valType="code" updates="None" name="name"/>
      </CodeComponent>
      <VariableComponent name="breath_duration" plugin="None">
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="breath_duration" valType="code" updates="None" name="name"/>
        <Param val="False" valType="bool" updates="constant" name="saveEndExp"/>
        <Param val="True" valType="bool" updates="constant" name="saveEndRoutine"/>
        <Param val="never" valType="str" updates="constant" name="saveFrameValue"/>
        <Param val="True" valType="bool" updates="constant" name="saveStartExp"/>
        <Param val="False" valType="bool" updates="constant" name="saveStartRoutine"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="360" valType="code" updates="constant" name="startExpValue"/>
        <Param val="" valType="code" updates="None" name="startFrameValue"/>
        <Param val="" valType="code" updates="constant" name="startRoutineValue"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="syncScreenRefresh"/>
      </VariableComponent>
      <VariableComponent name="stress_duration" plugin="None">
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="stress_duration" valType="code" updates="None" name="name"/>
        <Param val="False" valType="bool" updates="constant" name="saveEndExp"/>
        <Param val="True" valType="bool" updates="constant" name="saveEndRoutine"/>
        <Param val="never" valType="str" updates="constant" name="saveFrameValue"/>
        <Param val="False" valType="bool" updates="constant" name="saveStartExp"/>
        <Param val="False" valType="bool" updates="constant" name="saveStartRoutine"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="2" valType="code" updates="constant" name="startExpValue"/>
        <Param val="" valType="code" updates="None" name="startFrameValue"/>
        <Param val="" valType="code" updates="constant" name="startRoutineValue"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="syncScreenRefresh"/>
      </VariableComponent>
    </Routine>
    <Routine name="_03_05_07_breathing_trial">
      <RoutineSettingsComponent name="_03_05_07_breathing_trial" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="_03_05_07_breathing_trial" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <CodeComponent name="t_stim_3" plugin="None">
        <Param val="" valType="extendedCode" updates="constant" name="Before Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Before JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Experiment"/>
        <Param val="outlet.push_sample({&quot;x&quot;: [Number.parseInt(31)]});&amp;#10;" valType="extendedCode" updates="constant" name="Begin JS Routine"/>
        <Param val="outlet.push_sample(x=[int(31)])" valType="extendedCode" updates="constant" name="Begin Routine"/>
        <Param val="Auto-&gt;JS" valType="str" updates="None" name="Code Type"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each JS Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Routine"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="t_stim_3" valType="code" updates="None" name="name"/>
      </CodeComponent>
      <CodeComponent name="code_breathwork_vis" plugin="None">
        <Param val="" valType="extendedCode" updates="constant" name="Before Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Before JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Experiment"/>
        <Param val="/* Syntax Error: Fix Python code */" valType="extendedCode" updates="constant" name="Begin JS Routine"/>
        <Param val="# Paced Breathing Visualization for PsychoPy (height units)&amp;#10;# To be pasted into a code component in a PsychoPy routine&amp;#10;# Note: This code assumes 'win' (window object) is already defined in the PsychoPy environment&amp;#10;# IDE warnings about undefined variables can be ignored as they will be defined in the PsychoPy environment&amp;#10;&amp;#10;from psychopy import visual, core, event&amp;#10;import numpy as np&amp;#10;&amp;#10;# Check which breathing protocol to use (should be defined in experiment)&amp;#10;# breath_protocol should be either &quot;paced_breathing&quot;, &quot;fast_with_breath_hold&quot;, or &quot;resting_state&quot;&amp;#10;breath_protocol = &quot;fast_with_breath_hold&quot;  # Default value&amp;#10;&amp;#10;# Protocol Configuration - Single source of truth for all timing parameters&amp;#10;PROTOCOL_CONFIGS = {&amp;#10;    &quot;fast_with_breath_hold&quot;: {&amp;#10;        &quot;fast_inhale_time&quot;: 1.5,&amp;#10;        &quot;fast_exhale_time&quot;: 1.5,&amp;#10;        &quot;num_fast_breaths&quot;: 30,&amp;#10;        &quot;exhale_hold_duration&quot;: 30,  # Can be changed to 15 for 15s+15s mode&amp;#10;        &quot;inhale_hold_duration&quot;: 0,   # Can be changed to 15 for 15s+15s mode&amp;#10;        &quot;num_rounds&quot;: 3&amp;#10;    },&amp;#10;    &quot;paced_breathing&quot;: {&amp;#10;        &quot;inhale_time&quot;: 4.5,&amp;#10;        &quot;inhale_hold_time&quot;: 0.5,&amp;#10;        &quot;exhale_time&quot;: 4.5,&amp;#10;        &quot;exhale_hold_time&quot;: 0.5&amp;#10;    }&amp;#10;}&amp;#10;&amp;#10;# Extract protocol parameters from configuration&amp;#10;if breath_protocol == &quot;paced_breathing&quot;:&amp;#10;    # Mode 1: Original paced breathing&amp;#10;    config = PROTOCOL_CONFIGS[&quot;paced_breathing&quot;]&amp;#10;    inhale_time = config[&quot;inhale_time&quot;]&amp;#10;    inhale_hold_time = config[&quot;inhale_hold_time&quot;]&amp;#10;    exhale_time = config[&quot;exhale_time&quot;]&amp;#10;    exhale_hold_time = config[&quot;exhale_hold_time&quot;]&amp;#10;    total_cycle_time = inhale_time + inhale_hold_time + exhale_time + exhale_hold_time&amp;#10;&amp;#10;elif breath_protocol == &quot;fast_with_breath_hold&quot;:&amp;#10;    # Mode 2: Fast breathing with holds&amp;#10;    config = PROTOCOL_CONFIGS[&quot;fast_with_breath_hold&quot;]&amp;#10;    fast_inhale_time = config[&quot;fast_inhale_time&quot;]&amp;#10;    fast_exhale_time = config[&quot;fast_exhale_time&quot;]&amp;#10;    fast_cycle_time = fast_inhale_time + fast_exhale_time&amp;#10;    num_fast_breaths = config[&quot;num_fast_breaths&quot;]&amp;#10;    fast_breathing_duration = num_fast_breaths * fast_cycle_time  # 90 seconds&amp;#10;    exhale_hold_duration = config[&quot;exhale_hold_duration&quot;]&amp;#10;    inhale_hold_duration = config[&quot;inhale_hold_duration&quot;]&amp;#10;    total_hold_duration = exhale_hold_duration + inhale_hold_duration&amp;#10;    has_inhale_hold = inhale_hold_duration &gt; 0&amp;#10;    round_duration = fast_breathing_duration + total_hold_duration  # 120 seconds&amp;#10;    num_rounds = config[&quot;num_rounds&quot;]&amp;#10;&amp;#10;    # Define these variables for compatibility with other modes&amp;#10;    inhale_time = 4.5&amp;#10;    inhale_hold_time = 0.5&amp;#10;    exhale_time = 4.5&amp;#10;    exhale_hold_time = 0.5&amp;#10;    total_cycle_time = inhale_time + inhale_hold_time + exhale_time + exhale_hold_time&amp;#10;&amp;#10;elif breath_protocol == &quot;resting_state&quot;:&amp;#10;    # Mode 3: Resting state with crosshair&amp;#10;    # Define dummy variables to prevent &quot;referenced before assignment&quot; errors&amp;#10;    inhale_time = 4.5&amp;#10;    inhale_hold_time = 0.5&amp;#10;    exhale_time = 4.5&amp;#10;    exhale_hold_time = 0.5&amp;#10;    total_cycle_time = inhale_time + inhale_hold_time + exhale_time + exhale_hold_time&amp;#10;&amp;#10;else:&amp;#10;    # Default case - treat as resting state&amp;#10;    print(f&quot;Warning: Unknown breath_protocol '{breath_protocol}', defaulting to resting_state&quot;)&amp;#10;    breath_protocol = &quot;resting_state&quot;&amp;#10;&amp;#10;    # Define dummy variables to prevent &quot;referenced before assignment&quot; errors&amp;#10;    inhale_time = 4.5&amp;#10;    inhale_hold_time = 0.5&amp;#10;    exhale_time = 4.5&amp;#10;    exhale_hold_time = 0.5&amp;#10;    total_cycle_time = inhale_time + inhale_hold_time + exhale_time + exhale_hold_time&amp;#10;&amp;#10;# Check if breath_duration is defined in the experiment (common for all protocols)&amp;#10;try:&amp;#10;    # Try to access breath_duration&amp;#10;    total_duration = breath_duration  # Use variable from experiment&amp;#10;except NameError:&amp;#10;    # If not defined, set a default value&amp;#10;    total_duration = 360  # Default to 6 minutes&amp;#10;    print(&quot;Warning: breath_duration not defined, using default: 360 seconds&quot;)&amp;#10;&amp;#10;# Get window dimensions in height units&amp;#10;# In height units, the height is always 1.0 (from -0.5 to 0.5)&amp;#10;# The width depends on the aspect ratio&amp;#10;win_height = 1.0&amp;#10;win_width = win.size[0] / win.size[1]  # This gives us the aspect ratio&amp;#10;&amp;#10;# Normalize horizontal distance to window width&amp;#10;# Leave some margin on both sides (10% of width)&amp;#10;margin = 0.1 * win_width&amp;#10;usable_width = win_width - 2 * margin&amp;#10;x_start = -win_width/2 + margin&amp;#10;x_end = win_width/2 - margin&amp;#10;&amp;#10;# Vertical parameters (in height units)&amp;#10;y_min = -0.25  # Bottom position for exhale&amp;#10;y_max = 0.25   # Top position for inhale&amp;#10;y_range = y_max - y_min&amp;#10;&amp;#10;# Create visual components based on protocol&amp;#10;if breath_protocol == &quot;resting_state&quot;:&amp;#10;    # For resting state, create only the crosshair (matching the original resting state routine)&amp;#10;    cross = visual.TextStim(&amp;#10;        win=win,&amp;#10;        name='cross',&amp;#10;        text='+',&amp;#10;        font='Open Sans',&amp;#10;        pos=(0, 0),&amp;#10;        height=0.05,&amp;#10;        wrapWidth=None,&amp;#10;        ori=0.0,&amp;#10;        color='white',&amp;#10;        colorSpace='rgb',&amp;#10;        opacity=None,&amp;#10;        languageStyle='LTR',&amp;#10;        depth=-1.0&amp;#10;    )&amp;#10;&amp;#10;    # Set dummy variables for other components to prevent errors&amp;#10;    path_line = None&amp;#10;    ball = None&amp;#10;    background = None&amp;#10;    vertices = []&amp;#10;&amp;#10;else:&amp;#10;    # Create the breathing path line segments for paced_breathing and fast_with_breath_hold&amp;#10;    # Calculate segment widths based on their duration&amp;#10;    if breath_protocol == &quot;paced_breathing&quot;:&amp;#10;        # Original path vertices for paced breathing&amp;#10;        inhale_width = (inhale_time / total_cycle_time) * usable_width&amp;#10;        inhale_hold_width = (inhale_hold_time / total_cycle_time) * usable_width&amp;#10;        exhale_width = (exhale_time / total_cycle_time) * usable_width&amp;#10;        exhale_hold_width = (exhale_hold_time / total_cycle_time) * usable_width&amp;#10;&amp;#10;        vertices = [&amp;#10;            [x_start, y_min],                                  # Start point&amp;#10;            [x_start + inhale_width, y_max],                   # End of inhale&amp;#10;            [x_start + inhale_width + inhale_hold_width, y_max],  # End of inhale hold&amp;#10;            [x_start + inhale_width + inhale_hold_width + exhale_width, y_min],  # End of exhale&amp;#10;            [x_end, y_min]                                     # End of exhale hold&amp;#10;        ]&amp;#10;    else:  # fast_with_breath_hold&amp;#10;        # For fast_with_breath_hold, we need two different path visualizations:&amp;#10;        # 1. Fast breathing path (first 90 seconds of each round)&amp;#10;        # 2. Hold phases path (last 30 seconds of each round)&amp;#10;&amp;#10;        # Calculate the current phase based on time&amp;#10;        current_time = 0  # This will be updated in the animation loop&amp;#10;        round_num = int(current_time / round_duration)&amp;#10;        round_time = current_time - (round_num * round_duration)&amp;#10;&amp;#10;        # Fast breathing path parameters&amp;#10;        fast_inhale_width = (fast_inhale_time / fast_cycle_time) * usable_width&amp;#10;&amp;#10;        if round_time &lt; fast_breathing_duration:&amp;#10;            # Fast breathing path - just up and down, no holds&amp;#10;            vertices = [&amp;#10;                [x_start, y_min],                      # Start point&amp;#10;                [x_start + fast_inhale_width, y_max],  # End of inhale&amp;#10;                [x_end, y_min]                         # End of exhale&amp;#10;            ]&amp;#10;        else:&amp;#10;            # Hold phase path - flexible to handle single exhale hold or exhale+inhale holds&amp;#10;            if not has_inhale_hold:&amp;#10;                # Simple horizontal line at the bottom for single exhale hold&amp;#10;                vertices = [&amp;#10;                    [x_start, y_min],  # Start of exhale hold&amp;#10;                    [x_end, y_min]     # End of exhale hold (stay at bottom)&amp;#10;                ]&amp;#10;            else:&amp;#10;                # Complex path for exhale hold + inhale hold (future flexibility)&amp;#10;                exhale_hold_width = usable_width * exhale_hold_duration / total_hold_duration&amp;#10;                vertices = [&amp;#10;                    [x_start, y_min],                           # Start of exhale hold&amp;#10;                    [x_start + exhale_hold_width, y_min],       # End of exhale hold&amp;#10;                    [x_start + exhale_hold_width, y_max],       # Quick transition to inhale hold&amp;#10;                    [x_end, y_max]                              # End of inhale hold&amp;#10;                ]&amp;#10;&amp;#10;    # Create the path line&amp;#10;    path_line = visual.ShapeStim(&amp;#10;        win=win,&amp;#10;        vertices=vertices,&amp;#10;        closeShape=False,&amp;#10;        lineWidth=3,&amp;#10;        lineColor='white',&amp;#10;        opacity=0.7&amp;#10;    )&amp;#10;&amp;#10;    # Create the ball that will travel along the path&amp;#10;    ball = visual.Circle(&amp;#10;        win=win,&amp;#10;        radius=0.015,  # Adjusted for height units&amp;#10;        fillColor='lightblue',&amp;#10;        lineColor='white',&amp;#10;        lineWidth=2&amp;#10;    )&amp;#10;&amp;#10;    # Create background shading rectangle&amp;#10;    background = visual.Rect(&amp;#10;        win=win,&amp;#10;        width=win_width,&amp;#10;        height=y_range,&amp;#10;        fillColor='skyblue',&amp;#10;        opacity=0.2,&amp;#10;        pos=[0, y_min + y_range/2]  # Start at the bottom&amp;#10;    )&amp;#10;&amp;#10;    # Set dummy variable for crosshair&amp;#10;    cross = None&amp;#10;&amp;#10;# Function to calculate ball position at a given time&amp;#10;def get_ball_position(current_time):&amp;#10;    if breath_protocol == &quot;resting_state&quot;:&amp;#10;        # For resting state, no ball movement needed&amp;#10;        return 0, 0, &quot;Resting State&quot;&amp;#10;    elif breath_protocol == &quot;paced_breathing&quot;:&amp;#10;        # Define breathing parameters for paced breathing&amp;#10;        inhale_time = 4.5&amp;#10;        inhale_hold_time = 0.5&amp;#10;        exhale_time = 4.5&amp;#10;        exhale_hold_time = 0.5&amp;#10;        total_cycle_time = inhale_time + inhale_hold_time + exhale_time + exhale_hold_time&amp;#10;&amp;#10;        # Original ball position calculation&amp;#10;        cycle_time = current_time % total_cycle_time&amp;#10;&amp;#10;        # Calculate segment widths here to ensure they're defined&amp;#10;        inhale_width = (inhale_time / total_cycle_time) * usable_width&amp;#10;        inhale_hold_width = (inhale_hold_time / total_cycle_time) * usable_width&amp;#10;        exhale_width = (exhale_time / total_cycle_time) * usable_width&amp;#10;        exhale_hold_width = (exhale_hold_time / total_cycle_time) * usable_width&amp;#10;&amp;#10;        # Determine which segment we're in&amp;#10;        if cycle_time &lt; inhale_time:&amp;#10;            # Inhale segment - moving up&amp;#10;            progress = cycle_time / inhale_time&amp;#10;            x = x_start + progress * inhale_width&amp;#10;            y = y_min + progress * y_range&amp;#10;            phase = &quot;Inhale&quot;&amp;#10;&amp;#10;        elif cycle_time &lt; inhale_time + inhale_hold_time:&amp;#10;            # Inhale hold segment - staying at the top&amp;#10;            progress = (cycle_time - inhale_time) / inhale_hold_time&amp;#10;            x = x_start + inhale_width + progress * inhale_hold_width&amp;#10;            y = y_max&amp;#10;            phase = &quot;Hold&quot;&amp;#10;&amp;#10;        elif cycle_time &lt; inhale_time + inhale_hold_time + exhale_time:&amp;#10;            # Exhale segment - moving down&amp;#10;            progress = (cycle_time - inhale_time - inhale_hold_time) / exhale_time&amp;#10;            x = x_start + inhale_width + inhale_hold_width + progress * exhale_width&amp;#10;            y = y_max - progress * y_range&amp;#10;            phase = &quot;Exhale&quot;&amp;#10;&amp;#10;        else:&amp;#10;            # Exhale hold segment - staying at the bottom&amp;#10;            progress = (cycle_time - inhale_time - inhale_hold_time - exhale_time) / exhale_hold_time&amp;#10;            x = x_start + inhale_width + inhale_hold_width + exhale_width + progress * exhale_hold_width&amp;#10;            y = y_min&amp;#10;            phase = &quot;Hold&quot;&amp;#10;&amp;#10;    else:  # fast_with_breath_hold&amp;#10;        # Use parameters from global configuration (already defined above)&amp;#10;        # Calculate which round we're in&amp;#10;        round_num = int(current_time / round_duration)&amp;#10;        if round_num &gt;= num_rounds:&amp;#10;            round_num = num_rounds - 1&amp;#10;&amp;#10;        # Calculate time within the current round&amp;#10;        round_time = current_time - (round_num * round_duration)&amp;#10;&amp;#10;        # Determine which phase we're in&amp;#10;        if round_time &lt; fast_breathing_duration:&amp;#10;            # Fast breathing phase&amp;#10;            cycle_num = int(round_time / fast_cycle_time)&amp;#10;            cycle_time = round_time % fast_cycle_time&amp;#10;&amp;#10;            # Fast breathing path parameters - ensure consistent with path creation&amp;#10;            fast_inhale_width = (fast_inhale_time / fast_cycle_time) * usable_width&amp;#10;&amp;#10;            if cycle_time &lt; fast_inhale_time:&amp;#10;                # Inhale segment - moving up&amp;#10;                progress = cycle_time / fast_inhale_time&amp;#10;                x = x_start + progress * fast_inhale_width&amp;#10;                y = y_min + progress * y_range&amp;#10;                phase = &quot;Inhale&quot;&amp;#10;            else:&amp;#10;                # Exhale segment - moving down&amp;#10;                progress = (cycle_time - fast_inhale_time) / fast_exhale_time&amp;#10;                # Use the remaining width to ensure ball reaches end point&amp;#10;                remaining_width = usable_width - fast_inhale_width&amp;#10;                x = x_start + fast_inhale_width + progress * remaining_width&amp;#10;                y = y_max - progress * y_range&amp;#10;                phase = &quot;Exhale&quot;&amp;#10;&amp;#10;            # Add breath counter to phase&amp;#10;            phase += f&quot; ({cycle_num+1}/{num_fast_breaths})&quot;&amp;#10;&amp;#10;        else:&amp;#10;            # Hold phase - flexible to handle single exhale hold or exhale+inhale holds&amp;#10;            hold_time = round_time - fast_breathing_duration&amp;#10;&amp;#10;            if not has_inhale_hold:&amp;#10;                # Simple single exhale hold - ball moves horizontally across the bottom&amp;#10;                progress = hold_time / exhale_hold_duration&amp;#10;                x = x_start + progress * usable_width&amp;#10;                y = y_min&amp;#10;                phase = f&quot;Exhale Hold ({int(exhale_hold_duration - hold_time)}s)&quot;&amp;#10;            else:&amp;#10;                # Complex hold phase with exhale hold + inhale hold (for future flexibility)&amp;#10;                if hold_time &lt; exhale_hold_duration:&amp;#10;                    # Exhale hold phase&amp;#10;                    progress = hold_time / exhale_hold_duration&amp;#10;                    x = x_start + progress * (usable_width * exhale_hold_duration / total_hold_duration)&amp;#10;                    y = y_min&amp;#10;                    phase = f&quot;Exhale Hold ({int(exhale_hold_duration - hold_time)}s)&quot;&amp;#10;                else:&amp;#10;                    # Inhale hold phase&amp;#10;                    inhale_time_elapsed = hold_time - exhale_hold_duration&amp;#10;                    progress = inhale_time_elapsed / inhale_hold_duration&amp;#10;                    exhale_hold_width = usable_width * exhale_hold_duration / total_hold_duration&amp;#10;                    inhale_hold_width = usable_width * inhale_hold_duration / total_hold_duration&amp;#10;                    x = x_start + exhale_hold_width + progress * inhale_hold_width&amp;#10;                    y = y_max&amp;#10;                    phase = f&quot;Inhale Hold ({int(inhale_hold_duration - inhale_time_elapsed)}s)&quot;&amp;#10;&amp;#10;        # Add round information&amp;#10;        phase = f&quot;Round {round_num+1}/{num_rounds}: &quot; + phase&amp;#10;&amp;#10;    return x, y, phase&amp;#10;&amp;#10;# Create text display for the breathing phase&amp;#10;phase_text = visual.TextStim(&amp;#10;    win=win,&amp;#10;    text=&quot;Inhale&quot;,&amp;#10;    pos=[0, -0.35],&amp;#10;    color='white',&amp;#10;    height=0.05  # Adjusted for height units&amp;#10;)&amp;#10;&amp;#10;# Create a timer display&amp;#10;timer_text = visual.TextStim(&amp;#10;    win=win,&amp;#10;    text=&quot;&quot;,&amp;#10;    pos=[0, 0.4],&amp;#10;    color='white',&amp;#10;    height=0.03  # Adjusted for height units&amp;#10;)&amp;#10;&amp;#10;# Create breath counter rectangles for fast_with_breath_hold mode&amp;#10;breath_rects = []&amp;#10;round_rects = []&amp;#10;if breath_protocol == &quot;fast_with_breath_hold&quot;:&amp;#10;    # Calculate rectangle dimensions and positions&amp;#10;    rect_height = 0.05  # Height of each rectangle&amp;#10;    rect_width = 0.02   # Width of each rectangle&amp;#10;    rect_spacing = 0.005  # Space between rectangles&amp;#10;&amp;#10;    # Position in right 2/3 of screen&amp;#10;    screen_left = -win_width/2&amp;#10;    screen_right = win_width/2&amp;#10;    left_third_end = screen_left + win_width/3&amp;#10;&amp;#10;    # Calculate total width needed for all breath rectangles&amp;#10;    total_rect_width = num_fast_breaths * (rect_width + rect_spacing) - rect_spacing&amp;#10;&amp;#10;    # Center the rectangles in the right 2/3 of the screen&amp;#10;    rect_area_start = left_third_end&amp;#10;    rect_area_end = screen_right&amp;#10;    rect_area_width = rect_area_end - rect_area_start&amp;#10;&amp;#10;    # Start position for the first rectangle - leave space for the &quot;Breaths:&quot; text&amp;#10;    rect_start_x = left_third_end + 0.25  # Adjusted to leave space for text&amp;#10;&amp;#10;    # Create all breath rectangles - initially gray with white outline&amp;#10;    for i in range(num_fast_breaths):&amp;#10;        rect = visual.Rect(&amp;#10;            win=win,&amp;#10;            width=rect_width,&amp;#10;            height=rect_height,&amp;#10;            fillColor='gray',  # Start with gray fill&amp;#10;            lineColor='white',&amp;#10;            lineWidth=1,&amp;#10;            opacity=0.3,  # Make the gray semi-transparent&amp;#10;            pos=[rect_start_x + i * (rect_width + rect_spacing), -0.4]&amp;#10;        )&amp;#10;        breath_rects.append(rect)&amp;#10;&amp;#10;    # Create round counter rectangles&amp;#10;    # Calculate dimensions for round rectangles (slightly larger)&amp;#10;    round_rect_height = 0.06&amp;#10;    round_rect_width = 0.03&amp;#10;    round_rect_spacing = 0.01&amp;#10;&amp;#10;    # Position round rectangles in the left 1/4 of the screen&amp;#10;    left_quarter_end = screen_left + win_width/4&amp;#10;    round_rect_start_x = left_quarter_end - 0.15  # Positioned to the left of the round text&amp;#10;&amp;#10;    # Create round rectangles - initially gray with white outline&amp;#10;    for i in range(num_rounds):&amp;#10;        rect = visual.Rect(&amp;#10;            win=win,&amp;#10;            width=round_rect_width,&amp;#10;            height=round_rect_height,&amp;#10;            fillColor='gray',  # Start with gray fill&amp;#10;            lineColor='white',&amp;#10;            lineWidth=1,&amp;#10;            opacity=0.3,  # Make the gray semi-transparent&amp;#10;            pos=[round_rect_start_x + i * (round_rect_width + round_rect_spacing), -0.4]&amp;#10;        )&amp;#10;        round_rects.append(rect)&amp;#10;&amp;#10;# Create round counter text for fast_with_breath_hold mode&amp;#10;round_text = None&amp;#10;breaths_remaining_text = None&amp;#10;if breath_protocol == &quot;fast_with_breath_hold&quot;:&amp;#10;    # Round counter text - positioned at the right side of the left 1/4&amp;#10;    left_quarter_end = screen_left + win_width/4&amp;#10;    round_text = visual.TextStim(&amp;#10;        win=win,&amp;#10;        text=&quot;Round:&quot;,&amp;#10;        pos=[left_quarter_end - 0.35, -0.4],  # Moved further left so text ends ~2 chars before previous start&amp;#10;        color='white',&amp;#10;        height=0.05,  # Adjusted for height units&amp;#10;        alignHoriz='left'  # Align text to the left for consistent positioning&amp;#10;    )&amp;#10;&amp;#10;    # Breaths remaining text - positioned before the rectangles&amp;#10;    breaths_remaining_text = visual.TextStim(&amp;#10;        win=win,&amp;#10;        text=&quot;Breaths:&quot;,&amp;#10;        pos=[left_third_end + 0.05, -0.4],  # Left side of right 2/3, moved left&amp;#10;        color='white',&amp;#10;        height=0.05,  # Adjusted for height units&amp;#10;        alignHoriz='left'&amp;#10;    )&amp;#10;&amp;#10;# Main animation loop&amp;#10;timer = core.Clock()&amp;#10;continue_routine = True&amp;#10;&amp;#10;try:&amp;#10;    while continue_routine:&amp;#10;        # Get current time&amp;#10;        t = timer.getTime()&amp;#10;&amp;#10;        # Check if we've reached the total duration&amp;#10;        if t &gt;= total_duration:&amp;#10;            continue_routine = False&amp;#10;            break&amp;#10;&amp;#10;        # For fast_with_breath_hold mode, update the path visualization based on the current phase&amp;#10;        if breath_protocol == &quot;fast_with_breath_hold&quot;:&amp;#10;            # Use parameters from global configuration (already defined above)&amp;#10;            round_num = int(t / round_duration)&amp;#10;            if round_num &gt;= num_rounds:&amp;#10;                round_num = num_rounds - 1&amp;#10;&amp;#10;            round_time = t - (round_num * round_duration)&amp;#10;&amp;#10;            # Check if we need to switch path visualization&amp;#10;            is_hold_phase = round_time &gt;= fast_breathing_duration&amp;#10;&amp;#10;            # Check if path_line exists and has vertices before accessing them&amp;#10;            if hasattr(path_line, 'vertices') and len(path_line.vertices) &gt; 0:&amp;#10;                # Fast breathing path parameters&amp;#10;                fast_inhale_width = (fast_inhale_time / fast_cycle_time) * usable_width&amp;#10;&amp;#10;                # Check the current phase and update path if needed&amp;#10;                if is_hold_phase and len(path_line.vertices) == 3:&amp;#10;                    # Switch to hold phase path&amp;#10;                    if not has_inhale_hold:&amp;#10;                        # Simple horizontal line at bottom for single exhale hold&amp;#10;                        path_line.vertices = [&amp;#10;                            [x_start, y_min],  # Start of exhale hold&amp;#10;                            [x_end, y_min]     # End of exhale hold (stay at bottom)&amp;#10;                        ]&amp;#10;                    else:&amp;#10;                        # Complex path for exhale hold + inhale hold (future flexibility)&amp;#10;                        exhale_hold_width = usable_width * exhale_hold_duration / total_hold_duration&amp;#10;                        path_line.vertices = [&amp;#10;                            [x_start, y_min],                           # Start of exhale hold&amp;#10;                            [x_start + exhale_hold_width, y_min],       # End of exhale hold&amp;#10;                            [x_start + exhale_hold_width, y_max],       # Quick transition to inhale hold&amp;#10;                            [x_end, y_max]                              # End of inhale hold&amp;#10;                        ]&amp;#10;&amp;#10;                elif not is_hold_phase and (len(path_line.vertices) == 2 or len(path_line.vertices) == 4):&amp;#10;                    # Switch back to fast breathing path&amp;#10;                    path_line.vertices = [&amp;#10;                        [x_start, y_min],&amp;#10;                        [x_start + fast_inhale_width, y_max],&amp;#10;                        [x_end, y_min]&amp;#10;                    ]&amp;#10;&amp;#10;        # Update timer text - show remaining time&amp;#10;        time_left = int(total_duration - t)&amp;#10;        timer_text.text = f&quot;Time remaining: {time_left}s&quot;&amp;#10;&amp;#10;        # Draw stimuli based on protocol&amp;#10;        if breath_protocol == &quot;resting_state&quot;:&amp;#10;            # For resting state, only draw the crosshair and timer&amp;#10;            if cross:&amp;#10;                cross.draw()&amp;#10;            timer_text.draw()&amp;#10;&amp;#10;        else:&amp;#10;            # For breathing protocols, draw the breathing visualization&amp;#10;            # Calculate current ball position&amp;#10;            x, y, phase = get_ball_position(t)&amp;#10;&amp;#10;            # Update ball position&amp;#10;            if ball:&amp;#10;                ball.pos = [x, y]&amp;#10;&amp;#10;            # Update background height and position&amp;#10;            if background:&amp;#10;                background.height = y - y_min&amp;#10;                background.pos = [0, y_min + (y - y_min)/2]&amp;#10;&amp;#10;            # Update phase text&amp;#10;            phase_text.text = phase&amp;#10;&amp;#10;            # Draw breathing visualization components&amp;#10;            if background:&amp;#10;                background.draw()&amp;#10;            if path_line:&amp;#10;                path_line.draw()&amp;#10;            if ball:&amp;#10;                ball.draw()&amp;#10;            timer_text.draw()  # Keep timer text&amp;#10;&amp;#10;            # Draw breath counter rectangles and round counter for fast_with_breath_hold mode&amp;#10;            if breath_protocol == &quot;fast_with_breath_hold&quot;:&amp;#10;                # Draw round text (static &quot;Round:&quot; label)&amp;#10;                if round_text:&amp;#10;                    round_text.draw()&amp;#10;&amp;#10;                # Draw breaths remaining text&amp;#10;                if breaths_remaining_text:&amp;#10;                    breaths_remaining_text.draw()&amp;#10;&amp;#10;                # Update and draw breath rectangles&amp;#10;                if breath_rects:&amp;#10;                    # Calculate how many breaths have been completed in the current round&amp;#10;                    if round_time &lt; fast_breathing_duration:&amp;#10;                        completed_breaths = int(round_time / fast_cycle_time)&amp;#10;                    else:&amp;#10;                        completed_breaths = num_fast_breaths  # All breaths completed&amp;#10;&amp;#10;                    # Draw all breath rectangles - advance by 1 so current breath is already filled&amp;#10;                    for i, rect in enumerate(breath_rects):&amp;#10;                        # For the current breath cycle and completed ones&amp;#10;                        if i &lt;= completed_breaths:  # Changed from &lt; to &lt;= to include current breath&amp;#10;                            # Fill current and completed breath rectangles with lightblue&amp;#10;                            rect.fillColor = 'lightblue'&amp;#10;                            rect.opacity = 1.0&amp;#10;                        else:&amp;#10;                            # Keep future breath rectangles gray and semi-transparent&amp;#10;                            rect.fillColor = 'gray'&amp;#10;                            rect.opacity = 0.3&amp;#10;                        rect.draw()&amp;#10;&amp;#10;                # Update and draw round rectangles&amp;#10;                if round_rects:&amp;#10;                    # Draw all round rectangles&amp;#10;                    for i, rect in enumerate(round_rects):&amp;#10;                        if i &lt;= round_num:  # Fill current and previous rounds&amp;#10;                            # Fill completed round rectangles with same blue as breath counter&amp;#10;                            rect.fillColor = 'lightblue'&amp;#10;                            rect.opacity = 1.0&amp;#10;                        else:&amp;#10;                            # Keep future round rectangles gray and semi-transparent&amp;#10;                            rect.fillColor = 'gray'&amp;#10;                            rect.opacity = 0.3&amp;#10;                        rect.draw()&amp;#10;            else:&amp;#10;                # For paced_breathing mode, still show the phase text&amp;#10;                phase_text.draw()&amp;#10;&amp;#10;        # Check for quit (the Esc key)&amp;#10;        keys = event.getKeys(keyList=[&quot;escape&quot;])&amp;#10;        if 'escape' in keys:&amp;#10;            continue_routine = False&amp;#10;            core.quit()  # This will exit the experiment&amp;#10;&amp;#10;        # Refresh the screen&amp;#10;        win.flip()&amp;#10;except Exception as e:&amp;#10;    print(f&quot;Error in animation loop: {e}&quot;)&amp;#10;    # Handle the error appropriately for your experiment&amp;#10;finally:&amp;#10;    # Clean up&amp;#10;    import gc&amp;#10;    gc.collect()  # Force garbage collection&amp;#10;&amp;#10;# Just clear the screen and wait&amp;#10;try:&amp;#10;    win.flip()&amp;#10;    core.wait(2.0)&amp;#10;except Exception as e:&amp;#10;    print(f&quot;Error in final screen clear: {e}&quot;)&amp;#10;" valType="extendedCode" updates="constant" name="Begin Routine"/>
        <Param val="Auto-&gt;JS" valType="str" updates="None" name="Code Type"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each JS Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Routine"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="code_breathwork_vis" valType="code" updates="None" name="name"/>
      </CodeComponent>
    </Routine>
    <Routine name="_03_05_07_breathing_instr">
      <RoutineSettingsComponent name="_03_05_07_breathing_instr" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="_03_05_07_breathing_instr" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <TextComponent name="text_3" plugin="None">
        <Param val="white" valType="color" updates="constant" name="color"/>
        <Param val="rgb" valType="str" updates="constant" name="colorSpace"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="None" valType="str" updates="constant" name="flip"/>
        <Param val="Open Sans" valType="str" updates="constant" name="font"/>
        <Param val="LTR" valType="str" updates="None" name="languageStyle"/>
        <Param val="0.03" valType="num" updates="constant" name="letterHeight"/>
        <Param val="text_3" valType="code" updates="None" name="name"/>
        <Param val="" valType="num" updates="constant" name="opacity"/>
        <Param val="0" valType="num" updates="constant" name="ori"/>
        <Param val="(0, 0)" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="$breathing_task_text" valType="str" updates="constant" name="text"/>
        <Param val="from exp settings" valType="str" updates="None" name="units"/>
        <Param val="" valType="num" updates="constant" name="wrapWidth"/>
      </TextComponent>
      <KeyboardComponent name="key_resp_3" plugin="None">
        <Param val="'f1'" valType="list" updates="constant" name="allowedKeys"/>
        <Param val="" valType="str" updates="constant" name="correctAns"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="True" valType="bool" updates="constant" name="discard previous"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="True" valType="bool" updates="constant" name="forceEndRoutine"/>
        <Param val="key_resp_3" valType="code" updates="None" name="name"/>
        <Param val="press" valType="str" updates="constant" name="registerOn"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="last key" valType="str" updates="constant" name="store"/>
        <Param val="False" valType="bool" updates="constant" name="storeCorrect"/>
        <Param val="True" valType="bool" updates="constant" name="syncScreenRefresh"/>
      </KeyboardComponent>
      <CodeComponent name="t_begin_3" plugin="None">
        <Param val="" valType="extendedCode" updates="constant" name="Before Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Before JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin Routine"/>
        <Param val="Auto-&gt;JS" valType="str" updates="None" name="Code Type"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each JS Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Experiment"/>
        <Param val="outlet.push_sample({&quot;x&quot;: [1]});&amp;#10;" valType="extendedCode" updates="constant" name="End JS Routine"/>
        <Param val="outlet.push_sample(x=[1])" valType="extendedCode" updates="constant" name="End Routine"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="t_begin_3" valType="code" updates="None" name="name"/>
      </CodeComponent>
    </Routine>
    <Routine name="_02_pubspeak_instr">
      <RoutineSettingsComponent name="_02_pubspeak_instr" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="True" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="_02_pubspeak_instr" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <TextComponent name="text_2" plugin="None">
        <Param val="white" valType="color" updates="constant" name="color"/>
        <Param val="rgb" valType="str" updates="constant" name="colorSpace"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="None" valType="str" updates="constant" name="flip"/>
        <Param val="Open Sans" valType="str" updates="constant" name="font"/>
        <Param val="LTR" valType="str" updates="None" name="languageStyle"/>
        <Param val="0.03" valType="num" updates="constant" name="letterHeight"/>
        <Param val="text_2" valType="code" updates="None" name="name"/>
        <Param val="" valType="num" updates="constant" name="opacity"/>
        <Param val="0" valType="num" updates="constant" name="ori"/>
        <Param val="(0, 0)" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="Job Interview: 3 min.&amp;#10;Speak as in a real interview. &amp;#10;Try to speak fluently, but no &quot;umh&quot;  filler words! &amp;#10;Every &quot;umh&quot; filler word will be notified!&amp;#10;&amp;#10;Press F1 to start." valType="str" updates="constant" name="text"/>
        <Param val="from exp settings" valType="str" updates="None" name="units"/>
        <Param val="" valType="num" updates="constant" name="wrapWidth"/>
      </TextComponent>
      <KeyboardComponent name="key_resp_2" plugin="None">
        <Param val="'f1'" valType="list" updates="constant" name="allowedKeys"/>
        <Param val="" valType="str" updates="constant" name="correctAns"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="True" valType="bool" updates="constant" name="discard previous"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="True" valType="bool" updates="constant" name="forceEndRoutine"/>
        <Param val="key_resp_2" valType="code" updates="None" name="name"/>
        <Param val="press" valType="str" updates="constant" name="registerOn"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="last key" valType="str" updates="constant" name="store"/>
        <Param val="False" valType="bool" updates="constant" name="storeCorrect"/>
        <Param val="True" valType="bool" updates="constant" name="syncScreenRefresh"/>
      </KeyboardComponent>
      <CodeComponent name="t_begin_2" plugin="None">
        <Param val="" valType="extendedCode" updates="constant" name="Before Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Before JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin Routine"/>
        <Param val="Auto-&gt;JS" valType="str" updates="None" name="Code Type"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each JS Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Experiment"/>
        <Param val="outlet.push_sample({&quot;x&quot;: [1]});&amp;#10;" valType="extendedCode" updates="constant" name="End JS Routine"/>
        <Param val="outlet.push_sample(x=[1])" valType="extendedCode" updates="constant" name="End Routine"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="t_begin_2" valType="code" updates="None" name="name"/>
      </CodeComponent>
    </Routine>
    <Routine name="_02_pubspeak_trial">
      <RoutineSettingsComponent name="_02_pubspeak_trial" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="True" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="_02_pubspeak_trial" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <CodeComponent name="t_stim_2" plugin="None">
        <Param val="" valType="extendedCode" updates="constant" name="Before Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Before JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Experiment"/>
        <Param val="outlet.push_sample({&quot;x&quot;: [Number.parseInt(20)]});&amp;#10;" valType="extendedCode" updates="constant" name="Begin JS Routine"/>
        <Param val="outlet.push_sample(x=[int(20)])" valType="extendedCode" updates="constant" name="Begin Routine"/>
        <Param val="Auto-&gt;JS" valType="str" updates="None" name="Code Type"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each JS Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Routine"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="t_stim_2" valType="code" updates="None" name="name"/>
      </CodeComponent>
      <MovieComponent name="video" plugin="None">
        <Param val="False" valType="bool" updates="None" name="No audio"/>
        <Param val="center" valType="str" updates="constant" name="anchor"/>
        <Param val="ffpyplayer" valType="str" updates="None" name="backend"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="False" valType="bool" updates="constant" name="forceEndRoutine"/>
        <Param val="False" valType="bool" updates="None" name="loop"/>
        <Param val="$expInfo['interview_video']" valType="file" updates="set every repeat" name="movie"/>
        <Param val="video" valType="code" updates="None" name="name"/>
        <Param val="" valType="num" updates="constant" name="opacity"/>
        <Param val="0" valType="num" updates="constant" name="ori"/>
        <Param val="(0, 0)" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="1.78, 1.0" valType="list" updates="constant" name="size"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="$stress_duration" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="constant" name="stopWithRoutine"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="from exp settings" valType="str" updates="None" name="units"/>
        <Param val="1" valType="num" updates="None" name="volume"/>
      </MovieComponent>
    </Routine>
    <Routine name="_01_08_rest_state_instr">
      <RoutineSettingsComponent name="_01_08_rest_state_instr" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="True" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="_01_08_rest_state_instr" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <TextComponent name="text" plugin="None">
        <Param val="white" valType="color" updates="constant" name="color"/>
        <Param val="rgb" valType="str" updates="constant" name="colorSpace"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="None" valType="str" updates="constant" name="flip"/>
        <Param val="Open Sans" valType="str" updates="constant" name="font"/>
        <Param val="LTR" valType="str" updates="None" name="languageStyle"/>
        <Param val="0.03" valType="num" updates="constant" name="letterHeight"/>
        <Param val="text" valType="code" updates="None" name="name"/>
        <Param val="" valType="num" updates="constant" name="opacity"/>
        <Param val="0" valType="num" updates="constant" name="ori"/>
        <Param val="(0, 0)" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="Resting State task&amp;#10;Please keep looking at the cross for 6 minutes &amp;#10;without thinking about anything in particular.&amp;#10;&amp;#10;Press F1 to start." valType="str" updates="constant" name="text"/>
        <Param val="from exp settings" valType="str" updates="None" name="units"/>
        <Param val="" valType="num" updates="constant" name="wrapWidth"/>
      </TextComponent>
      <KeyboardComponent name="key_resp" plugin="None">
        <Param val="'f1'" valType="list" updates="constant" name="allowedKeys"/>
        <Param val="" valType="str" updates="constant" name="correctAns"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="True" valType="bool" updates="constant" name="discard previous"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="True" valType="bool" updates="constant" name="forceEndRoutine"/>
        <Param val="key_resp" valType="code" updates="None" name="name"/>
        <Param val="press" valType="str" updates="constant" name="registerOn"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="last key" valType="str" updates="constant" name="store"/>
        <Param val="False" valType="bool" updates="constant" name="storeCorrect"/>
        <Param val="True" valType="bool" updates="constant" name="syncScreenRefresh"/>
      </KeyboardComponent>
      <CodeComponent name="t_begin" plugin="None">
        <Param val="" valType="extendedCode" updates="constant" name="Before Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Before JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin Routine"/>
        <Param val="Auto-&gt;JS" valType="str" updates="None" name="Code Type"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each JS Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Experiment"/>
        <Param val="outlet.push_sample({&quot;x&quot;: [1]});&amp;#10;" valType="extendedCode" updates="constant" name="End JS Routine"/>
        <Param val="outlet.push_sample(x=[1])" valType="extendedCode" updates="constant" name="End Routine"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="t_begin" valType="code" updates="None" name="name"/>
      </CodeComponent>
    </Routine>
    <Routine name="_01_08_rest_state_trial">
      <RoutineSettingsComponent name="_01_08_rest_state_trial" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="True" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="_01_08_rest_state_trial" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <CodeComponent name="t_stim" plugin="None">
        <Param val="" valType="extendedCode" updates="constant" name="Before Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Before JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Experiment"/>
        <Param val="outlet.push_sample({&quot;x&quot;: [10]});&amp;#10;" valType="extendedCode" updates="constant" name="Begin JS Routine"/>
        <Param val="outlet.push_sample(x=[10])" valType="extendedCode" updates="constant" name="Begin Routine"/>
        <Param val="Auto-&gt;JS" valType="str" updates="None" name="Code Type"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each JS Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Routine"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="t_stim" valType="code" updates="None" name="name"/>
      </CodeComponent>
      <PolygonComponent name="crosshair" plugin="None">
        <Param val="center" valType="str" updates="constant" name="anchor"/>
        <Param val="rgb" valType="str" updates="constant" name="colorSpace"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="True" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="white" valType="color" updates="constant" name="fillColor"/>
        <Param val="linear" valType="str" updates="constant" name="interpolate"/>
        <Param val="white" valType="color" updates="constant" name="lineColor"/>
        <Param val="1" valType="num" updates="constant" name="lineWidth"/>
        <Param val="4" valType="int" updates="constant" name="nVertices"/>
        <Param val="crosshair" valType="code" updates="None" name="name"/>
        <Param val="" valType="num" updates="constant" name="opacity"/>
        <Param val="0" valType="num" updates="constant" name="ori"/>
        <Param val="(0, 0)" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="cross" valType="str" updates="None" name="shape"/>
        <Param val="(0.2, 0.2)" valType="list" updates="constant" name="size"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="$breath_duration" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="from exp settings" valType="str" updates="None" name="units"/>
        <Param val="" valType="list" updates="constant" name="vertices"/>
      </PolygonComponent>
      <TextComponent name="cross" plugin="None">
        <Param val="white" valType="color" updates="constant" name="color"/>
        <Param val="rgb" valType="str" updates="constant" name="colorSpace"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="None" valType="str" updates="constant" name="flip"/>
        <Param val="Open Sans" valType="str" updates="constant" name="font"/>
        <Param val="LTR" valType="str" updates="None" name="languageStyle"/>
        <Param val="0.05" valType="num" updates="constant" name="letterHeight"/>
        <Param val="cross" valType="code" updates="None" name="name"/>
        <Param val="" valType="num" updates="constant" name="opacity"/>
        <Param val="0" valType="num" updates="constant" name="ori"/>
        <Param val="(0, 0)" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="$breath_duration" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="+" valType="str" updates="constant" name="text"/>
        <Param val="from exp settings" valType="str" updates="None" name="units"/>
        <Param val="" valType="num" updates="constant" name="wrapWidth"/>
      </TextComponent>
    </Routine>
    <Routine name="task_end_question">
      <RoutineSettingsComponent name="task_end_question" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="True" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="task_end_question" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <CodeComponent name="code_questionaire" plugin="None">
        <Param val="" valType="extendedCode" updates="constant" name="Before Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Before JS Experiment"/>
        <Param val="question_counter = 0&amp;#10;question_text = 'Questionnaire #'" valType="extendedCode" updates="constant" name="Begin Experiment"/>
        <Param val="question_counter = 0;&amp;#10;question_text = &quot;Questionnaire #&quot;;&amp;#10;" valType="extendedCode" updates="constant" name="Begin JS Experiment"/>
        <Param val="question_counter += 1;&amp;#10;current_letter = chr((64 + question_counter));&amp;#10;question_text = (&quot;Questionnaire: &quot; + current_letter);&amp;#10;" valType="extendedCode" updates="constant" name="Begin JS Routine"/>
        <Param val="question_counter += 1&amp;#10;current_letter = chr(64 + question_counter)  # 65 is ASCII for 'A'&amp;#10;question_text = 'Questionnaire: ' + current_letter" valType="extendedCode" updates="constant" name="Begin Routine"/>
        <Param val="Auto-&gt;JS" valType="str" updates="None" name="Code Type"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each JS Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Routine"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="code_questionaire" valType="code" updates="None" name="name"/>
      </CodeComponent>
      <TextComponent name="text_question_number" plugin="None">
        <Param val="white" valType="color" updates="constant" name="color"/>
        <Param val="rgb" valType="str" updates="constant" name="colorSpace"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="None" valType="str" updates="constant" name="flip"/>
        <Param val="Open Sans" valType="str" updates="constant" name="font"/>
        <Param val="LTR" valType="str" updates="None" name="languageStyle"/>
        <Param val="0.03" valType="num" updates="constant" name="letterHeight"/>
        <Param val="text_question_number" valType="code" updates="None" name="name"/>
        <Param val="" valType="num" updates="constant" name="opacity"/>
        <Param val="0" valType="num" updates="constant" name="ori"/>
        <Param val="0, 0.4" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="$question_text" valType="str" updates="set every repeat" name="text"/>
        <Param val="from exp settings" valType="str" updates="None" name="units"/>
        <Param val="" valType="num" updates="constant" name="wrapWidth"/>
      </TextComponent>
      <TextComponent name="text_q_stress" plugin="None">
        <Param val="white" valType="color" updates="constant" name="color"/>
        <Param val="rgb" valType="str" updates="constant" name="colorSpace"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="None" valType="str" updates="constant" name="flip"/>
        <Param val="Open Sans" valType="str" updates="constant" name="font"/>
        <Param val="LTR" valType="str" updates="None" name="languageStyle"/>
        <Param val="0.03" valType="num" updates="constant" name="letterHeight"/>
        <Param val="text_q_stress" valType="code" updates="None" name="name"/>
        <Param val="" valType="num" updates="constant" name="opacity"/>
        <Param val="0" valType="num" updates="constant" name="ori"/>
        <Param val="(0, 0)" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="What is your current stress/calm level?&amp;#10;(-10: as stressed as possible,  0: neutral, +10: as calm as possible)?&amp;#10;&amp;#10;&amp;#10;What is your current cap comfort level &amp;#10;(0: super uncomfortable, 10: super comfortable)? &amp;#10;&amp;#10;&amp;#10;Press F1 to continue." valType="str" updates="constant" name="text"/>
        <Param val="from exp settings" valType="str" updates="None" name="units"/>
        <Param val="" valType="num" updates="constant" name="wrapWidth"/>
      </TextComponent>
      <SoundComponent name="exp_end_bip" plugin="None">
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="True" valType="bool" updates="constant" name="hamming"/>
        <Param val="exp_end_bip" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="sound/bip.wav" valType="str" updates="constant" name="sound"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="1.0" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="constant" name="stopWithRoutine"/>
        <Param val="True" valType="bool" updates="constant" name="syncScreenRefresh"/>
        <Param val="1" valType="num" updates="constant" name="volume"/>
      </SoundComponent>
      <KeyboardComponent name="key_q_stress" plugin="None">
        <Param val="'f1'" valType="list" updates="constant" name="allowedKeys"/>
        <Param val="" valType="str" updates="constant" name="correctAns"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="True" valType="bool" updates="constant" name="discard previous"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="True" valType="bool" updates="constant" name="forceEndRoutine"/>
        <Param val="key_q_stress" valType="code" updates="None" name="name"/>
        <Param val="press" valType="str" updates="constant" name="registerOn"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="last key" valType="str" updates="constant" name="store"/>
        <Param val="False" valType="bool" updates="constant" name="storeCorrect"/>
        <Param val="True" valType="bool" updates="constant" name="syncScreenRefresh"/>
      </KeyboardComponent>
    </Routine>
    <Routine name="routine_03_05_07_breathing_video">
      <RoutineSettingsComponent name="routine_03_05_07_breathing_video" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="True" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="routine_03_05_07_breathing_video" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <CodeComponent name="t_stim_3_" plugin="None">
        <Param val="" valType="extendedCode" updates="constant" name="Before Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Before JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Experiment"/>
        <Param val="outlet.push_sample({&quot;x&quot;: [Number.parseInt(31)]});&amp;#10;" valType="extendedCode" updates="constant" name="Begin JS Routine"/>
        <Param val="outlet.push_sample(x=[int(31)])" valType="extendedCode" updates="constant" name="Begin Routine"/>
        <Param val="Auto-&gt;JS" valType="str" updates="None" name="Code Type"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each JS Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Routine"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="t_stim_3_" valType="code" updates="None" name="name"/>
      </CodeComponent>
      <MovieComponent name="video_2" plugin="None">
        <Param val="False" valType="bool" updates="None" name="No audio"/>
        <Param val="center" valType="str" updates="constant" name="anchor"/>
        <Param val="ffpyplayer" valType="str" updates="None" name="backend"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="False" valType="bool" updates="constant" name="forceEndRoutine"/>
        <Param val="False" valType="bool" updates="None" name="loop"/>
        <Param val="$expInfo['breathing_video']" valType="file" updates="set every repeat" name="movie"/>
        <Param val="video_2" valType="code" updates="None" name="name"/>
        <Param val="" valType="num" updates="constant" name="opacity"/>
        <Param val="0" valType="num" updates="constant" name="ori"/>
        <Param val="(0, 0)" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="1.78, 1.0" valType="list" updates="constant" name="size"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="$breath_duration" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="constant" name="stopWithRoutine"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="from exp settings" valType="str" updates="None" name="units"/>
        <Param val="1" valType="num" updates="None" name="volume"/>
      </MovieComponent>
    </Routine>
  </Routines>
  <Flow>
    <Routine name="Load_LSL"/>
    <Routine name="Experiment_Setup"/>
    <Routine name="_01_08_rest_state_instr"/>
    <Routine name="_01_08_rest_state_trial"/>
    <Routine name="task_end_question"/>
    <Routine name="_02_pubspeak_instr"/>
    <Routine name="_02_pubspeak_trial"/>
    <Routine name="task_end_question"/>
    <Routine name="_03_05_07_breathing_instr"/>
    <Routine name="routine_03_05_07_breathing_video"/>
    <Routine name="_03_05_07_breathing_trial"/>
    <Routine name="task_end_question"/>
    <Routine name="_04_arithmetic_instr"/>
    <Routine name="_04_arithmetic_trial"/>
    <Routine name="task_end_question"/>
    <Routine name="_03_05_07_breathing_instr"/>
    <Routine name="_03_05_07_breathing_trial"/>
    <Routine name="task_end_question"/>
    <Routine name="_06_stroop_instr"/>
    <LoopInitiator loopType="TrialHandler" name="trials">
      <Param name="Selected rows" updates="None" val="" valType="str"/>
      <Param name="conditions" updates="None" val="[{'word': 'red', 'wordColor': 'red', 'corrAns': 'left', 'Congruent': 1}, {'word': 'red', 'wordColor': 'green', 'corrAns': 'down', 'Congruent': 0}, {'word': 'red', 'wordColor': 'blue', 'corrAns': 'right', 'Congruent': 0}, {'word': 'green', 'wordColor': 'red', 'corrAns': 'left', 'Congruent': 0}, {'word': 'green', 'wordColor': 'green', 'corrAns': 'down', 'Congruent': 1}, {'word': 'green', 'wordColor': 'blue', 'corrAns': 'right', 'Congruent': 0}, {'word': 'blue', 'wordColor': 'red', 'corrAns': 'left', 'Congruent': 0}, {'word': 'blue', 'wordColor': 'green', 'corrAns': 'down', 'Congruent': 0}, {'word': 'blue', 'wordColor': 'blue', 'corrAns': 'right', 'Congruent': 1}]" valType="str"/>
      <Param name="conditionsFile" updates="None" val="english.xlsx" valType="file"/>
      <Param name="endPoints" updates="None" val="[0, 1]" valType="num"/>
      <Param name="isTrials" updates="None" val="True" valType="bool"/>
      <Param name="loopType" updates="None" val="random" valType="str"/>
      <Param name="nReps" updates="None" val="1" valType="num"/>
      <Param name="name" updates="None" val="trials" valType="code"/>
      <Param name="random seed" updates="None" val="" valType="code"/>
    </LoopInitiator>
    <Routine name="_06_stroop_trial"/>
    <Routine name="_06_stroop_feedback"/>
    <LoopTerminator name="trials"/>
    <Routine name="task_end_question"/>
    <Routine name="_03_05_07_breathing_instr"/>
    <Routine name="_03_05_07_breathing_trial"/>
    <Routine name="task_end_question"/>
    <Routine name="_01_08_rest_state_instr"/>
    <Routine name="_01_08_rest_state_trial"/>
    <Routine name="task_end_question"/>
    <Routine name="End"/>
  </Flow>
</PsychoPy2experiment>
