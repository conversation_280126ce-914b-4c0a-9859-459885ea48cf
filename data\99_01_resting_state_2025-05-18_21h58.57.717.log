7.9950 	WARNING 	User requested fullscreen with size [1536  864], but screen is actually [1920, 1080]. Using actual size
8.6957 	EXP 	Created window1 = Window(allowGUI=True, allowStencil=True, autoLog=True, backendConf=UNKNOWN, backgroundFit=<method-wrapper '__getattribute__' of attributeSetter object at 0x0000016D81AA9C40>, backgroundImage=<method-wrapper '__getattribute__' of attributeSetter object at 0x0000016D81AA9BE0>, bitsMode=UNKNOWN, blendMode='avg', bpc=(8, 8, 8), color=array([0, 0, 0]), colorSpace='rgb', depthBits=8, fullscr=<method-wrapper '__getattribute__' of attributeSetter object at 0x0000016D81AA9A30>, gamma=None, gammaErrorPolicy='raise', lms=UNKNOWN, monitor=<psychopy.monitors.calibTools.Monitor object at 0x0000016D8317F520>, multiSample=False, name='window1', numSamples=2, pos=(0, 0), screen=0, size=array([1920, 1080]), stencilBits=8, stereo=False, title='PsychoPy', units='height', useFBO=True, useRetina=False, viewOri=0.0, viewPos=None, viewScale=None, waitBlanking=True, winType='pyglet')
8.6959 	EXP 	window1: mouseVisible = True
8.6959 	EXP 	window1: backgroundImage = ''
8.6959 	EXP 	window1: backgroundFit = 'none'
8.6986 	EXP 	window1: Attempting to measure frame rate of screen (0) ...
8.6987 	EXP 	window1: recordFrameIntervals = False
8.8623 	EXP 	window1: recordFrameIntervals = True
9.0446 	EXP 	Screen (0) actual frame rate measured at 60.13Hz
9.0447 	EXP 	window1: recordFrameIntervals = False
9.0453 	EXP 	window1: mouseVisible = False
12.4099 	EXP 	01_resting_state: status = STARTED
12.7695 	EXP 	window1: mouseVisible = True
12.8815 	EXP 	Created text = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.8817 	EXP 	window1: mouseVisible = True
12.8880 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
12.8882 	EXP 	window1: mouseVisible = True
12.9025 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.9052 	EXP 	Sound exp_end_bip set volume 1.000
12.9055 	EXP 	window1: mouseVisible = True
12.9186 	EXP 	Created text_2 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_2', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.9239 	EXP 	window1: mouseVisible = True
12.9556 	EXP 	window1: mouseVisible = True
12.9668 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.9700 	EXP 	Sound exp_end_bip set volume 1.000
12.9703 	EXP 	window1: mouseVisible = True
12.9811 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.9814 	EXP 	window1: mouseVisible = True
12.9928 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
12.9960 	EXP 	Sound exp_end_bip set volume 1.000
12.9962 	EXP 	window1: mouseVisible = True
13.0078 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.0108 	EXP 	Sound exp_end_bip set volume 1.000
13.0110 	EXP 	window1: mouseVisible = True
13.0185 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.0188 	EXP 	window1: mouseVisible = True
13.0301 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.0325 	EXP 	Sound exp_end_bip set volume 1.000
13.0330 	EXP 	window1: mouseVisible = True
13.0520 	EXP 	Created stroop_instr = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='stroop_instr', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.0522 	EXP 	window1: mouseVisible = True
13.0644 	EXP 	Created instrText_4 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-2.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_4', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.0646 	EXP 	window1: mouseVisible = True
13.0666 	EXP 	Created stim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.15, italic=False, languageStyle='LTR', name='stim', opacity=1.0, ori=0, pos=array([0., 0.]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
13.0669 	EXP 	window1: mouseVisible = True
13.0671 	EXP 	window1: mouseVisible = True
13.0676 	EXP 	window1: mouseVisible = True
13.0681 	EXP 	window1: mouseVisible = True
13.0744 	EXP 	window1: mouseVisible = True
13.0745 	EXP 	window1: mouseVisible = True
13.0752 	EXP 	window1: mouseVisible = True
13.0757 	EXP 	window1: mouseVisible = True
13.1148 	EXP 	window1: mouseVisible = True
13.1213 	EXP 	Created instrText_2 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-4.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_2', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.1216 	EXP 	window1: mouseVisible = True
13.1218 	EXP 	window1: mouseVisible = True
13.1224 	EXP 	window1: mouseVisible = True
13.1229 	EXP 	window1: mouseVisible = True
13.1279 	EXP 	window1: mouseVisible = True
13.1281 	EXP 	window1: mouseVisible = True
13.1286 	EXP 	window1: mouseVisible = True
13.1291 	EXP 	window1: mouseVisible = True
13.1341 	EXP 	window1: mouseVisible = True
13.1343 	EXP 	window1: mouseVisible = True
13.1350 	EXP 	window1: mouseVisible = True
13.1357 	EXP 	window1: mouseVisible = True
13.1409 	EXP 	window1: mouseVisible = True
13.1410 	EXP 	window1: mouseVisible = True
13.1417 	EXP 	window1: mouseVisible = True
13.1424 	EXP 	window1: mouseVisible = True
13.1490 	EXP 	window1: mouseVisible = True
13.1564 	EXP 	Created instrText_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-3.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_3', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.1566 	EXP 	window1: mouseVisible = True
13.1793 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.1818 	EXP 	Sound exp_end_bip set volume 1.000
13.1820 	EXP 	window1: mouseVisible = True
13.1932 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.1934 	EXP 	window1: mouseVisible = True
13.2017 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.2050 	EXP 	Sound exp_end_bip set volume 1.000
13.2054 	EXP 	window1: mouseVisible = True
13.2140 	EXP 	Created text = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.2143 	EXP 	window1: mouseVisible = True
13.2188 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
13.2190 	EXP 	window1: mouseVisible = True
13.2334 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.2357 	EXP 	Sound exp_end_bip set volume 1.000
13.2359 	EXP 	window1: mouseVisible = True
13.2445 	EXP 	Created EndMessage = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='EndMessage', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='Thank you!', units='height', win=Window(...), wrapWidth=1)
0.0051 	EXP 	video: autoLog = True
0.0051 	EXP 	trial_counter: autoLog = True
0.0051 	EXP 	fbtxt: autoLog = True
0.0051 	EXP 	trial_counter_2: autoLog = True
0.0155 	EXP 	text: autoDraw = True
0.6369 	DATA 	Keypress: f1
0.6467 	EXP 	text: autoDraw = False
0.6467 	EXP 	cross: autoDraw = True
11.6542 	EXP 	Sound exp_end_bip started
11.6545 	EXP 	cross: autoDraw = False
11.6545 	EXP 	cross: autoDraw = False
11.6545 	EXP 	text_q_stress: autoDraw = True
12.4217 	EXP 	Sound exp_end_bip stopped
12.4218 	EXP 	Sound exp_end_bip paused
12.4259 	DATA 	Keypress: f1
12.4304 	EXP 	text_q_stress: autoDraw = False
12.4304 	EXP 	text_2: autoDraw = True
13.0327 	DATA 	Keypress: f1
13.0401 	EXP 	text_2: autoDraw = False
13.0401 	EXP 	video: autoDraw = True
15.3021 	EXP 	Sound exp_end_bip reached end of file
15.3163 	EXP 	Sound exp_end_bip started
15.3086 	EXP 	video: autoDraw = False
15.3086 	EXP 	video: autoDraw = False
15.3086 	EXP 	text_q_stress: autoDraw = True
15.8993 	EXP 	Sound exp_end_bip stopped
15.8994 	EXP 	Sound exp_end_bip paused
15.9031 	DATA 	Keypress: f1
15.9076 	EXP 	text_q_stress: autoDraw = False
15.9076 	EXP 	text_3: autoDraw = True
16.3020 	EXP 	window1: mouseVisible = True
16.3060 	EXP 	Created unnamed ShapeStim = ShapeStim(__class__=<class 'psychopy.visual.shape.ShapeStim'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=False, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0, 0, 0]), fillColorSpace='rgb', fillRGB=array([0, 0, 0]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=3, name='unnamed ShapeStim', opacity=0.35, ori=0.0, pos=array([0., 0.]), size=array([1., 1.]), units='height', vertices=ndarray(...), win=Window(...), windingRule=None)
16.3141 	EXP 	window1: mouseVisible = True
16.3237 	EXP 	Created unnamed Circle = Circle(__class__=<class 'psychopy.visual.circle.Circle'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=2, name='unnamed Circle', opacity=1.0, ori=0.0, pos=array([0., 0.]), size=array([1., 1.]), units='height', vertices=ndarray(...), win=Window(...))
16.3240 	EXP 	window1: mouseVisible = True
16.3258 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.05882353, 0.61568627, 0.84313725]), fillColorSpace='rgb', fillRGB=array([0.05882353, 0.61568627, 0.84313725]), interpolate=True, lineColor=array([0., 0., 0.]), lineColorSpace='rgb', lineRGB=array([0, 0, 0]), lineWidth=1.5, name='unnamed Rect', opacity=0.1, ori=0.0, pos=array([0., 0.]), size=array([1.77777778, 0.5       ]), units='height', vertices=ndarray(...), win=Window(...))
16.3260 	EXP 	window1: mouseVisible = True
16.3345 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([ 0.  , -0.35]), rgb=UNKNOWN, text='Inhale', units='height', win=Window(...), wrapWidth=1)
16.3347 	EXP 	window1: mouseVisible = True
16.3364 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([0. , 0.4]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
16.3366 	EXP 	window1: mouseVisible = True
16.3382 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([-0.0462963, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3383 	EXP 	window1: mouseVisible = True
16.3399 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([-0.0212963, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3400 	EXP 	window1: mouseVisible = True
16.3419 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.0037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3421 	EXP 	window1: mouseVisible = True
16.3437 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.0287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3438 	EXP 	window1: mouseVisible = True
16.3454 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.0537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3456 	EXP 	window1: mouseVisible = True
16.3475 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.0787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3476 	EXP 	window1: mouseVisible = True
16.3490 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.1037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3491 	EXP 	window1: mouseVisible = True
16.3508 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.1287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3509 	EXP 	window1: mouseVisible = True
16.3526 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.1537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3527 	EXP 	window1: mouseVisible = True
16.3541 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.1787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3542 	EXP 	window1: mouseVisible = True
16.3561 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.2037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3562 	EXP 	window1: mouseVisible = True
16.3578 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.2287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3579 	EXP 	window1: mouseVisible = True
16.3593 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.2537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3594 	EXP 	window1: mouseVisible = True
16.3614 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.2787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3615 	EXP 	window1: mouseVisible = True
16.3636 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.3037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3637 	EXP 	window1: mouseVisible = True
16.3652 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.3287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3653 	EXP 	window1: mouseVisible = True
16.3671 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.3537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3672 	EXP 	window1: mouseVisible = True
16.3687 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.3787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3688 	EXP 	window1: mouseVisible = True
16.3703 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.4037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3704 	EXP 	window1: mouseVisible = True
16.3724 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.4287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3725 	EXP 	window1: mouseVisible = True
16.3740 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.4537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3742 	EXP 	window1: mouseVisible = True
16.3757 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.4787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3757 	EXP 	window1: mouseVisible = True
16.3772 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.5037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3773 	EXP 	window1: mouseVisible = True
16.3790 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.5287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3792 	EXP 	window1: mouseVisible = True
16.3810 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.5537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3811 	EXP 	window1: mouseVisible = True
16.3831 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.5787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3832 	EXP 	window1: mouseVisible = True
16.3850 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.6037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3851 	EXP 	window1: mouseVisible = True
16.3867 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.6287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3868 	EXP 	window1: mouseVisible = True
16.3883 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.6537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3884 	EXP 	window1: mouseVisible = True
16.3898 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.6787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
16.3899 	EXP 	window1: mouseVisible = True
16.3984 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([-0.49444444, -0.4       ]), rgb=UNKNOWN, text='Round: 1/3', units='height', win=Window(...), wrapWidth=1)
16.3986 	EXP 	window1: mouseVisible = True
16.3987 	WARNING 	TextStim.alignHoriz is deprecated. Use alignText and anchorHoriz attributes instead
16.4040 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz='left', alignText='left', alignVert=method-wrapper(...), anchorHoriz='left', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([-0.2462963, -0.4      ]), rgb=UNKNOWN, text='Breaths:', units='height', win=Window(...), wrapWidth=1)
16.4364 	DATA 	Keypress: f1
16.4414 	EXP 	text_3: autoDraw = False
16.4414 	EXP 	unnamed TextStim: height = 0.05
16.4414 	EXP 	unnamed TextStim: height = 0.05
16.4414 	EXP 	unnamed TextStim: height = 0.05
16.4414 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.4414 	EXP 	unnamed TextStim: height = 0.03
16.4414 	EXP 	unnamed TextStim: height = 0.03
16.4414 	EXP 	unnamed TextStim: height = 0.03
16.4414 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.4414 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.4620 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.4620 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.4620 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.4848 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.4848 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.4848 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.5067 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.5067 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.5067 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.5274 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.5274 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.5274 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.5479 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.5479 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.5479 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.5707 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.5707 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.5707 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.5940 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.5940 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.5940 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.6175 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.6175 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.6175 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.6389 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.6389 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.6389 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.6607 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.6607 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.6607 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.6859 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.6859 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.6859 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.7071 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.7071 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.7071 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.7286 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.7286 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.7286 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.7553 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.7553 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.7553 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.7774 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.7774 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.7774 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.8002 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.8002 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.8002 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.8210 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.8210 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.8210 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.8414 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.8414 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.8414 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.8638 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.8638 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.8638 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.8870 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.8870 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.8870 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.9096 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.9096 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.9096 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.9349 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.9349 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.9349 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.9550 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.9550 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.9550 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.9756 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.9756 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.9756 	EXP 	unnamed TextStim: text = 'Round: 1/3'
16.9969 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
16.9969 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
16.9969 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.0210 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.0210 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
17.0210 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.0432 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.0432 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
17.0432 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.0675 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.0675 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
17.0675 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.0895 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.0895 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
17.0895 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.1123 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.1123 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
17.1123 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.1355 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.1355 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
17.1355 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.1587 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.1587 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
17.1587 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.1825 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.1825 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
17.1825 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.2087 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.2087 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
17.2087 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.2341 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.2341 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
17.2341 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.2574 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.2574 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
17.2574 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.2823 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.2823 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
17.2823 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.3074 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.3074 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
17.3074 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.3290 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.3290 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
17.3290 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.3509 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.3509 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
17.3509 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.3747 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.3747 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
17.3747 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.3959 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.3959 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
17.3959 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.4180 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.4180 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
17.4180 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.4481 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.4481 	EXP 	unnamed TextStim: height = 0.03
17.4481 	EXP 	unnamed TextStim: height = 0.03
17.4481 	EXP 	unnamed TextStim: height = 0.03
17.4481 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.4481 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.4767 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.4767 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.4767 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.5008 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.5008 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.5008 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.5248 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.5248 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.5248 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.5469 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.5469 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.5469 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.5680 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.5680 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.5680 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.5894 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.5894 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.5894 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.6118 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.6118 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.6118 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.6381 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.6381 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.6381 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.6610 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.6610 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.6610 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.6896 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.6896 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.6896 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.7110 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.7110 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.7110 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.7324 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.7324 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.7324 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.7542 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.7542 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.7542 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.7747 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.7747 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.7747 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.7970 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.7970 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.7970 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.8199 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.8199 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.8199 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.8424 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.8424 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.8424 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.8650 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.8650 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.8650 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.8878 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.8878 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.8878 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.9112 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
17.9112 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.9112 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.9447 	EXP 	unnamed TextStim: height = 0.05
17.9447 	EXP 	unnamed TextStim: height = 0.05
17.9447 	EXP 	unnamed TextStim: height = 0.05
17.9447 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
17.9447 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.9447 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.9676 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
17.9676 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.9676 	EXP 	unnamed TextStim: text = 'Round: 1/3'
17.9865 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
17.9865 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
17.9865 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.0057 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.0057 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
18.0057 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.0269 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.0269 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
18.0269 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.0508 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.0508 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
18.0508 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.0745 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.0745 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
18.0745 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.1008 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.1008 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
18.1008 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.1218 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.1218 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
18.1218 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.1471 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.1471 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
18.1471 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.1724 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.1724 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
18.1724 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.1977 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.1977 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
18.1977 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.2193 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.2193 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
18.2193 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.2400 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.2400 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
18.2400 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.2603 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.2603 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
18.2603 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.2830 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.2830 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
18.2830 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.3094 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.3094 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
18.3094 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.3381 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.3381 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
18.3381 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.3678 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.3678 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
18.3678 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.3932 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.3932 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
18.3932 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.4144 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.4144 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
18.4144 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.4390 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.4390 	EXP 	unnamed TextStim: height = 0.03
18.4390 	EXP 	unnamed TextStim: height = 0.03
18.4390 	EXP 	unnamed TextStim: height = 0.03
18.4390 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.4390 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.4590 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.4590 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.4590 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.4836 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.4836 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.4836 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.5099 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.5099 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.5099 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.5314 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.5314 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.5314 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.5530 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.5530 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.5530 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.5739 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.5739 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.5739 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.5958 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.5958 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.5958 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.6170 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.6170 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.6170 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.6415 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.6415 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.6415 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.6710 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.6710 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.6710 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.6945 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.6945 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.6945 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.7167 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.7167 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.7167 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.7380 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.7380 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.7380 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.7601 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.7601 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.7601 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.7816 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.7816 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.7816 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.8039 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.8039 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.8039 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.8350 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.8350 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.8350 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.8543 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.8543 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.8543 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.8741 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.8741 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.8741 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.8927 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.8927 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.8927 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.9132 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.9132 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.9132 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.9329 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.9329 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.9329 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.9552 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.9552 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.9552 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.9740 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.9740 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.9740 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.9934 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
18.9934 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
18.9934 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.0143 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.0143 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
19.0143 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.0362 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.0362 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
19.0362 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.0610 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.0610 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
19.0610 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.0826 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.0826 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
19.0826 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.1113 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.1113 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
19.1113 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.1333 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.1333 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
19.1333 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.1621 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.1621 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
19.1621 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.1929 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.1929 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
19.1929 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.2173 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.2173 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
19.2173 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.2410 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.2410 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
19.2410 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.2623 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.2623 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
19.2623 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.2821 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.2821 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
19.2821 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.3015 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.3015 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
19.3015 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.3236 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.3236 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
19.3236 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.3432 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.3432 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
19.3432 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.3673 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.3673 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
19.3673 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.3896 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.3896 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
19.3896 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.4107 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.4107 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
19.4107 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.4380 	EXP 	unnamed TextStim: height = 0.05
19.4380 	EXP 	unnamed TextStim: height = 0.05
19.4380 	EXP 	unnamed TextStim: height = 0.05
19.4380 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.4380 	EXP 	unnamed TextStim: height = 0.03
19.4380 	EXP 	unnamed TextStim: height = 0.03
19.4380 	EXP 	unnamed TextStim: height = 0.03
19.4380 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.4380 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.4597 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.4597 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.4597 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.4809 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.4809 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.4809 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.5068 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.5068 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.5068 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.5287 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.5287 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.5287 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.5522 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.5522 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.5522 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.5769 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.5769 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.5769 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.5985 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.5985 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.5985 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.6237 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.6237 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.6237 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.6428 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.6428 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.6428 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.6624 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.6624 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.6624 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.6821 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.6821 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.6821 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.7060 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.7060 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.7060 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.7251 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.7251 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.7251 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.7462 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.7462 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.7462 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.7715 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.7715 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.7715 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.7926 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.7926 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.7926 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.8184 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.8184 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.8184 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.8402 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.8402 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.8402 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.8615 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.8615 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.8615 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.8821 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.8821 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.8821 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.9068 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.9068 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.9068 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.9275 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.9275 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.9275 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.9488 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.9488 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.9488 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.9716 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.9716 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.9716 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.9901 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
19.9901 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
19.9901 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.0091 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.0091 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
20.0091 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.0311 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.0311 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
20.0311 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.0519 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.0519 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
20.0519 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.0738 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.0738 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
20.0738 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.0947 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.0947 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
20.0947 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.1175 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.1175 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
20.1175 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.1397 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.1397 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
20.1397 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.1612 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.1612 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
20.1612 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.1824 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.1824 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
20.1824 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.2040 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.2040 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
20.2040 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.2249 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.2249 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
20.2249 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.2476 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.2476 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
20.2476 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.2694 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.2694 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
20.2694 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.2949 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.2949 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
20.2949 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.3202 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.3202 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
20.3202 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.3489 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.3489 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
20.3489 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.3755 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.3755 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
20.3755 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.3989 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.3989 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
20.3989 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.4243 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.4243 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
20.4243 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.4522 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.4522 	EXP 	unnamed TextStim: height = 0.03
20.4522 	EXP 	unnamed TextStim: height = 0.03
20.4522 	EXP 	unnamed TextStim: height = 0.03
20.4522 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.4522 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.4754 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.4754 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.4754 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.4982 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.4982 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.4982 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.5212 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.5212 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.5212 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.5411 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.5411 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.5411 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.5628 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.5628 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.5628 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.5855 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.5855 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.5855 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.6075 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.6075 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.6075 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.6293 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.6293 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.6293 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.6610 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.6610 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.6610 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.6869 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.6869 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.6869 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.7061 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.7061 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.7061 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.7254 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.7254 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.7254 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.7475 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.7475 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.7475 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.7685 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.7685 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.7685 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.7893 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.7893 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.7893 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.8110 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.8110 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.8110 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.8326 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.8326 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.8326 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.8584 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.8584 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.8584 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.8892 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.8892 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.8892 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.9150 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
20.9150 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.9150 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.9426 	EXP 	unnamed TextStim: height = 0.05
20.9426 	EXP 	unnamed TextStim: height = 0.05
20.9426 	EXP 	unnamed TextStim: height = 0.05
20.9426 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
20.9426 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.9426 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.9684 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
20.9684 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.9684 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.9883 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
20.9883 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
20.9883 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.0085 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.0085 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
21.0085 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.0317 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.0317 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
21.0317 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.0525 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.0525 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
21.0525 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.0803 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.0803 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
21.0803 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.1047 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.1047 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
21.1047 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.1281 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.1281 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
21.1281 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.1494 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.1494 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
21.1494 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.1758 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.1758 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
21.1758 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.1981 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.1981 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
21.1981 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.2199 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.2199 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
21.2199 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.2472 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.2472 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
21.2472 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.2704 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.2704 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
21.2704 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.2929 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.2929 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
21.2929 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.3145 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.3145 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
21.3145 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.3410 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.3410 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
21.3410 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.3703 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.3703 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
21.3703 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.3948 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.3948 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
21.3948 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.4213 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.4213 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
21.4213 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.4521 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.4521 	EXP 	unnamed TextStim: height = 0.03
21.4521 	EXP 	unnamed TextStim: height = 0.03
21.4521 	EXP 	unnamed TextStim: height = 0.03
21.4521 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.4521 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.4737 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.4737 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.4737 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.4946 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.4946 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.4946 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.5174 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.5174 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.5174 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.5396 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.5396 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.5396 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.5611 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.5611 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.5611 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.5841 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.5841 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.5841 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.6050 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.6050 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.6050 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.6269 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.6269 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.6269 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.6507 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.6507 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.6507 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.6772 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.6772 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.6772 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.7062 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.7062 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.7062 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.7284 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.7284 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.7284 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.7494 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.7494 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.7494 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.7734 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.7734 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.7734 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.7972 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.7972 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.7972 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.8195 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.8195 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.8195 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.8416 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.8416 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.8416 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.8638 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.8638 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.8638 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.8843 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.8843 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.8843 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.9054 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.9054 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.9054 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.9309 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.9309 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.9309 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.9522 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.9522 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.9522 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.9761 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.9761 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.9761 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.9978 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
21.9978 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
21.9978 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.0190 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.0190 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
22.0190 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.0411 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.0411 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
22.0411 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.0638 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.0638 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
22.0638 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.0862 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.0862 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
22.0862 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.1079 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.1079 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
22.1079 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.1300 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.1300 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
22.1300 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.1538 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.1538 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
22.1538 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.1773 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.1773 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
22.1773 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.2002 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.2002 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
22.2002 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.2204 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.2204 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
22.2204 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.2468 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.2468 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
22.2468 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.2734 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.2734 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
22.2734 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.2945 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.2945 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
22.2945 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.3134 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.3134 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
22.3134 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.3332 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.3332 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
22.3332 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.3552 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.3552 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
22.3552 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.3781 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.3781 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
22.3781 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.4008 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.4008 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
22.4008 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.4236 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.4236 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
22.4236 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.4521 	EXP 	unnamed TextStim: height = 0.05
22.4521 	EXP 	unnamed TextStim: height = 0.05
22.4521 	EXP 	unnamed TextStim: height = 0.05
22.4521 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.4521 	EXP 	unnamed TextStim: height = 0.03
22.4521 	EXP 	unnamed TextStim: height = 0.03
22.4521 	EXP 	unnamed TextStim: height = 0.03
22.4521 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.4521 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.4725 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.4725 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.4725 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.4975 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.4975 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.4975 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.5171 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.5171 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.5171 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.5452 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.5452 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.5452 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.5665 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.5665 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.5665 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.5850 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.5850 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.5850 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.6036 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.6036 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.6036 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.6218 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.6218 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.6218 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.6410 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.6410 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.6410 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.6629 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.6629 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.6629 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.6827 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.6827 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.6827 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.7060 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.7060 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.7060 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.7275 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.7275 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.7275 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.7488 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.7488 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.7488 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.7697 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.7697 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.7697 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.7925 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.7925 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.7925 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.8168 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.8168 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.8168 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.8399 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.8399 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.8399 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.8608 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.8608 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.8608 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.8828 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.8828 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.8828 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.9013 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.9013 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.9013 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.9252 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.9252 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.9252 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.9484 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.9484 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.9484 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.9675 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.9675 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.9675 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.9923 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
22.9923 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
22.9923 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.0126 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.0126 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
23.0126 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.0348 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.0348 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
23.0348 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.0583 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.0583 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
23.0583 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.0839 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.0839 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
23.0839 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.1093 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.1093 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
23.1093 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.1293 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.1293 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
23.1293 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.1511 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.1511 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
23.1511 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.1714 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.1714 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
23.1714 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.1933 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.1933 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
23.1933 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.2143 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.2143 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
23.2143 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.2349 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.2349 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
23.2349 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.2555 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.2555 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
23.2555 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.2752 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.2752 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
23.2752 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.2961 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.2961 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
23.2961 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.3166 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.3166 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
23.3166 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.3378 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.3378 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
23.3378 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.3589 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.3589 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
23.3589 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.3809 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.3809 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
23.3809 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.4028 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.4028 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
23.4028 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.4264 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.4264 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
23.4264 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.4512 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.4512 	EXP 	unnamed TextStim: height = 0.03
23.4512 	EXP 	unnamed TextStim: height = 0.03
23.4512 	EXP 	unnamed TextStim: height = 0.03
23.4512 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.4512 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.4749 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.4749 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.4749 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.4963 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.4963 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.4963 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.5185 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.5185 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.5185 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.5407 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.5407 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.5407 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.5634 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.5634 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.5634 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.5873 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.5873 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.5873 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.6094 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.6094 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.6094 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.6314 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.6314 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.6314 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.6541 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.6541 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.6541 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.6761 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.6761 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.6761 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.6988 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.6988 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.6988 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.7225 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.7225 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.7225 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.7449 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.7449 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.7449 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.7709 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.7709 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.7709 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.7939 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.7939 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.7939 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.8198 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.8198 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.8198 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.8438 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.8438 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.8438 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.8669 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.8669 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.8669 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.8893 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.8893 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.8893 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.9142 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
23.9142 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.9142 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.9430 	EXP 	unnamed TextStim: height = 0.05
23.9430 	EXP 	unnamed TextStim: height = 0.05
23.9430 	EXP 	unnamed TextStim: height = 0.05
23.9430 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
23.9430 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.9430 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.9649 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
23.9649 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.9649 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.9886 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
23.9886 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
23.9886 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.0128 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.0128 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
24.0128 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.0351 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.0351 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
24.0351 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.0567 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.0567 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
24.0567 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.0808 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.0808 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
24.0808 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.1047 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.1047 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
24.1047 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.1262 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.1262 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
24.1262 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.1487 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.1487 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
24.1487 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.1719 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.1719 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
24.1719 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.1992 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.1992 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
24.1992 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.2279 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.2279 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
24.2279 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.2585 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.2585 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
24.2585 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.2845 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.2845 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
24.2845 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.3116 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.3116 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
24.3116 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.3377 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.3377 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
24.3377 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.3665 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.3665 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
24.3665 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.3884 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.3884 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
24.3884 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.4147 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.4147 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
24.4147 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.4404 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.4404 	EXP 	unnamed TextStim: height = 0.03
24.4404 	EXP 	unnamed TextStim: height = 0.03
24.4404 	EXP 	unnamed TextStim: height = 0.03
24.4404 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.4404 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.4638 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.4638 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.4638 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.4858 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.4858 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.4858 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.5100 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.5100 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.5100 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.5340 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.5340 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.5340 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.5551 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.5551 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.5551 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.5796 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.5796 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.5796 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.6030 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.6030 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.6030 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.6291 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.6291 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.6291 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.6531 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.6531 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.6531 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.6789 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.6789 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.6789 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.7009 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.7009 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.7009 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.7219 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.7219 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.7219 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.7447 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.7447 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.7447 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.7672 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.7672 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.7672 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.7885 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.7885 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.7885 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.8112 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.8112 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.8112 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.8397 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.8397 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.8397 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.8689 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.8689 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.8689 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.8912 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.8912 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.8912 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.9143 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.9143 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.9143 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.9341 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.9341 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.9341 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.9541 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.9541 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.9541 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.9765 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.9765 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.9765 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.9975 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
24.9975 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
24.9975 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.0167 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.0167 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
25.0167 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.0364 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.0364 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
25.0364 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.0567 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.0567 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
25.0567 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.0776 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.0776 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
25.0776 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.0983 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.0983 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
25.0983 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.1194 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.1194 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
25.1194 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.1421 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.1421 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
25.1421 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.1628 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.1628 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
25.1628 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.1825 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.1825 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
25.1825 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.2046 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.2046 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
25.2046 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.2275 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.2275 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
25.2275 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.2478 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.2478 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
25.2478 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.2686 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.2686 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
25.2686 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.2890 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.2890 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
25.2890 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.3090 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.3090 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
25.3090 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.3286 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.3286 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
25.3286 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.3489 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.3489 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
25.3489 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.3708 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.3708 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
25.3708 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.3930 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.3930 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
25.3930 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.4130 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.4130 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
25.4130 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.4391 	EXP 	unnamed TextStim: height = 0.05
25.4391 	EXP 	unnamed TextStim: height = 0.05
25.4391 	EXP 	unnamed TextStim: height = 0.05
25.4391 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.4391 	EXP 	unnamed TextStim: height = 0.03
25.4391 	EXP 	unnamed TextStim: height = 0.03
25.4391 	EXP 	unnamed TextStim: height = 0.03
25.4391 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.4391 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.4623 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.4623 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.4623 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.4865 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.4865 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.4865 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.5110 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.5110 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.5110 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.5344 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.5344 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.5344 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.5544 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.5544 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.5544 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.5772 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.5772 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.5772 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.5981 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.5981 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.5981 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.6196 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.6196 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.6196 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.6402 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.6402 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.6402 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.6619 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.6619 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.6619 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.6826 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.6826 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.6826 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.7040 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.7040 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.7040 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.7253 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.7253 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.7253 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.7474 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.7474 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.7474 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.7697 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.7697 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.7697 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.7908 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.7908 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.7908 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.8128 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.8128 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.8128 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.8394 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.8394 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.8394 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.8679 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.8679 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.8679 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.8894 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.8894 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.8894 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.9103 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.9103 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.9103 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.9317 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.9317 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.9317 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.9519 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.9519 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.9519 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.9739 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
25.9739 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
25.9739 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.0002 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.0002 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
26.0002 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.0224 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.0224 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
26.0224 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.0451 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.0451 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
26.0451 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.0675 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.0675 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
26.0675 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.0891 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.0891 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
26.0891 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.1145 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.1145 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
26.1145 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.1372 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.1372 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
26.1372 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.1594 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.1594 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
26.1594 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.1869 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.1869 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
26.1869 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.2128 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.2128 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
26.2128 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.2360 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.2360 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
26.2360 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.2596 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.2596 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
26.2596 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.2808 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.2808 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
26.2808 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.3025 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.3025 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
26.3025 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.3235 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.3235 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
26.3235 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.3447 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.3447 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
26.3447 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.3696 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.3696 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
26.3696 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.3934 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.3934 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
26.3934 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.4156 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.4156 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
26.4156 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.4404 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.4404 	EXP 	unnamed TextStim: height = 0.03
26.4404 	EXP 	unnamed TextStim: height = 0.03
26.4404 	EXP 	unnamed TextStim: height = 0.03
26.4404 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.4404 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.4651 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.4651 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.4651 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.4871 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.4871 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.4871 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.5108 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.5108 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.5108 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.5356 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.5356 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.5356 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.5572 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.5572 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.5572 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.5816 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.5816 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.5816 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.6038 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.6038 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.6038 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.6270 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.6270 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.6270 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.6490 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.6490 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.6490 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.6715 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.6715 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.6715 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.6953 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.6953 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.6953 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.7176 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.7176 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.7176 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.7396 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.7396 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.7396 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.7623 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.7623 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.7623 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.7848 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.7848 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.7848 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.8085 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.8085 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.8085 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.8309 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.8309 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.8309 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.8516 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.8516 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.8516 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.8745 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.8745 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.8745 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.8965 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.8965 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.8965 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.9188 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
26.9188 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.9188 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.9447 	EXP 	unnamed TextStim: height = 0.05
26.9447 	EXP 	unnamed TextStim: height = 0.05
26.9447 	EXP 	unnamed TextStim: height = 0.05
26.9447 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
26.9447 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.9447 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.9670 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
26.9670 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.9670 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.9895 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
26.9895 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
26.9895 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.0128 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.0128 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
27.0128 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.0373 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.0373 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
27.0373 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.0592 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.0592 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
27.0592 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.0803 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.0803 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
27.0803 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.0997 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.0997 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
27.0997 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.1201 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.1201 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
27.1201 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.1414 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.1414 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
27.1414 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.1634 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.1634 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
27.1634 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.1843 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.1843 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
27.1843 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.2058 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.2058 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
27.2058 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.2272 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.2272 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
27.2272 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.2503 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.2503 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
27.2503 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.2733 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.2733 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
27.2733 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.2967 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.2967 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
27.2967 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.3193 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.3193 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
27.3193 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.3430 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.3430 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
27.3430 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.3673 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.3673 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
27.3673 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.3903 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.3903 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
27.3903 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.4125 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.4125 	EXP 	unnamed TextStim: text = 'Time remaining: 349s'
27.4125 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.4373 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.4373 	EXP 	unnamed TextStim: height = 0.03
27.4373 	EXP 	unnamed TextStim: height = 0.03
27.4373 	EXP 	unnamed TextStim: height = 0.03
27.4373 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.4373 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.4597 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.4597 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.4597 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.4814 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.4814 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.4814 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.5027 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.5027 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.5027 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.5259 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.5259 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.5259 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.5509 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.5509 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.5509 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.5724 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.5724 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.5724 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.5938 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.5938 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.5938 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.6150 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.6150 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.6150 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.6401 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.6401 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.6401 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.6639 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.6639 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.6639 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.6866 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.6866 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.6866 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.7095 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.7095 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.7095 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.7303 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.7303 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.7303 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.7508 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.7508 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.7508 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.7732 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.7732 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.7732 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.7967 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.7967 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.7967 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.8183 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.8183 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.8183 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.8421 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.8421 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.8421 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.8637 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.8637 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.8637 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.8881 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.8881 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.8881 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.9097 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.9097 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.9097 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.9309 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.9309 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.9309 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.9513 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.9513 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.9513 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.9733 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.9733 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.9733 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.9931 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
27.9931 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
27.9931 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.0167 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
28.0167 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
28.0167 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.0393 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
28.0393 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
28.0393 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.0612 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
28.0612 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
28.0612 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.0825 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
28.0825 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
28.0825 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.1088 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
28.1088 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
28.1088 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.1331 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
28.1331 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
28.1331 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.1584 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
28.1584 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
28.1584 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.1856 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
28.1856 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
28.1856 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.2127 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
28.2127 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
28.2127 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.2387 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
28.2387 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
28.2387 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.2589 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
28.2589 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
28.2589 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.2794 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
28.2794 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
28.2794 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.3001 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
28.3001 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
28.3001 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.3220 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
28.3220 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
28.3220 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.3460 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
28.3460 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
28.3460 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.3691 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
28.3691 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
28.3691 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.3904 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
28.3904 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
28.3904 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.4108 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (4/30)'
28.4108 	EXP 	unnamed TextStim: text = 'Time remaining: 348s'
28.4108 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.4369 	EXP 	unnamed TextStim: height = 0.05
28.4369 	EXP 	unnamed TextStim: height = 0.05
28.4369 	EXP 	unnamed TextStim: height = 0.05
28.4369 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.4369 	EXP 	unnamed TextStim: height = 0.03
28.4369 	EXP 	unnamed TextStim: height = 0.03
28.4369 	EXP 	unnamed TextStim: height = 0.03
28.4369 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.4369 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.4577 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.4577 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.4577 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.4786 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.4786 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.4786 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.4990 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.4990 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.4990 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.5195 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.5195 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.5195 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.5414 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.5414 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.5414 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.5626 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.5626 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.5626 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.5867 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.5867 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.5867 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.6087 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.6087 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.6087 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.6298 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.6298 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.6298 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.6518 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.6518 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.6518 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.6730 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.6730 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.6730 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.6935 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.6935 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.6935 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.7154 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.7154 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.7154 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.7408 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.7408 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.7408 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.7657 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.7657 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.7657 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.7941 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.7941 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.7941 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.8144 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.8144 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.8144 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.8335 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.8335 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.8335 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.8542 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.8542 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.8542 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.8769 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.8769 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.8769 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.8988 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.8988 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.8988 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.9209 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.9209 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.9209 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.9412 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.9412 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.9412 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.9609 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.9609 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.9609 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.9809 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
28.9809 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
28.9809 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.0053 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.0053 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
29.0053 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.0274 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.0274 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
29.0274 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.0492 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.0492 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
29.0492 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.0712 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.0712 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
29.0712 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.0931 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.0931 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
29.0931 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.1164 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.1164 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
29.1164 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.1403 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.1403 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
29.1403 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.1652 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.1652 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
29.1652 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.1878 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.1878 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
29.1878 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.2154 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.2154 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
29.2154 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.2433 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.2433 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
29.2433 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.2631 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.2631 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
29.2631 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.2829 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.2829 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
29.2829 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.3028 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.3028 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
29.3028 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.3242 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.3242 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
29.3242 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.3460 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.3460 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
29.3460 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.3674 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.3674 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
29.3674 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.3887 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.3887 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
29.3887 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.4214 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.4214 	EXP 	unnamed TextStim: text = 'Time remaining: 347s'
29.4214 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.4481 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.4481 	EXP 	unnamed TextStim: height = 0.03
29.4481 	EXP 	unnamed TextStim: height = 0.03
29.4481 	EXP 	unnamed TextStim: height = 0.03
29.4481 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.4481 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.4693 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.4693 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.4693 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.4913 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.4913 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.4913 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.5160 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.5160 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.5160 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.5383 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.5383 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.5383 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.5636 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.5636 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.5636 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.5920 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.5920 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.5920 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.6193 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.6193 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.6193 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.6411 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.6411 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.6411 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.6632 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.6632 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.6632 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.6832 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.6832 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.6832 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.7034 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.7034 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.7034 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.7255 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.7255 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.7255 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.7480 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.7480 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.7480 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.7708 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.7708 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.7708 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.7929 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.7929 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.7929 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.8169 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.8169 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.8169 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.8384 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.8384 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.8384 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.8595 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.8595 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.8595 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.8811 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.8811 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.8811 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.9031 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.9031 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.9031 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.9403 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (5/30)'
29.9403 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.9403 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.9708 	EXP 	unnamed TextStim: height = 0.05
29.9708 	EXP 	unnamed TextStim: height = 0.05
29.9708 	EXP 	unnamed TextStim: height = 0.05
29.9708 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
29.9708 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.9708 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.9930 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
29.9930 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
29.9930 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.0157 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.0157 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
30.0157 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.0372 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.0372 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
30.0372 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.0580 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.0580 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
30.0580 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.0814 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.0814 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
30.0814 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.1040 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.1040 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
30.1040 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.1267 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.1267 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
30.1267 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.1488 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.1488 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
30.1488 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.1744 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.1744 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
30.1744 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.2019 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.2019 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
30.2019 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.2278 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.2278 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
30.2278 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.2516 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.2516 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
30.2516 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.2789 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.2789 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
30.2789 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.3048 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.3048 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
30.3048 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.3309 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.3309 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
30.3309 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.3604 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.3604 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
30.3604 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.3828 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.3828 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
30.3828 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.4049 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.4049 	EXP 	unnamed TextStim: text = 'Time remaining: 346s'
30.4049 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.4298 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.4298 	EXP 	unnamed TextStim: height = 0.03
30.4298 	EXP 	unnamed TextStim: height = 0.03
30.4298 	EXP 	unnamed TextStim: height = 0.03
30.4298 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.4298 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.4510 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.4510 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.4510 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.4751 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.4751 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.4751 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.4963 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.4963 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.4963 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.5188 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.5188 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.5188 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.5473 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.5473 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.5473 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.5709 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.5709 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.5709 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.5926 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.5926 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.5926 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.6152 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.6152 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.6152 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.6390 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.6390 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.6390 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.6623 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.6623 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.6623 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.6820 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.6820 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.6820 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.7012 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.7012 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.7012 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.7227 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.7227 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.7227 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.7476 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.7476 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.7476 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.7707 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.7707 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.7707 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.7912 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.7912 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.7912 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.8118 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.8118 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.8118 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.8334 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.8334 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.8334 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.8550 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.8550 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.8550 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.8775 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.8775 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.8775 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.8986 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.8986 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.8986 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.9220 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.9220 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.9220 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.9477 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.9477 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.9477 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.9679 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.9679 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.9679 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.9895 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
30.9895 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
30.9895 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.0102 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
31.0102 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
31.0102 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.0312 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
31.0312 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
31.0312 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.0587 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
31.0587 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
31.0587 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.0791 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
31.0791 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
31.0791 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.1000 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
31.1000 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
31.1000 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.1225 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
31.1225 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
31.1225 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.1428 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
31.1428 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
31.1428 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.1622 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
31.1622 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
31.1622 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.1813 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
31.1813 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
31.1813 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.2040 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
31.2040 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
31.2040 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.2264 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
31.2264 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
31.2264 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.2509 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
31.2509 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
31.2509 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.2726 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
31.2726 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
31.2726 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.2956 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
31.2956 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
31.2956 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.3165 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
31.3165 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
31.3165 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.3387 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
31.3387 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
31.3387 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.3590 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
31.3590 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
31.3590 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.3801 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
31.3801 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
31.3801 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.4009 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
31.4009 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
31.4009 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.4215 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (5/30)'
31.4215 	EXP 	unnamed TextStim: text = 'Time remaining: 345s'
31.4215 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.4483 	EXP 	unnamed TextStim: height = 0.05
31.4483 	EXP 	unnamed TextStim: height = 0.05
31.4483 	EXP 	unnamed TextStim: height = 0.05
31.4483 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.4483 	EXP 	unnamed TextStim: height = 0.03
31.4483 	EXP 	unnamed TextStim: height = 0.03
31.4483 	EXP 	unnamed TextStim: height = 0.03
31.4483 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.4483 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.4695 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.4695 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.4695 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.4922 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.4922 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.4922 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.5127 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.5127 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.5127 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.5322 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.5322 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.5322 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.5533 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.5533 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.5533 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.5756 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.5756 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.5756 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.5980 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.5980 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.5980 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.6203 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.6203 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.6203 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.6439 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.6439 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.6439 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.6660 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.6660 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.6660 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.6884 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.6884 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.6884 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.7089 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.7089 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.7089 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.7293 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.7293 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.7293 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.7488 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.7488 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.7488 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.7696 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.7696 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.7696 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.7895 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.7895 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.7895 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.8108 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.8108 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.8108 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.8367 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.8367 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.8367 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.8575 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.8575 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.8575 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.8779 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.8779 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.8779 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.8983 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.8983 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.8983 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.9196 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.9196 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.9196 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.9475 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.9475 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.9475 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.9703 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.9703 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.9703 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.9953 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
31.9953 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
31.9953 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.0230 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.0230 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
32.0230 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.0463 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.0463 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
32.0463 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.0728 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.0728 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
32.0728 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.0930 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.0930 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
32.0930 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.1142 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.1142 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
32.1142 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.1350 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.1350 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
32.1350 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.1556 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.1556 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
32.1556 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.1768 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.1768 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
32.1768 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.1971 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.1971 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
32.1971 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.2175 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.2175 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
32.2175 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.2389 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.2389 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
32.2389 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.2601 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.2601 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
32.2601 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.2828 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.2828 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
32.2828 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.3099 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.3099 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
32.3099 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.3387 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.3387 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
32.3387 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.3654 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.3654 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
32.3654 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.3881 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.3881 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
32.3881 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.4110 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.4110 	EXP 	unnamed TextStim: text = 'Time remaining: 344s'
32.4110 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.4395 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.4395 	EXP 	unnamed TextStim: height = 0.03
32.4395 	EXP 	unnamed TextStim: height = 0.03
32.4395 	EXP 	unnamed TextStim: height = 0.03
32.4395 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.4395 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.4634 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.4634 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.4634 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.4867 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.4867 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.4867 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.5076 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.5076 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.5076 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.5282 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.5282 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.5282 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.5481 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.5481 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.5481 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.5709 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.5709 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.5709 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.5946 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.5946 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.5946 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.6172 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.6172 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.6172 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.6397 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.6397 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.6397 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.6638 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.6638 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.6638 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.6853 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.6853 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.6853 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.7100 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.7100 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.7100 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.7370 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.7370 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.7370 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.7628 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.7628 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.7628 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.7838 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.7838 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.7838 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.8056 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.8056 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.8056 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.8276 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.8276 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.8276 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.8510 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.8510 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.8510 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.8731 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.8731 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.8731 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.8940 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.8940 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.8940 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.9174 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (6/30)'
32.9174 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.9174 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.9453 	EXP 	unnamed TextStim: height = 0.05
32.9453 	EXP 	unnamed TextStim: height = 0.05
32.9453 	EXP 	unnamed TextStim: height = 0.05
32.9453 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
32.9453 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.9453 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.9684 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
32.9684 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.9684 	EXP 	unnamed TextStim: text = 'Round: 1/3'
32.9896 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
32.9896 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
32.9896 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.0099 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.0099 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
33.0099 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.0344 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.0344 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
33.0344 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.0562 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.0562 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
33.0562 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.0796 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.0796 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
33.0796 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.1021 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.1021 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
33.1021 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.1218 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.1218 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
33.1218 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.1425 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.1425 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
33.1425 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.1637 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.1637 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
33.1637 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.1836 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.1836 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
33.1836 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.2050 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.2050 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
33.2050 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.2264 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.2264 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
33.2264 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.2496 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.2496 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
33.2496 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.2718 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.2718 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
33.2718 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.2938 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.2938 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
33.2938 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.3147 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.3147 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
33.3147 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.3356 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.3356 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
33.3356 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.3570 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.3570 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
33.3570 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.3794 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.3794 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
33.3794 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.4008 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.4008 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
33.4008 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.4248 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.4248 	EXP 	unnamed TextStim: text = 'Time remaining: 343s'
33.4248 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.4528 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.4528 	EXP 	unnamed TextStim: height = 0.03
33.4528 	EXP 	unnamed TextStim: height = 0.03
33.4528 	EXP 	unnamed TextStim: height = 0.03
33.4528 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.4528 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.4780 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.4780 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.4780 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.5048 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.5048 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.5048 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.5249 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.5249 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.5249 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.5455 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.5455 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.5455 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.5681 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.5681 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.5681 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.5911 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.5911 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.5911 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.6141 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.6141 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.6141 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.6380 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.6380 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.6380 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.6613 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.6613 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.6613 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.6813 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.6813 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.6813 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.7031 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.7031 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.7031 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.7227 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.7227 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.7227 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.7452 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.7452 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.7452 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.7680 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.7680 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.7680 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.7897 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.7897 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.7897 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.8120 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.8120 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.8120 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.8321 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.8321 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.8321 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.8538 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.8538 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.8538 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.8757 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.8757 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.8757 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.8945 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.8945 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.8945 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.9147 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.9147 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.9147 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.9371 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.9371 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.9371 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.9611 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.9611 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.9611 	EXP 	unnamed TextStim: text = 'Round: 1/3'
33.9834 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
33.9834 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
33.9834 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.0071 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
34.0071 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
34.0071 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.0276 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
34.0276 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
34.0276 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.0476 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
34.0476 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
34.0476 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.0691 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
34.0691 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
34.0691 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.0910 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
34.0910 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
34.0910 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.1120 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
34.1120 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
34.1120 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.1319 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
34.1319 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
34.1319 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.1532 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
34.1532 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
34.1532 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.1734 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
34.1734 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
34.1734 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.1962 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
34.1962 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
34.1962 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.2207 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
34.2207 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
34.2207 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.2416 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
34.2416 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
34.2416 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.2634 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
34.2634 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
34.2634 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.2834 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
34.2834 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
34.2834 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.3039 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
34.3039 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
34.3039 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.3231 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
34.3231 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
34.3231 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.3450 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
34.3450 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
34.3450 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.3678 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
34.3678 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
34.3678 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.3895 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
34.3895 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
34.3895 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.4120 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (6/30)'
34.4120 	EXP 	unnamed TextStim: text = 'Time remaining: 342s'
34.4120 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.4387 	EXP 	unnamed TextStim: height = 0.05
34.4387 	EXP 	unnamed TextStim: height = 0.05
34.4387 	EXP 	unnamed TextStim: height = 0.05
34.4387 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.4387 	EXP 	unnamed TextStim: height = 0.03
34.4387 	EXP 	unnamed TextStim: height = 0.03
34.4387 	EXP 	unnamed TextStim: height = 0.03
34.4387 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.4387 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.4594 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.4594 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.4594 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.4809 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.4809 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.4809 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.5019 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.5019 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.5019 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.5214 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.5214 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.5214 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.5423 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.5423 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.5423 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.5645 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.5645 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.5645 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.5865 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.5865 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.5865 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.6071 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.6071 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.6071 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.6273 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.6273 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.6273 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.6480 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.6480 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.6480 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.6696 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.6696 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.6696 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.6909 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.6909 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.6909 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.7113 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.7113 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.7113 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.7316 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.7316 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.7316 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.7530 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.7530 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.7530 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.7753 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.7753 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.7753 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.8015 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.8015 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.8015 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.8234 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.8234 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.8234 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.8435 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.8435 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.8435 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.8660 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.8660 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.8660 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.8864 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.8864 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.8864 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.9060 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.9060 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.9060 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.9262 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.9262 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.9262 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.9467 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.9467 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.9467 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.9684 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.9684 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.9684 	EXP 	unnamed TextStim: text = 'Round: 1/3'
34.9919 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
34.9919 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
34.9919 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.0141 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.0141 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
35.0141 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.0397 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.0397 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
35.0397 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.0621 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.0621 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
35.0621 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.0839 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.0839 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
35.0839 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.1087 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.1087 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
35.1087 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.1309 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.1309 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
35.1309 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.1533 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.1533 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
35.1533 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.1742 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.1742 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
35.1742 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.1930 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.1930 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
35.1930 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.2114 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.2114 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
35.2114 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.2334 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.2334 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
35.2334 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.2570 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.2570 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
35.2570 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.2810 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.2810 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
35.2810 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.3093 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.3093 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
35.3093 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.3364 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.3364 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
35.3364 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.3568 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.3568 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
35.3568 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.3810 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.3810 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
35.3810 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.4088 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.4088 	EXP 	unnamed TextStim: text = 'Time remaining: 341s'
35.4088 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.4334 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.4334 	EXP 	unnamed TextStim: height = 0.03
35.4334 	EXP 	unnamed TextStim: height = 0.03
35.4334 	EXP 	unnamed TextStim: height = 0.03
35.4334 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.4334 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.4540 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.4540 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.4540 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.4755 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.4755 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.4755 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.4990 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.4990 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.4990 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.5212 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.5212 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.5212 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.5428 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.5428 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.5428 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.5638 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.5638 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.5638 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.5867 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.5867 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.5867 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.6083 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.6083 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.6083 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.6304 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.6304 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.6304 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.6538 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.6538 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.6538 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.6766 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.6766 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.6766 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.7021 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.7021 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.7021 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.7258 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.7258 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.7258 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.7487 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.7487 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.7487 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.7731 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.7731 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.7731 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.7956 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.7956 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.7956 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.8187 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.8187 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.8187 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.8409 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.8409 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.8409 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.8630 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.8630 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.8630 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.8869 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.8869 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.8869 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.9093 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (7/30)'
35.9093 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.9093 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.9341 	EXP 	unnamed TextStim: height = 0.05
35.9341 	EXP 	unnamed TextStim: height = 0.05
35.9341 	EXP 	unnamed TextStim: height = 0.05
35.9341 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
35.9341 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.9341 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.9565 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
35.9565 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.9565 	EXP 	unnamed TextStim: text = 'Round: 1/3'
35.9788 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
35.9788 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
35.9788 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.0035 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.0035 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
36.0035 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.0269 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.0269 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
36.0269 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.0516 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.0516 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
36.0516 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.0746 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.0746 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
36.0746 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.0956 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.0956 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
36.0956 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.1211 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.1211 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
36.1211 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.1420 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.1420 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
36.1420 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.1632 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.1632 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
36.1632 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.1847 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.1847 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
36.1847 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.2058 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.2058 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
36.2058 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.2280 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.2280 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
36.2280 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.2522 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.2522 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
36.2522 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.2729 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.2729 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
36.2729 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.2933 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.2933 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
36.2933 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.3148 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.3148 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
36.3148 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.3356 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.3356 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
36.3356 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.3615 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.3615 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
36.3615 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.3849 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.3849 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
36.3849 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.4060 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.4060 	EXP 	unnamed TextStim: text = 'Time remaining: 340s'
36.4060 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.4316 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.4316 	EXP 	unnamed TextStim: height = 0.03
36.4316 	EXP 	unnamed TextStim: height = 0.03
36.4316 	EXP 	unnamed TextStim: height = 0.03
36.4316 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.4316 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.4584 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.4584 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.4584 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.4831 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.4831 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.4831 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.5038 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.5038 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.5038 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.5240 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.5240 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.5240 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.5442 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.5442 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.5442 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.5643 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.5643 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.5643 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.5858 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.5858 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.5858 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.6123 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.6123 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.6123 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.6345 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.6345 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.6345 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.6572 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.6572 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.6572 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.6791 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.6791 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.6791 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.6996 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.6996 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.6996 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.7218 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.7218 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.7218 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.7424 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.7424 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.7424 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.7646 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.7646 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.7646 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.7859 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.7859 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.7859 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.8073 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.8073 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.8073 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.8283 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.8283 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.8283 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.8491 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.8491 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.8491 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.8694 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.8694 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.8694 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.8895 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.8895 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.8895 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.9110 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.9110 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.9110 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.9357 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.9357 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.9357 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.9574 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.9574 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.9574 	EXP 	unnamed TextStim: text = 'Round: 1/3'
36.9839 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
36.9839 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
36.9839 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.0039 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
37.0039 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
37.0039 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.0252 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
37.0252 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
37.0252 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.0476 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
37.0476 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
37.0476 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.0692 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
37.0692 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
37.0692 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.0911 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
37.0911 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
37.0911 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.1114 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
37.1114 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
37.1114 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.1340 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
37.1340 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
37.1340 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.1557 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
37.1557 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
37.1557 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.1751 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
37.1751 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
37.1751 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.1947 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
37.1947 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
37.1947 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.2181 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
37.2181 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
37.2181 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.2395 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
37.2395 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
37.2395 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.2594 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
37.2594 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
37.2594 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.2809 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
37.2809 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
37.2809 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.3007 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
37.3007 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
37.3007 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.3226 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
37.3226 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
37.3226 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.3443 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
37.3443 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
37.3443 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.3651 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
37.3651 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
37.3651 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.3857 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
37.3857 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
37.3857 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.4060 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (7/30)'
37.4060 	EXP 	unnamed TextStim: text = 'Time remaining: 339s'
37.4060 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.4316 	EXP 	unnamed TextStim: height = 0.05
37.4316 	EXP 	unnamed TextStim: height = 0.05
37.4316 	EXP 	unnamed TextStim: height = 0.05
37.4316 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.4316 	EXP 	unnamed TextStim: height = 0.03
37.4316 	EXP 	unnamed TextStim: height = 0.03
37.4316 	EXP 	unnamed TextStim: height = 0.03
37.4316 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.4316 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.4533 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.4533 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.4533 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.4732 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.4732 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.4732 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.4943 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.4943 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.4943 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.5153 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.5153 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.5153 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.5359 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.5359 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.5359 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.5573 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.5573 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.5573 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.5778 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.5778 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.5778 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.5989 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.5989 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.5989 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.6193 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.6193 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.6193 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.6409 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.6409 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.6409 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.6630 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.6630 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.6630 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.6846 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.6846 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.6846 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.7063 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.7063 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.7063 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.7265 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.7265 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.7265 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.7468 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.7468 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.7468 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.7686 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.7686 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.7686 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.7910 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.7910 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.7910 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.8190 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.8190 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.8190 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.8423 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.8423 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.8423 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.8611 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.8611 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.8611 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.8809 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.8809 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.8809 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.9004 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.9004 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.9004 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.9219 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (8/30)'
37.9219 	EXP 	unnamed TextStim: text = 'Time remaining: 338s'
37.9219 	EXP 	unnamed TextStim: text = 'Round: 1/3'
37.9392 	DATA 	Keypress: escape
38.5796 	EXP 	window1: mouseVisible = True
