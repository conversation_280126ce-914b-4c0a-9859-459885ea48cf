9.4625 	WARNING 	User requested fullscreen with size [1536  864], but screen is actually [1920, 1080]. Using actual size
10.0964 	EXP 	Created window1 = Window(allowGUI=True, allowStencil=True, autoLog=True, backendConf=UNKNOWN, backgroundFit=<method-wrapper '__getattribute__' of attributeSetter object at 0x000001B2681F61F0>, backgroundImage=<method-wrapper '__getattribute__' of attributeSetter object at 0x000001B2681F6160>, bitsMode=UNKNOWN, blendMode='avg', bpc=(8, 8, 8), color=array([0, 0, 0]), colorSpace='rgb', depthBits=8, fullscr=<method-wrapper '__getattribute__' of attributeSetter object at 0x000001B2681E6430>, gamma=None, gammaErrorPolicy='raise', lms=UNKNOWN, monitor=<psychopy.monitors.calibTools.Monitor object at 0x000001B26870B190>, multiSample=False, name='window1', numSamples=2, pos=(0, 0), screen=0, size=array([1920, 1080]), stencilBits=8, stereo=False, title='PsychoPy', units='height', useFBO=True, useRetina=False, viewOri=0.0, viewPos=None, viewScale=None, waitBlanking=True, winType='pyglet')
10.0966 	EXP 	window1: mouseVisible = True
10.0966 	EXP 	window1: backgroundImage = ''
10.0966 	EXP 	window1: backgroundFit = 'none'
10.0980 	EXP 	window1: Attempting to measure frame rate of screen (0) ...
10.0980 	EXP 	window1: recordFrameIntervals = False
10.2620 	EXP 	window1: recordFrameIntervals = True
10.4448 	EXP 	Screen (0) actual frame rate measured at 60.33Hz
10.4449 	EXP 	window1: recordFrameIntervals = False
10.4450 	EXP 	window1: mouseVisible = False
12.9296 	EXP 	01_resting_state: status = STARTED
13.0871 	EXP 	window1: mouseVisible = True
13.1416 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
13.1417 	EXP 	window1: mouseVisible = True
13.1593 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.1596 	EXP 	window1: mouseVisible = True
13.1641 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.1644 	EXP 	window1: mouseVisible = True
13.1803 	EXP 	Created stroop_instr = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='stroop_instr', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.1805 	EXP 	window1: mouseVisible = True
13.1901 	EXP 	Created instrText_4 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-2.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_4', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.1902 	EXP 	window1: mouseVisible = True
13.1924 	EXP 	Created stim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.15, italic=False, languageStyle='LTR', name='stim', opacity=1.0, ori=0, pos=array([0., 0.]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
13.1927 	EXP 	window1: mouseVisible = True
13.1929 	EXP 	window1: mouseVisible = True
13.1934 	EXP 	window1: mouseVisible = True
13.1937 	EXP 	window1: mouseVisible = True
13.1984 	EXP 	window1: mouseVisible = True
13.1986 	EXP 	window1: mouseVisible = True
13.1990 	EXP 	window1: mouseVisible = True
13.1994 	EXP 	window1: mouseVisible = True
13.2205 	EXP 	window1: mouseVisible = True
13.2258 	EXP 	Created instrText_2 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-4.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_2', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.2261 	EXP 	window1: mouseVisible = True
13.2262 	EXP 	window1: mouseVisible = True
13.2267 	EXP 	window1: mouseVisible = True
13.2270 	EXP 	window1: mouseVisible = True
13.2309 	EXP 	window1: mouseVisible = True
13.2310 	EXP 	window1: mouseVisible = True
13.2315 	EXP 	window1: mouseVisible = True
13.2319 	EXP 	window1: mouseVisible = True
13.2357 	EXP 	window1: mouseVisible = True
13.2359 	EXP 	window1: mouseVisible = True
13.2363 	EXP 	window1: mouseVisible = True
13.2367 	EXP 	window1: mouseVisible = True
13.2412 	EXP 	window1: mouseVisible = True
13.2414 	EXP 	window1: mouseVisible = True
13.2418 	EXP 	window1: mouseVisible = True
13.2422 	EXP 	window1: mouseVisible = True
13.2462 	EXP 	window1: mouseVisible = True
13.2509 	EXP 	Created instrText_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-3.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_3', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.2511 	EXP 	window1: mouseVisible = True
13.2635 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.2638 	EXP 	window1: mouseVisible = True
13.2687 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
13.2689 	EXP 	window1: mouseVisible = True
13.2805 	EXP 	Created EndMessage = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='EndMessage', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='Thank you!', units='height', win=Window(...), wrapWidth=1)
0.0050 	EXP 	trial_counter: autoLog = True
0.0050 	EXP 	fbtxt: autoLog = True
0.0050 	EXP 	trial_counter_2: autoLog = True
0.0266 	EXP 	cross: autoDraw = True
15.0340 	EXP 	cross: autoDraw = False
15.0340 	EXP 	cross: autoDraw = False
15.0340 	EXP 	text_3: autoDraw = True
22.0282 	EXP 	window1: mouseVisible = True
22.0307 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
22.0308 	EXP 	window1: mouseVisible = True
22.0358 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([ 0.  , -0.35]), rgb=UNKNOWN, text='Inhale', units='height', win=Window(...), wrapWidth=1)
22.0359 	EXP 	window1: mouseVisible = True
22.0368 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([0. , 0.4]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
22.0453 	DATA 	Keypress: f1
22.0489 	EXP 	text_3: autoDraw = False
22.0489 	EXP 	unnamed TextStim: height = 0.03
22.0489 	EXP 	unnamed TextStim: height = 0.03
22.0489 	EXP 	unnamed TextStim: height = 0.03
22.0489 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.0604 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.0775 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.0941 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.1109 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.1273 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.1439 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.1604 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.1769 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.1940 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.2105 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.2272 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.2454 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.2609 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.2774 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.2938 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.3109 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.3273 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.3440 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.3602 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.3772 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.3937 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.4102 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.4274 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.4437 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.4602 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.4768 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.4938 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.5105 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.5270 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.5436 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.5600 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.5770 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.5939 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.6109 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.6268 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.6436 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.6596 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.6767 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.6934 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.7094 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.7263 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.7431 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.7597 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.7769 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.7950 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.8101 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.8266 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.8438 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.8599 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.8770 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.8936 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.9100 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.9267 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.9437 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.9600 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.9766 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
22.9933 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
23.0100 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
23.0264 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
23.0429 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
23.0596 	EXP 	unnamed TextStim: height = 0.03
23.0596 	EXP 	unnamed TextStim: height = 0.03
23.0596 	EXP 	unnamed TextStim: height = 0.03
23.0596 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.0764 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.0931 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.1102 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.1265 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.1430 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.1596 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.1762 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.1931 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.2097 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.2264 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.2430 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.2594 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.2764 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.2935 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.3096 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.3262 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.3430 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.3594 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.3764 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.3933 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.4098 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.4265 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.4439 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.4595 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.4759 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.4928 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.5095 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.5261 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.5430 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.5595 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.5763 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.5933 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.6103 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.6271 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.6436 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.6594 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.6768 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.6933 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.7098 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.7267 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.7434 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.7599 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.7769 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.7933 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.8095 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.8262 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.8431 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.8598 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.8763 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.8936 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.9100 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.9258 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.9427 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.9599 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.9768 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
23.9932 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
24.0101 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
24.0263 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
24.0434 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
24.0594 	EXP 	unnamed TextStim: height = 0.03
24.0594 	EXP 	unnamed TextStim: height = 0.03
24.0594 	EXP 	unnamed TextStim: height = 0.03
24.0594 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.0757 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.0927 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.1094 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.1261 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.1429 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.1594 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.1760 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.1930 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.2095 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.2263 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.2433 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.2599 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.2768 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.2940 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.3099 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.3263 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.3429 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.3593 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.3764 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.3922 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.4092 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.4275 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.4430 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.4598 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.4762 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.4926 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.5096 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.5262 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.5428 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.5594 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.5758 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.5924 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.6093 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.6263 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.6426 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.6592 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.6758 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.6925 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.7090 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.7260 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.7424 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.7596 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.7762 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.7925 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.8086 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.8259 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.8425 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.8590 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.8758 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.8925 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.9091 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.9261 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.9428 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.9595 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.9772 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
24.9925 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
25.0093 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
25.0258 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
25.0422 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
25.0584 	EXP 	unnamed TextStim: height = 0.03
25.0584 	EXP 	unnamed TextStim: height = 0.03
25.0584 	EXP 	unnamed TextStim: height = 0.03
25.0584 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.0757 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.0921 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.1089 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.1259 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.1422 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.1589 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.1755 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.1924 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.2089 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.2258 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.2427 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.2586 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.2750 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.2917 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.3077 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.3248 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.3412 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.3578 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.3749 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.3912 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.4080 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.4248 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.4416 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.4582 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.4749 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.4915 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.5076 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.5257 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.5413 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.5578 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.5748 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.5916 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.6078 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.6248 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.6418 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.6578 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.6744 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.6915 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.7078 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.7241 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.7408 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.7576 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.7744 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.7907 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.8072 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.8244 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.8408 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.8578 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.8746 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.8911 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.9074 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.9245 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.9408 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.9576 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.9745 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
25.9912 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
26.0072 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
26.0242 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
26.0404 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
26.0567 	EXP 	unnamed TextStim: height = 0.03
26.0567 	EXP 	unnamed TextStim: height = 0.03
26.0567 	EXP 	unnamed TextStim: height = 0.03
26.0567 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.0754 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.0909 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.1074 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.1243 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.1406 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.1572 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.1739 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.1906 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.2067 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.2238 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.2399 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.2571 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.2735 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.2904 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.3073 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.3239 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.3404 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.3571 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.3738 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.3903 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.4069 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.4238 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.4404 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.4571 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.4739 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.4905 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.5069 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.5237 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.5407 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.5574 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.5741 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.5908 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.6068 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.6237 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.6403 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.6569 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.6737 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.6902 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.7067 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.7233 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.7402 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.7566 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.7735 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.7902 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.8065 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.8233 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.8399 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.8567 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.8733 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.8900 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.9062 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.9229 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.9397 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.9566 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.9735 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
26.9907 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
27.0066 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
27.0231 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
27.0398 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
27.0564 	EXP 	unnamed TextStim: height = 0.03
27.0564 	EXP 	unnamed TextStim: height = 0.03
27.0564 	EXP 	unnamed TextStim: height = 0.03
27.0564 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.0729 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.0897 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.1067 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.1234 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.1395 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.1565 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.1731 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.1895 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.2063 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.2233 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.2397 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.2566 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.2731 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.2895 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.3064 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.3234 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.3396 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.3564 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.3728 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.3895 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.4062 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.4232 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.4394 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.4563 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.4728 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.4896 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.5064 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.5233 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.5395 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.5557 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.5728 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.5892 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.6056 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.6223 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.6390 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.6557 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.6727 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.6892 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.7060 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.7251 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.7397 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.7557 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.7727 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.7888 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.8058 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.8223 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.8389 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.8559 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.8731 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.8895 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.9059 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.9225 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.9390 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.9559 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.9729 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
27.9891 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
28.0061 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
28.0224 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
28.0390 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
28.0554 	EXP 	unnamed TextStim: height = 0.03
28.0554 	EXP 	unnamed TextStim: height = 0.03
28.0554 	EXP 	unnamed TextStim: height = 0.03
28.0554 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.0724 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.0890 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.1058 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.1225 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.1393 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.1557 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.1731 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.1893 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.2060 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.2225 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.2385 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.2552 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.2718 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.2888 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.3052 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.3223 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.3390 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.3554 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.3724 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.3888 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.4056 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.4222 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.4386 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.4554 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.4720 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.4885 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.5059 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.5220 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.5389 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.5554 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.5719 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.5888 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.6054 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.6221 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.6387 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.6554 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.6726 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.6886 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.7059 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.7220 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.7384 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.7550 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.7716 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.7882 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.8050 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.8218 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.8385 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.8552 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.8722 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.8886 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.9049 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.9217 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.9386 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.9553 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.9715 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
28.9882 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
29.0048 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
29.0219 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
29.0385 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
29.0551 	EXP 	unnamed TextStim: height = 0.03
29.0551 	EXP 	unnamed TextStim: height = 0.03
29.0551 	EXP 	unnamed TextStim: height = 0.03
29.0551 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.0715 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.0880 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.1049 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.1217 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.1381 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.1548 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.1716 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.1881 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.2050 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.2218 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.2382 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.2546 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.2714 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.2882 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.3051 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.3219 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.3383 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.3551 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.3711 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.3881 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.4049 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.4212 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.4379 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.4550 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.4714 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.4878 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.5045 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.5213 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.5380 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.5546 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.5714 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.5879 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.6044 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.6211 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.6374 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.6545 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.6711 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.6881 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.7048 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.7212 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.7376 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.7540 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.7709 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.7874 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.8036 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.8207 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.8376 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.8542 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.8708 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.8879 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.9046 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.9212 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.9378 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.9547 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.9708 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
29.9869 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
30.0041 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
30.0207 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
30.0373 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
30.0546 	EXP 	unnamed TextStim: height = 0.03
30.0546 	EXP 	unnamed TextStim: height = 0.03
30.0546 	EXP 	unnamed TextStim: height = 0.03
30.0546 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.0709 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.0876 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.1041 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.1206 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.1372 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.1539 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.1708 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.1873 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.2038 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.2209 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.2373 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.2540 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.2704 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.2870 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.3038 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.3205 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.3371 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.3543 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.3703 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.3869 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.4042 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.4203 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.4372 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.4539 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.4710 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.4870 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.5040 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.5203 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.5368 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.5540 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.5705 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.5865 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.6034 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.6203 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.6371 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.6541 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.6705 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.6869 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.7031 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.7204 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.7368 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.7535 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.7699 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.7867 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.8034 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.8200 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.8368 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.8539 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.8698 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.8867 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.9033 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.9201 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.9367 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.9533 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.9698 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
30.9865 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
31.0033 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
31.0212 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
31.0371 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
31.0543 	EXP 	unnamed TextStim: height = 0.03
31.0543 	EXP 	unnamed TextStim: height = 0.03
31.0543 	EXP 	unnamed TextStim: height = 0.03
31.0543 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.0706 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.0865 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.1031 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.1198 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.1362 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.1531 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.1700 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.1864 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.2034 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.2199 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.2363 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.2531 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.2700 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.2866 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.3030 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.3196 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.3365 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.3534 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.3699 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.3866 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.4030 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.4195 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.4362 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.4531 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.4696 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.4863 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.5029 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.5198 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.5365 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.5531 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.5697 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.5864 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.6031 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.6195 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.6363 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.6528 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.6693 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.6861 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.7029 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.7195 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.7361 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.7525 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.7693 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.7860 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.8028 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.8190 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.8360 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.8530 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.8694 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.8860 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.9029 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.9195 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.9359 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.9527 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.9695 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
31.9859 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
32.0026 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
32.0190 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
32.0358 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
32.0521 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
32.0691 	EXP 	unnamed TextStim: height = 0.03
32.0691 	EXP 	unnamed TextStim: height = 0.03
32.0691 	EXP 	unnamed TextStim: height = 0.03
32.0691 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.0857 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.1024 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.1202 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.1357 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.1525 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.1691 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.1859 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.2025 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.2188 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.2358 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.2522 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.2688 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.2854 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.3024 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.3192 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.3357 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.3525 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.3691 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.3854 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.4019 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.4186 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.4354 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.4522 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.4689 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.4854 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.5016 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.5185 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.5353 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.5517 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.5685 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.5853 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.6019 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.6188 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.6354 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.6518 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.6707 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.6855 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.7018 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.7187 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.7361 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.7521 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.7686 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.7849 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.8011 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.8185 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.8350 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.8517 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.8684 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.8851 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.9018 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.9188 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.9357 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.9519 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.9686 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
32.9853 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
33.0017 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
33.0183 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
33.0352 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
33.0518 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
33.0684 	EXP 	unnamed TextStim: height = 0.03
33.0684 	EXP 	unnamed TextStim: height = 0.03
33.0684 	EXP 	unnamed TextStim: height = 0.03
33.0684 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.0850 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.1015 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.1183 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.1348 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.1516 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.1681 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.1853 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.2018 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.2197 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.2351 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.2515 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.2683 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.2851 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.3013 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.3180 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.3350 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.3514 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.3681 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.3850 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.4013 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.4181 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.4351 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.4514 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.4679 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.4848 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.5013 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.5181 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.5345 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.5514 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.5678 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.5843 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.6012 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.6179 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.6342 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.6510 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.6678 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.6847 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.7008 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.7175 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.7342 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.7513 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.7693 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.7846 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.8009 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.8175 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.8344 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.8511 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.8676 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.8844 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.9012 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.9175 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.9344 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.9512 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.9677 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
33.9842 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
34.0011 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
34.0178 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
34.0343 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
34.0511 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
34.0674 	EXP 	unnamed TextStim: height = 0.03
34.0674 	EXP 	unnamed TextStim: height = 0.03
34.0674 	EXP 	unnamed TextStim: height = 0.03
34.0674 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.0842 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.1009 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.1175 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.1341 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.1505 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.1672 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.1843 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.2007 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.2173 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.2340 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.2506 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.2673 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.2847 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.3014 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.3195 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.3349 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.3515 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.3672 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.3838 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.4003 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.4170 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.4339 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.4506 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.4674 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.4842 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.5005 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.5172 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.5339 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.5502 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.5668 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.5840 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.6001 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.6171 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.6336 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.6498 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.6668 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.6837 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.7001 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.7163 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.7333 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.7504 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.7668 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.7839 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.8002 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.8164 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.8334 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.8521 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.8667 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.8838 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.9003 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.9168 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.9335 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.9501 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.9667 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.9835 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
34.9998 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
35.0165 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
35.0331 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
35.0502 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
35.0666 	EXP 	unnamed TextStim: height = 0.03
35.0666 	EXP 	unnamed TextStim: height = 0.03
35.0666 	EXP 	unnamed TextStim: height = 0.03
35.0666 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.0833 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.0998 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.1166 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.1335 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.1500 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.1665 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.1833 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.2002 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.2164 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.2334 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.2499 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.2666 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.2835 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.3001 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.3161 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.3329 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.3495 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.3666 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.3833 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.3996 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.4166 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.4336 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.4497 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.4666 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.4830 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.4995 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.5163 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.5330 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.5496 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.5660 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.5837 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.5995 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.6164 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.6332 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.6499 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.6663 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.6828 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.6991 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.7163 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.7325 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.7492 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.7660 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.7827 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.7995 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.8156 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.8329 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.8496 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.8661 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.8829 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.8994 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.9160 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.9325 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.9496 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.9660 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.9830 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
35.9992 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
36.0156 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
36.0330 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
36.0494 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
36.0657 	EXP 	unnamed TextStim: height = 0.03
36.0657 	EXP 	unnamed TextStim: height = 0.03
36.0657 	EXP 	unnamed TextStim: height = 0.03
36.0657 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.0826 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.0996 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.1157 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.1325 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.1493 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.1662 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.1825 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.1992 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.2156 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.2327 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.2489 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.2654 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.2826 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.2990 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.3154 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.3325 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.3488 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.3655 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.3820 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.3988 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.4158 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.4324 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.4491 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.4659 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.4829 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.4995 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.5157 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.5324 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.5488 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.5654 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.5821 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.5987 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.6153 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.6321 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.6486 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.6657 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.6822 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.6984 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.7157 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.7320 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.7486 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.7653 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.7815 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.7981 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.8151 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.8320 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.8484 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.8647 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.8817 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.8985 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.9151 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.9317 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.9485 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.9651 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.9815 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
36.9983 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
37.0151 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
37.0314 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
37.0481 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
39.1190 	EXP 	text_3: autoDraw = True
46.9747 	EXP 	window1: mouseVisible = True
46.9793 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
46.9795 	EXP 	window1: mouseVisible = True
46.9834 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([ 0.  , -0.35]), rgb=UNKNOWN, text='Inhale', units='height', win=Window(...), wrapWidth=1)
46.9835 	EXP 	window1: mouseVisible = True
46.9844 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([0. , 0.4]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
46.9886 	DATA 	Keypress: f1
46.9924 	EXP 	text_3: autoDraw = False
46.9924 	EXP 	unnamed TextStim: height = 0.03
46.9924 	EXP 	unnamed TextStim: height = 0.03
46.9924 	EXP 	unnamed TextStim: height = 0.03
46.9924 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.0069 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.0234 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.0398 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.0564 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.0735 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.0903 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.1065 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.1240 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.1398 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.1567 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.1737 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.1897 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.2066 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.2231 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.2394 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.2559 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.2732 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.2898 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.3065 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.3231 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.3402 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.3567 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.3732 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.3903 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.4065 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.4233 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.4399 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.4564 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.4732 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.4894 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.5073 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.5230 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.5392 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.5561 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.5728 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.5897 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.6063 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.6230 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.6397 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.6563 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.6729 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.6899 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.7071 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.7228 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.7395 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.7562 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.7731 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.7892 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.8058 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.8233 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.8396 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.8564 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.8731 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.8895 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.9063 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.9235 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.9397 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.9562 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.9728 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.9892 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
48.0058 	EXP 	unnamed TextStim: height = 0.03
48.0058 	EXP 	unnamed TextStim: height = 0.03
48.0058 	EXP 	unnamed TextStim: height = 0.03
48.0058 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.0230 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.0397 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.0560 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.0728 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.0894 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.1057 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.1231 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.1396 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.1562 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.1730 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.1895 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.2063 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.2235 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.2392 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.2562 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.2723 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.2891 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.3056 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.3218 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.3388 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.3555 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.3725 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.3888 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.4056 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.4224 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.4393 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.4557 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.4720 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.4886 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.5054 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.5221 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.5390 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.5553 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.5721 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.5891 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.6054 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.6216 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.6387 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.6555 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.6717 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.6887 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.7053 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.7218 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.7389 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.7553 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.7718 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.7887 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.8052 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.8214 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.8386 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.8549 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.8718 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.8888 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.9054 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.9219 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.9386 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.9556 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.9721 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.9885 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
49.0050 	EXP 	unnamed TextStim: height = 0.03
49.0050 	EXP 	unnamed TextStim: height = 0.03
49.0050 	EXP 	unnamed TextStim: height = 0.03
49.0050 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.0220 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.0384 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.0554 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.0718 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.0882 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.1052 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.1216 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.1384 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.1552 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.1715 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.1882 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.2051 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.2212 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.2383 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.2547 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.2710 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.2878 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.3046 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.3213 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.3385 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.3549 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.3715 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.3883 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.4053 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.4216 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.4378 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.4554 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.4726 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.4879 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.5047 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.5216 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.5380 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.5548 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.5715 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.5878 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.6048 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.6218 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.6379 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.6550 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.6715 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.6882 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.7059 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.7210 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.7381 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.7545 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.7711 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.7876 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.8041 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.8211 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.8374 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.8543 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.8709 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.8879 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.9051 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.9208 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.9375 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.9546 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.9708 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.9877 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
50.0045 	EXP 	unnamed TextStim: height = 0.03
50.0045 	EXP 	unnamed TextStim: height = 0.03
50.0045 	EXP 	unnamed TextStim: height = 0.03
50.0045 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.0212 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.0379 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.0546 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.0709 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.0877 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.1041 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.1208 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.1378 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.1545 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.1708 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.1878 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.2044 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.2209 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.2380 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.2545 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.2708 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.2880 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.3042 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.3211 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.3376 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.3544 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.3709 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.3875 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.4042 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.4208 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.4380 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.4547 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.4711 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.4879 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.5045 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.5203 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.5379 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.5548 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.5718 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.5878 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.6048 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.6208 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.6380 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.6547 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.6709 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.6877 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.7047 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.7210 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.7378 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.7549 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.7739 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.7889 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.8043 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.8206 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.8377 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.8544 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.8711 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.8876 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.9044 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.9202 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.9367 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.9536 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.9700 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.9868 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
51.0037 	EXP 	unnamed TextStim: height = 0.03
51.0037 	EXP 	unnamed TextStim: height = 0.03
51.0037 	EXP 	unnamed TextStim: height = 0.03
51.0037 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.0200 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.0366 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.0538 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.0703 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.0868 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.1033 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.1200 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.1363 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.1531 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.1701 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.1867 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.2038 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.2200 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.2367 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.2541 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.2703 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.2865 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.3031 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.3200 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.3363 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.3532 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.3697 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.3865 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.4030 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.4197 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.4367 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.4535 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.4698 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.4863 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.5029 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.5195 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.5360 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.5528 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.5694 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.5865 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.6030 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.6197 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.6362 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.6530 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.6696 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.6865 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.7028 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.7193 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.7358 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.7528 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.7695 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.7864 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.8030 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.8193 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.8356 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.8534 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.8696 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.8865 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.9032 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.9194 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.9362 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.9527 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.9693 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.9862 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
52.0029 	EXP 	unnamed TextStim: height = 0.03
52.0029 	EXP 	unnamed TextStim: height = 0.03
52.0029 	EXP 	unnamed TextStim: height = 0.03
52.0029 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.0192 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.0361 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.0525 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.0692 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.0858 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.1026 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.1192 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.1363 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.1524 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.1689 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.1857 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.2025 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.2192 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.2356 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.2525 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.2689 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.2856 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.3023 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.3188 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.3357 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.3529 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.3693 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.3855 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.4029 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.4191 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.4358 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.4526 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.4690 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.4857 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.5027 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.5194 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.5359 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.5526 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.5692 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.5864 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.6027 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.6196 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.6363 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.6531 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.6695 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.6860 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.7029 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.7194 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.7366 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.7525 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.7689 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.7861 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.8035 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.8198 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.8366 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.8532 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.8700 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.8867 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.9033 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.9195 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.9361 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.9545 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.9697 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.9863 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
53.0027 	EXP 	unnamed TextStim: height = 0.03
53.0027 	EXP 	unnamed TextStim: height = 0.03
53.0027 	EXP 	unnamed TextStim: height = 0.03
53.0027 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.0193 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.0360 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.0528 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.0696 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.0861 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.1031 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.1191 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.1368 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.1529 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.1695 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.1863 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.2021 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.2191 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.2356 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.2521 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.2697 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.2864 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.3026 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.3193 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.3366 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.3524 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.3686 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.3855 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.4024 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.4190 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.4356 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.4524 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.4689 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.4879 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.5027 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.5185 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.5358 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.5525 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.5687 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.5870 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.6020 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.6187 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.6357 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.6521 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.6686 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.6854 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.7014 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.7182 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.7354 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.7519 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.7687 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.7854 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.8019 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.8190 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.8357 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.8521 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.8688 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.8852 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.9022 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.9185 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.9354 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.9521 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.9683 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.9857 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
54.0018 	EXP 	unnamed TextStim: height = 0.03
54.0018 	EXP 	unnamed TextStim: height = 0.03
54.0018 	EXP 	unnamed TextStim: height = 0.03
54.0018 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.0185 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.0359 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.0520 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.0684 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.0853 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.1017 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.1182 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.1349 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.1518 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.1683 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.1850 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.2012 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.2181 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.2349 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.2516 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.2683 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.2853 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.3005 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.3173 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.3342 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.3508 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.3673 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.3838 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.4007 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.4175 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.4342 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.4510 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.4672 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.4842 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.5006 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.5173 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.5342 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.5502 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.5677 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.5839 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.6005 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.6173 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.6341 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.6505 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.6670 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.6840 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.7000 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.7170 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.7336 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.7501 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.7672 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.7848 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.8008 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.8174 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.8335 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.8504 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.8675 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.8840 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.9001 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.9172 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.9337 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.9505 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.9667 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.9841 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
55.0003 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
55.0167 	EXP 	unnamed TextStim: height = 0.03
55.0167 	EXP 	unnamed TextStim: height = 0.03
55.0167 	EXP 	unnamed TextStim: height = 0.03
55.0167 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.0333 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.0495 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.0660 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.0835 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.0999 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.1169 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.1337 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.1504 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.1669 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.1840 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.2004 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.2169 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.2334 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.2497 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.2666 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.2834 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.2997 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.3165 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.3333 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.3499 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.3666 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.3833 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.3999 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.4166 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.4331 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.4503 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.4665 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.4830 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.4996 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.5166 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.5335 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.5497 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.5664 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.5835 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.5996 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.6162 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.6334 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.6497 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.6665 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.6830 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.6995 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.7163 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.7328 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.7494 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.7662 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.7826 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.7996 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.8158 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.8330 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.8496 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.8663 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.8827 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.8993 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.9158 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.9328 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.9495 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.9661 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.9832 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.9993 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
56.0161 	EXP 	unnamed TextStim: height = 0.03
56.0161 	EXP 	unnamed TextStim: height = 0.03
56.0161 	EXP 	unnamed TextStim: height = 0.03
56.0161 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.0326 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.0489 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.0657 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.0831 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.0991 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.1159 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.1325 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.1490 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.1660 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.1826 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.1994 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.2155 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.2322 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.2486 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.2651 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.2825 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.2991 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.3156 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.3327 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.3487 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.3657 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.3823 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.3991 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.4157 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.4325 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.4493 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.4658 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.4827 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.4989 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.5153 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.5325 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.5493 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.5656 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.5819 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.5989 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.6153 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.6326 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.6489 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.6654 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.6822 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.6982 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.7154 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.7321 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.7488 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.7654 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.7821 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.7985 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.8148 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.8320 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.8482 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.8655 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.8826 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.8989 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.9155 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.9317 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.9493 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.9661 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.9826 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.9994 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
57.0155 	EXP 	unnamed TextStim: height = 0.03
57.0155 	EXP 	unnamed TextStim: height = 0.03
57.0155 	EXP 	unnamed TextStim: height = 0.03
57.0155 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.0316 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.0483 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.0654 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.0831 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.0990 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.1155 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.1325 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.1490 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.1658 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.1826 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.1989 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.2158 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.2327 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.2488 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.2654 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.2824 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.2989 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.3167 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.3322 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.3487 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.3656 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.3824 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.3991 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.4159 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.4329 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.4488 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.4659 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.4825 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.4988 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.5159 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.5319 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.5488 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.5656 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.5824 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.5987 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.6157 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.6323 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.6478 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.6651 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.6812 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.6985 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.7150 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.7311 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.7478 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.7647 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.7812 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.7977 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.8145 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.8312 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.8478 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.8658 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.8813 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.8979 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.9148 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.9316 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.9475 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.9645 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.9818 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.9974 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
58.0144 	EXP 	unnamed TextStim: height = 0.03
58.0144 	EXP 	unnamed TextStim: height = 0.03
58.0144 	EXP 	unnamed TextStim: height = 0.03
58.0144 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.0311 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.0475 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.0644 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.0810 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.0978 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.1143 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.1310 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.1477 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.1643 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.1812 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.1978 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.2143 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.2310 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.2476 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.2645 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.2810 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.2974 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.3138 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.3308 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.3477 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.3647 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.3818 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.3973 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.4145 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.4310 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.4476 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.4636 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.4809 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.4975 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.5141 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.5308 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.5472 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.5639 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.5803 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.5974 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.6140 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.6305 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.6471 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.6645 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.6805 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.6976 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.7136 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.7303 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.7470 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.7642 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.7804 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.7975 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.8148 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.8306 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.8466 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.8637 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.8802 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.8971 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.9138 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.9307 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.9469 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.9635 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.9803 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.9965 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
59.0137 	EXP 	unnamed TextStim: height = 0.03
59.0137 	EXP 	unnamed TextStim: height = 0.03
59.0137 	EXP 	unnamed TextStim: height = 0.03
59.0137 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.0300 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.0466 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.0636 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.0801 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.0971 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.1137 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.1305 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.1468 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.1637 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.1803 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.1965 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.2132 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.2298 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.2465 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.2638 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.2803 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.2966 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.3138 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.3297 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.3464 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.3631 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.3798 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.3963 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.4130 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.4299 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.4470 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.4631 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.4813 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.4965 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.5130 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.5297 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.5464 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.5631 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.5795 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.5961 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.6130 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.6295 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.6463 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.6629 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.6796 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.6969 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.7129 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.7296 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.7467 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.7627 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.7797 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.7960 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.8125 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.8295 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.8459 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.8626 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.8794 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.8959 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.9128 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.9294 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.9462 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.9632 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.9795 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.9962 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
60.0128 	EXP 	unnamed TextStim: height = 0.03
60.0128 	EXP 	unnamed TextStim: height = 0.03
60.0128 	EXP 	unnamed TextStim: height = 0.03
60.0128 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.0309 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.0460 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.0627 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.0795 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.0962 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.1127 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.1294 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.1461 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.1627 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.1796 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.1958 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.2130 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.2292 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.2459 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.2626 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.2791 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.2958 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.3127 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.3296 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.3458 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.3626 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.3790 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.3955 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.4124 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.4288 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.4456 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.4628 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.4792 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.4956 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.5126 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.5292 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.5454 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.5621 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.5805 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.5956 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.6122 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.6289 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.6458 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.6623 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.6788 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.6958 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.7122 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.7287 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.7455 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.7619 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.7786 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.7955 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.8123 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.8287 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.8453 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.8618 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.8786 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.8955 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.9119 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.9289 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.9455 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.9623 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.9790 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.9957 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
61.0115 	EXP 	unnamed TextStim: height = 0.03
61.0115 	EXP 	unnamed TextStim: height = 0.03
61.0115 	EXP 	unnamed TextStim: height = 0.03
61.0115 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.0285 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.0452 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.0616 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.0787 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.0952 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.1123 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.1301 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.1454 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.1617 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.1784 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.1953 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.2117 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.2285 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.2454 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.2618 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.2787 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.2953 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.3119 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.3286 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.3447 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.3613 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.3781 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.3950 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.4114 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.4280 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.4449 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.4614 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.4786 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.4953 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.5117 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.5284 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.5452 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.5620 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.5780 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.5951 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.6112 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.6280 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.6454 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.6615 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.6780 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.6951 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.7112 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.7276 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.7447 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.7613 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.7783 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.7955 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.8111 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.8281 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.8447 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.8612 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.8777 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.8945 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.9115 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.9282 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.9451 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.9614 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.9781 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
61.9960 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
64.0824 	EXP 	stroop_instr: autoDraw = True
64.0824 	EXP 	instrText_4: autoDraw = True
79.2331 	DATA 	Keypress: escape
79.4144 	EXP 	01_resting_state: status = STOPPED
79.4299 	EXP 	stroop_instr: autoDraw = False
79.4299 	EXP 	instrText_4: autoDraw = False
79.4301 	EXP 	01_resting_state: status = STOPPED
79.5087 	EXP 	window1: mouseVisible = True
