7.7848 	WARNING 	User requested fullscreen with size [1536  864], but screen is actually [1920, 1080]. Using actual size
8.4293 	EXP 	Created window1 = Window(allowGUI=True, allowStencil=True, autoLog=True, backendConf=UNKNOWN, backgroundFit=<method-wrapper '__getattribute__' of attributeSetter object at 0x000001366EE83340>, backgroundImage=<method-wrapper '__getattribute__' of attributeSetter object at 0x000001366EE83310>, bitsMode=UNKNOWN, blendMode='avg', bpc=(8, 8, 8), color=array([0, 0, 0]), colorSpace='rgb', depthBits=8, fullscr=<method-wrapper '__getattribute__' of attributeSetter object at 0x000001366EE83160>, gamma=None, gammaErrorPolicy='raise', lms=UNKNOWN, monitor=<psychopy.monitors.calibTools.Monitor object at 0x000001366F3C8310>, multiSample=False, name='window1', numSamples=2, pos=(0, 0), screen=0, size=array([1920, 1080]), stencilBits=8, stereo=False, title='PsychoPy', units='height', useFBO=True, useRetina=False, viewOri=0.0, viewPos=None, viewScale=None, waitBlanking=True, winType='pyglet')
8.4295 	EXP 	window1: mouseVisible = True
8.4296 	EXP 	window1: backgroundImage = ''
8.4296 	EXP 	window1: backgroundFit = 'none'
8.4316 	EXP 	window1: Attempting to measure frame rate of screen (0) ...
8.4316 	EXP 	window1: recordFrameIntervals = False
8.5952 	EXP 	window1: recordFrameIntervals = True
8.7784 	EXP 	Screen (0) actual frame rate measured at 60.07Hz
8.7785 	EXP 	window1: recordFrameIntervals = False
8.7788 	EXP 	window1: mouseVisible = False
11.2732 	EXP 	01_resting_state: status = STARTED
11.4365 	EXP 	window1: mouseVisible = True
11.4956 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
11.4958 	EXP 	window1: mouseVisible = True
11.5155 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.5156 	EXP 	window1: mouseVisible = True
11.5220 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.5223 	EXP 	window1: mouseVisible = True
11.5391 	EXP 	Created stroop_instr = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='stroop_instr', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.5392 	EXP 	window1: mouseVisible = True
11.5498 	EXP 	Created instrText_4 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-2.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_4', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.5499 	EXP 	window1: mouseVisible = True
11.5516 	EXP 	Created stim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.15, italic=False, languageStyle='LTR', name='stim', opacity=1.0, ori=0, pos=array([0., 0.]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
11.5519 	EXP 	window1: mouseVisible = True
11.5520 	EXP 	window1: mouseVisible = True
11.5524 	EXP 	window1: mouseVisible = True
11.5528 	EXP 	window1: mouseVisible = True
11.5579 	EXP 	window1: mouseVisible = True
11.5580 	EXP 	window1: mouseVisible = True
11.5585 	EXP 	window1: mouseVisible = True
11.5589 	EXP 	window1: mouseVisible = True
11.5942 	EXP 	window1: mouseVisible = True
11.5997 	EXP 	Created instrText_2 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-4.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_2', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.6000 	EXP 	window1: mouseVisible = True
11.6001 	EXP 	window1: mouseVisible = True
11.6008 	EXP 	window1: mouseVisible = True
11.6012 	EXP 	window1: mouseVisible = True
11.6059 	EXP 	window1: mouseVisible = True
11.6061 	EXP 	window1: mouseVisible = True
11.6066 	EXP 	window1: mouseVisible = True
11.6071 	EXP 	window1: mouseVisible = True
11.6117 	EXP 	window1: mouseVisible = True
11.6119 	EXP 	window1: mouseVisible = True
11.6124 	EXP 	window1: mouseVisible = True
11.6129 	EXP 	window1: mouseVisible = True
11.6174 	EXP 	window1: mouseVisible = True
11.6176 	EXP 	window1: mouseVisible = True
11.6181 	EXP 	window1: mouseVisible = True
11.6185 	EXP 	window1: mouseVisible = True
11.6230 	EXP 	window1: mouseVisible = True
11.6301 	EXP 	Created instrText_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-3.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_3', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.6303 	EXP 	window1: mouseVisible = True
11.6440 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.6443 	EXP 	window1: mouseVisible = True
11.6492 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
11.6495 	EXP 	window1: mouseVisible = True
11.6603 	EXP 	Created EndMessage = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='EndMessage', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='Thank you!', units='height', win=Window(...), wrapWidth=1)
0.0041 	EXP 	trial_counter: autoLog = True
0.0041 	EXP 	fbtxt: autoLog = True
0.0041 	EXP 	trial_counter_2: autoLog = True
0.0151 	EXP 	cross: autoDraw = True
15.0196 	EXP 	cross: autoDraw = False
15.0196 	EXP 	cross: autoDraw = False
15.0196 	EXP 	text_3: autoDraw = True
20.4677 	EXP 	window1: mouseVisible = True
20.4708 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
20.4709 	EXP 	window1: mouseVisible = True
20.4759 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([ 0.  , -0.35]), rgb=UNKNOWN, text='Inhale', units='height', win=Window(...), wrapWidth=1)
20.4760 	EXP 	window1: mouseVisible = True
20.4771 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([0. , 0.4]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
20.4881 	DATA 	Keypress: f1
20.4920 	EXP 	text_3: autoDraw = False
20.4920 	EXP 	unnamed TextStim: height = 0.03
20.4920 	EXP 	unnamed TextStim: height = 0.03
20.4920 	EXP 	unnamed TextStim: height = 0.03
20.4920 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.5002 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.5155 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.5326 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.5496 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.5658 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.5829 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.5989 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.6160 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.6327 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.6495 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.6658 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.6823 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.6991 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.7156 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.7316 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.7487 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.7648 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.7828 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.7981 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.8151 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.8318 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.8481 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.8648 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.8815 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.8983 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.9149 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.9318 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.9482 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.9650 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.9813 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
20.9979 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.0145 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.0320 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.0482 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.0646 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.0813 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.0981 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.1144 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.1312 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.1482 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.1647 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.1814 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.1981 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.2146 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.2316 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.2479 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.2648 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.2812 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.2977 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.3145 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.3311 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.3479 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.3646 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.3812 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.3981 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.4145 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.4312 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.4481 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.4644 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.4809 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
21.4975 	EXP 	unnamed TextStim: height = 0.03
21.4975 	EXP 	unnamed TextStim: height = 0.03
21.4975 	EXP 	unnamed TextStim: height = 0.03
21.4975 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.5142 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.5310 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.5478 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.5640 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.5808 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.5973 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.6142 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.6307 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.6479 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.6644 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.6818 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.6987 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.7147 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.7311 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.7474 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.7647 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.7807 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.7974 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.8147 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.8304 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.8470 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.8633 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.8800 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.8969 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.9138 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.9304 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.9476 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.9642 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.9805 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
21.9975 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.0138 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.0306 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.0472 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.0639 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.0804 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.0977 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.1139 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.1304 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.1482 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.1642 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.1805 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.1971 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.2138 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.2305 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.2468 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.2633 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.2797 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.2968 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.3133 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.3301 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.3469 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.3632 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.3801 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.3965 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.4131 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.4306 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.4468 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.4631 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.4797 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
22.4964 	EXP 	unnamed TextStim: height = 0.03
22.4964 	EXP 	unnamed TextStim: height = 0.03
22.4964 	EXP 	unnamed TextStim: height = 0.03
22.4964 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.5131 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.5299 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.5469 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.5634 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.5799 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.5967 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.6133 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.6304 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.6466 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.6634 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.6820 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.6971 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.7131 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.7301 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.7470 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.7631 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.7797 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.7965 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.8130 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.8305 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.8466 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.8628 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.8797 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.8966 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.9140 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.9311 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.9463 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.9631 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.9799 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
22.9967 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.0133 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.0299 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.0464 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.0631 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.0798 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.0965 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.1127 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.1298 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.1476 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.1633 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.1801 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.1956 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.2125 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.2292 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.2469 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.2628 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.2798 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.2971 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.3130 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.3304 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.3471 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.3632 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.3796 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.3961 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.4134 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.4304 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.4468 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.4635 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.4803 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
23.4966 	EXP 	unnamed TextStim: height = 0.03
23.4966 	EXP 	unnamed TextStim: height = 0.03
23.4966 	EXP 	unnamed TextStim: height = 0.03
23.4966 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.5134 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.5303 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.5468 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.5635 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.5804 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.5968 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.6133 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.6306 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.6464 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.6631 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.6810 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.6969 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.7133 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.7302 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.7465 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.7634 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.7800 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.7968 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.8133 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.8295 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.8468 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.8633 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.8801 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.8965 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.9129 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.9299 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.9463 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.9633 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.9799 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
23.9963 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.0130 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.0305 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.0466 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.0634 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.0803 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.0963 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.1127 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.1298 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.1466 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.1635 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.1803 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.1966 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.2132 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.2300 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.2463 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.2628 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.2798 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.2962 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.3125 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.3299 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.3464 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.3627 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.3799 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.3960 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.4129 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.4295 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.4460 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.4628 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.4796 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
24.4959 	EXP 	unnamed TextStim: height = 0.03
24.4959 	EXP 	unnamed TextStim: height = 0.03
24.4959 	EXP 	unnamed TextStim: height = 0.03
24.4959 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.5121 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.5293 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.5463 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.5630 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.5796 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.5962 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.6123 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.6292 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.6457 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.6623 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.6797 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.6954 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.7123 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.7294 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.7457 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.7625 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.7796 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.7956 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.8127 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.8300 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.8466 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.8625 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.8795 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.8960 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.9122 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.9291 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.9457 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.9625 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.9791 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
24.9957 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.0122 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.0287 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.0456 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.0622 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.0792 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.0955 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.1128 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.1288 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.1452 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.1619 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.1787 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.1954 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.2119 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.2286 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.2451 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.2620 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.2788 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.2954 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.3121 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.3293 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.3460 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.3619 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.3781 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.3946 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.4111 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.4278 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.4444 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.4607 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.4778 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
25.4940 	EXP 	unnamed TextStim: height = 0.03
25.4940 	EXP 	unnamed TextStim: height = 0.03
25.4940 	EXP 	unnamed TextStim: height = 0.03
25.4940 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.5110 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.5279 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.5443 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.5613 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.5783 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.5956 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.6109 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.6276 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.6442 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.6607 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.6774 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.6940 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.7106 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.7276 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.7446 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.7610 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.7776 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.7943 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.8107 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.8277 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.8443 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.8608 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.8780 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.8940 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.9107 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.9275 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.9444 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.9607 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.9774 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
25.9949 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.0103 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.0272 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.0440 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.0604 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.0773 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.0943 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.1109 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.1273 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.1438 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.1604 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.1768 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.1936 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.2102 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.2271 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.2437 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.2604 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.2771 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.2938 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.3110 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.3282 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.3446 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.3613 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.3785 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.3941 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.4111 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.4284 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.4445 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.4613 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.4772 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
26.4934 	EXP 	unnamed TextStim: height = 0.03
26.4934 	EXP 	unnamed TextStim: height = 0.03
26.4934 	EXP 	unnamed TextStim: height = 0.03
26.4934 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.5107 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.5274 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.5440 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.5611 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.5796 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.5944 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.6116 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.6280 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.6442 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.6607 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.6780 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.6940 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.7107 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.7276 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.7440 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.7607 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.7775 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.7938 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.8110 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.8276 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.8451 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.8613 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.8781 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.8941 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.9104 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.9273 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.9440 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.9610 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.9773 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
26.9938 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.0110 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.0273 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.0429 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.0597 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.0767 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.0934 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.1100 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.1287 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.1430 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.1595 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.1762 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.1929 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.2097 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.2265 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.2427 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.2597 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.2768 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.2929 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.3096 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.3260 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.3429 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.3596 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.3763 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.3930 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.4099 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.4263 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.4430 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.4597 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.4764 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.4931 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
27.5092 	EXP 	unnamed TextStim: height = 0.03
27.5092 	EXP 	unnamed TextStim: height = 0.03
27.5092 	EXP 	unnamed TextStim: height = 0.03
27.5092 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.5259 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.5422 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.5594 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.5759 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.5927 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.6092 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.6260 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.6423 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.6593 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.6763 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.6942 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.7097 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.7258 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.7419 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.7592 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.7760 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.7924 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.8091 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.8262 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.8428 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.8594 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.8760 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.8925 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.9092 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.9254 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.9423 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.9592 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.9754 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
27.9921 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.0092 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.0258 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.0423 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.0589 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.0757 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.0922 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.1093 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.1256 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.1423 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.1591 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.1758 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.1923 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.2085 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.2264 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.2421 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.2588 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.2752 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.2920 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.3087 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.3257 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.3423 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.3609 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.3764 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.3932 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.4088 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.4256 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.4421 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.4590 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.4756 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.4925 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
28.5092 	EXP 	unnamed TextStim: height = 0.03
28.5092 	EXP 	unnamed TextStim: height = 0.03
28.5092 	EXP 	unnamed TextStim: height = 0.03
28.5092 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.5259 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.5423 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.5596 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.5755 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.5923 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.6088 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.6254 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.6418 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.6584 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.6747 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.6915 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.7082 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.7251 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.7419 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.7590 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.7770 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.7909 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.8082 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.8250 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.8423 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.8588 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.8747 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.8915 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.9079 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.9248 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.9414 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.9583 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.9748 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
28.9925 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.0093 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.0256 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.0420 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.0598 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.0758 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.0923 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.1087 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.1253 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.1416 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.1588 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.1754 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.1918 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.2089 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.2254 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.2421 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.2592 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.2756 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.2923 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.3088 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.3268 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.3419 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.3585 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.3749 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.3918 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.4088 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.4253 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.4419 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.4584 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.4754 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.4917 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
29.5093 	EXP 	unnamed TextStim: height = 0.03
29.5093 	EXP 	unnamed TextStim: height = 0.03
29.5093 	EXP 	unnamed TextStim: height = 0.03
29.5093 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.5251 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.5423 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.5586 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.5755 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.5917 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.6080 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.6251 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.6416 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.6580 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.6750 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.6919 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.7086 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.7250 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.7414 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.7587 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.7751 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.7917 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.8089 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.8247 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.8412 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.8572 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.8757 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.8906 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.9077 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.9240 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.9407 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.9573 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.9741 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
29.9904 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.0072 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.0243 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.0403 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.0575 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.0743 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.0904 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.1074 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.1242 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.1405 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.1571 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.1739 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.1905 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.2072 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.2239 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.2404 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.2570 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.2740 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.2903 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.3071 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.3240 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.3404 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.3572 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.3742 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.3908 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.4071 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.4253 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.4405 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.4573 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.4732 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.4902 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
30.5068 	EXP 	unnamed TextStim: height = 0.03
30.5068 	EXP 	unnamed TextStim: height = 0.03
30.5068 	EXP 	unnamed TextStim: height = 0.03
30.5068 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.5235 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.5403 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.5567 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.5738 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.5904 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.6069 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.6235 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.6401 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.6566 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.6735 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.6898 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.7070 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.7234 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.7401 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.7574 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.7735 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.7898 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.8062 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.8232 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.8403 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.8565 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.8730 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.8898 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.9066 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.9235 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.9407 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.9571 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.9736 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
30.9916 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.0064 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.0230 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.0397 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.0564 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.0733 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.0900 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.1067 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.1234 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.1402 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.1568 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.1733 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.1896 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.2065 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.2230 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.2397 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.2562 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.2735 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.2906 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.3060 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.3228 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.3395 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.3558 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.3724 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.3895 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.4061 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.4235 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.4399 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.4560 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.4729 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.4896 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
31.5059 	EXP 	unnamed TextStim: height = 0.03
31.5059 	EXP 	unnamed TextStim: height = 0.03
31.5059 	EXP 	unnamed TextStim: height = 0.03
31.5059 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.5227 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.5393 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.5567 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.5730 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.5897 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.6060 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.6226 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.6395 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.6562 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.6728 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.6891 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.7055 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.7225 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.7395 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.7560 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.7727 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.7894 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.8054 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.8224 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.8392 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.8558 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.8725 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.8895 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.9059 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.9227 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.9394 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.9560 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.9726 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
31.9894 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.0057 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.0224 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.0390 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.0557 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.0723 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.0895 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.1059 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.1225 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.1395 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.1558 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.1723 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.1896 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.2055 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.2221 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.2390 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.2554 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.2720 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.2887 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.3054 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.3220 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.3393 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.3557 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.3721 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.3889 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.4055 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.4226 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.4388 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.4560 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.4718 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.4886 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
32.5053 	EXP 	unnamed TextStim: height = 0.03
32.5053 	EXP 	unnamed TextStim: height = 0.03
32.5053 	EXP 	unnamed TextStim: height = 0.03
32.5053 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.5221 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.5390 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.5551 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.5718 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.5889 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.6053 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.6218 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.6386 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.6563 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.6721 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.6887 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.7054 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.7215 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.7384 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.7550 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.7719 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.7882 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.8047 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.8216 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.8382 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.8550 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.8718 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.8883 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.9047 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.9218 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.9387 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.9550 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.9717 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
32.9885 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.0048 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.0216 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.0384 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.0548 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.0717 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.0883 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.1056 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.1223 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.1388 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.1556 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.1723 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.1898 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.2054 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.2223 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.2390 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.2552 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.2719 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.2888 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.3051 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.3217 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.3386 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.3546 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.3712 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.3879 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.4045 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.4212 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.4379 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.4550 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.4715 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.4880 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
33.5041 	EXP 	unnamed TextStim: height = 0.03
33.5041 	EXP 	unnamed TextStim: height = 0.03
33.5041 	EXP 	unnamed TextStim: height = 0.03
33.5041 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.5210 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.5378 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.5543 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.5712 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.5878 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.6046 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.6214 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.6381 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.6544 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.6709 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.6876 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.7046 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.7209 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.7381 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.7544 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.7714 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.7882 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.8042 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.8215 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.8391 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.8549 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.8715 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.8886 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.9048 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.9216 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.9384 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.9551 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.9720 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
33.9883 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.0052 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.0215 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.0381 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.0554 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.0715 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.0881 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.1046 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.1215 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.1380 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.1548 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.1714 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.1877 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.2046 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.2218 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.2383 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.2547 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.2713 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.2897 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.3042 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.3211 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.3378 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.3541 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.3711 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.3881 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.4047 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.4211 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.4380 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.4544 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.4712 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.4875 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
34.5043 	EXP 	unnamed TextStim: height = 0.03
34.5043 	EXP 	unnamed TextStim: height = 0.03
34.5043 	EXP 	unnamed TextStim: height = 0.03
34.5043 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.5212 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.5375 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.5545 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.5710 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.5876 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.6042 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.6210 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.6379 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.6545 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.6712 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.6879 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.7049 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.7212 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.7374 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.7541 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.7706 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.7872 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.8039 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.8207 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.8391 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.8540 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.8707 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.8865 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.9030 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.9205 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.9366 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.9532 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.9704 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
34.9864 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.0032 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.0200 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.0365 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.0532 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.0705 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.0867 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.1032 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.1198 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.1366 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.1533 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.1703 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.1867 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.2034 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.2204 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.2365 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.2527 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.2703 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.2861 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.3027 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.3197 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.3363 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.3529 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.3697 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.3879 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.4031 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.4201 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.4361 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.4531 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.4692 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
35.4858 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
37.5552 	EXP 	text_3: autoDraw = True
45.9787 	EXP 	window1: mouseVisible = True
45.9836 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
45.9838 	EXP 	window1: mouseVisible = True
45.9882 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([ 0.  , -0.35]), rgb=UNKNOWN, text='Inhale', units='height', win=Window(...), wrapWidth=1)
45.9884 	EXP 	window1: mouseVisible = True
45.9892 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([0. , 0.4]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
45.9932 	DATA 	Keypress: f1
45.9971 	EXP 	text_3: autoDraw = False
45.9971 	EXP 	unnamed TextStim: height = 0.03
45.9971 	EXP 	unnamed TextStim: height = 0.03
45.9971 	EXP 	unnamed TextStim: height = 0.03
45.9971 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.0108 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.0274 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.0440 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.0603 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.0773 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.0941 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.1105 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.1271 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.1440 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.1606 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.1776 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.1940 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.2104 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.2269 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.2450 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.2614 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.2775 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.2941 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.3108 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.3272 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.3440 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.3604 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.3772 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.3937 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.4104 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.4273 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.4440 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.4607 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.4771 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.4940 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.5106 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.5276 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.5439 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.5611 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.5768 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.5936 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.6105 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.6268 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.6436 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.6606 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.6773 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.6936 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.7101 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.7267 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.7435 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.7603 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.7769 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.7935 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.8110 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.8269 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.8434 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.8602 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.8766 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.8938 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.9105 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.9266 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.9433 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.9603 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.9766 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
46.9932 	EXP 	unnamed TextStim: text = 'Time remaining: 14s'
47.0097 	EXP 	unnamed TextStim: height = 0.03
47.0097 	EXP 	unnamed TextStim: height = 0.03
47.0097 	EXP 	unnamed TextStim: height = 0.03
47.0097 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.0267 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.0437 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.0603 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.0770 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.0934 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.1096 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.1293 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.1431 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.1596 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.1763 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.1931 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.2104 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.2262 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.2434 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.2599 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.2767 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.2940 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.3103 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.3266 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.3429 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.3609 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.3763 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.3934 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.4103 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.4264 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.4432 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.4600 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.4763 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.4928 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.5101 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.5263 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.5431 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.5598 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.5762 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.5930 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.6095 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.6264 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.6427 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.6595 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.6763 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.6931 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.7093 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.7258 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.7427 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.7595 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.7761 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.7930 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.8096 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.8266 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.8426 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.8595 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.8759 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.8929 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.9092 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.9261 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.9428 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.9593 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.9759 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
47.9928 	EXP 	unnamed TextStim: text = 'Time remaining: 13s'
48.0092 	EXP 	unnamed TextStim: height = 0.03
48.0092 	EXP 	unnamed TextStim: height = 0.03
48.0092 	EXP 	unnamed TextStim: height = 0.03
48.0092 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.0275 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.0426 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.0594 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.0760 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.0926 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.1093 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.1258 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.1427 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.1597 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.1760 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.1924 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.2086 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.2257 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.2422 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.2586 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.2756 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.2924 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.3100 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.3259 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.3429 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.3594 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.3759 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.3923 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.4086 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.4261 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.4423 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.4590 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.4757 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.4923 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.5093 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.5258 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.5422 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.5592 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.5771 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.5925 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.6088 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.6263 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.6421 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.6590 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.6757 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.6918 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.7084 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.7253 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.7422 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.7588 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.7754 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.7921 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.8089 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.8252 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.8420 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.8582 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.8744 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.8916 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.9087 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.9249 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.9418 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.9591 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.9751 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
48.9924 	EXP 	unnamed TextStim: text = 'Time remaining: 12s'
49.0091 	EXP 	unnamed TextStim: height = 0.03
49.0091 	EXP 	unnamed TextStim: height = 0.03
49.0091 	EXP 	unnamed TextStim: height = 0.03
49.0091 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.0261 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.0417 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.0594 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.0747 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.0915 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.1084 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.1247 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.1414 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.1583 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.1750 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.1922 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.2086 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.2251 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.2415 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.2588 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.2750 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.2926 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.3084 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.3251 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.3424 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.3585 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.3747 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.3915 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.4080 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.4246 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.4413 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.4589 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.4748 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.4914 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.5084 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.5247 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.5413 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.5580 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.5748 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.5914 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.6081 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.6247 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.6411 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.6586 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.6749 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.6911 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.7079 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.7243 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.7413 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.7581 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.7747 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.7909 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.8073 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.8245 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.8412 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.8576 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.8745 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.8909 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.9075 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.9243 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.9407 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.9576 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.9741 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
49.9906 	EXP 	unnamed TextStim: text = 'Time remaining: 11s'
50.0074 	EXP 	unnamed TextStim: height = 0.03
50.0074 	EXP 	unnamed TextStim: height = 0.03
50.0074 	EXP 	unnamed TextStim: height = 0.03
50.0074 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.0243 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.0405 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.0580 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.0743 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.0907 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.1075 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.1241 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.1408 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.1575 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.1740 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.1908 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.2091 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.2244 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.2407 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.2576 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.2742 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.2911 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.3077 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.3242 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.3415 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.3574 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.3738 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.3905 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.4073 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.4239 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.4407 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.4582 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.4739 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.4903 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.5072 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.5241 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.5407 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.5573 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.5739 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.5907 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.6075 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.6240 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.6404 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.6570 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.6738 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.6910 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.7069 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.7236 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.7406 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.7571 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.7753 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.7903 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.8066 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.8236 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.8405 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.8568 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.8732 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.8900 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.9067 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.9235 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.9399 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.9569 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.9735 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
50.9901 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
51.0067 	EXP 	unnamed TextStim: height = 0.03
51.0067 	EXP 	unnamed TextStim: height = 0.03
51.0067 	EXP 	unnamed TextStim: height = 0.03
51.0067 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.0234 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.0402 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.0568 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.0737 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.0900 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.1064 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.1234 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.1402 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.1568 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.1737 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.1900 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.2066 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.2232 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.2398 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.2566 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.2733 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.2912 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.3067 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.3232 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.3401 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.3565 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.3731 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.3901 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.4062 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.4230 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.4399 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.4565 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.4737 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.4904 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.5066 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.5230 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.5397 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.5564 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.5731 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.5897 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.6067 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.6228 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.6396 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.6570 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.6730 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.6891 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.7056 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.7228 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.7399 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.7561 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.7728 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.7898 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.8059 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.8227 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.8397 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.8579 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.8729 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.8891 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.9056 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.9222 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.9395 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.9563 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.9727 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
51.9893 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
52.0061 	EXP 	unnamed TextStim: height = 0.03
52.0061 	EXP 	unnamed TextStim: height = 0.03
52.0061 	EXP 	unnamed TextStim: height = 0.03
52.0061 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.0226 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.0392 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.0561 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.0728 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.0893 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.1056 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.1235 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.1400 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.1559 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.1726 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.1900 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.2060 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.2230 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.2400 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.2556 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.2726 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.2899 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.3059 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.3226 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.3395 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.3555 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.3723 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.3889 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.4055 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.4222 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.4391 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.4555 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.4721 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.4891 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.5055 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.5220 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.5390 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.5559 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.5718 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.5887 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.6057 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.6223 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.6388 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.6553 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.6714 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.6889 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.7054 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.7220 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.7389 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.7554 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.7716 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.7884 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.8051 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.8219 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.8386 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.8551 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.8721 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.8882 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.9048 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.9214 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.9389 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.9553 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.9721 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
52.9887 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.0051 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
53.0218 	EXP 	unnamed TextStim: height = 0.03
53.0218 	EXP 	unnamed TextStim: height = 0.03
53.0218 	EXP 	unnamed TextStim: height = 0.03
53.0218 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.0384 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.0549 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.0715 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.0884 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.1048 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.1218 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.1384 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.1553 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.1716 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.1890 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.2049 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.2215 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.2389 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.2553 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.2717 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.2881 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.3047 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.3210 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.3391 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.3548 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.3714 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.3885 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.4051 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.4215 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.4383 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.4550 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.4715 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.4882 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.5047 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.5215 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.5381 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.5547 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.5713 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.5878 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.6053 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.6220 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.6390 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.6549 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.6718 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.6885 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.7051 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.7217 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.7386 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.7552 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.7717 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.7883 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.8049 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.8217 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.8385 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.8550 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.8718 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.8876 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.9036 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.9209 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.9376 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.9541 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.9711 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
53.9876 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.0045 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
54.0209 	EXP 	unnamed TextStim: height = 0.03
54.0209 	EXP 	unnamed TextStim: height = 0.03
54.0209 	EXP 	unnamed TextStim: height = 0.03
54.0209 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.0390 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.0545 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.0709 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.0878 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.1041 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.1210 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.1376 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.1546 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.1708 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.1875 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.2045 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.2208 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.2373 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.2538 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.2705 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.2874 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.3043 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.3209 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.3378 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.3547 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.3709 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.3879 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.4039 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.4206 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.4370 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.4535 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.4702 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.4870 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.5036 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.5204 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.5374 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.5537 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.5704 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.5871 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.6039 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.6204 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.6376 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.6540 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.6706 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.6871 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.7035 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.7203 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.7367 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.7537 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.7707 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.7865 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.8034 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.8207 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.8367 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.8536 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.8706 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.8872 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.9034 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.9198 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.9367 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.9534 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.9699 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
54.9868 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.0036 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
55.0202 	EXP 	unnamed TextStim: height = 0.03
55.0202 	EXP 	unnamed TextStim: height = 0.03
55.0202 	EXP 	unnamed TextStim: height = 0.03
55.0202 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.0368 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.0535 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.0699 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.0870 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.1036 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.1199 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.1387 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.1534 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.1700 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.1876 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.2033 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.2199 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.2367 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.2531 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.2700 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.2866 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.3030 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.3199 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.3367 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.3531 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.3698 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.3869 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.4033 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.4203 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.4364 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.4531 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.4699 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.4860 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.5029 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.5198 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.5362 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.5529 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.5698 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.5862 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.6027 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.6197 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.6364 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.6532 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.6700 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.6880 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.7029 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.7192 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.7368 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.7528 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.7698 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.7862 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.8023 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.8199 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.8366 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.8529 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.8695 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.8863 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.9030 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.9196 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.9359 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.9525 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.9692 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
55.9862 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.0026 	EXP 	unnamed TextStim: text = 'Time remaining: 5s'
56.0194 	EXP 	unnamed TextStim: height = 0.03
56.0194 	EXP 	unnamed TextStim: height = 0.03
56.0194 	EXP 	unnamed TextStim: height = 0.03
56.0194 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.0359 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.0528 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.0695 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.0861 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.1024 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.1191 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.1360 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.1527 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.1697 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.1862 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.2027 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.2193 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.2380 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.2525 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.2692 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.2858 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.3024 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.3195 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.3359 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.3524 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.3690 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.3858 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.4025 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.4192 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.4358 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.4521 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.4689 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.4855 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.5023 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.5195 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.5358 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.5526 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.5691 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.5858 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.6025 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.6191 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.6361 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.6523 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.6693 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.6851 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.7019 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.7183 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.7351 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.7515 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.7685 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.7849 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.8018 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.8187 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.8360 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.8521 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.8691 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.8852 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.9017 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.9190 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.9353 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.9519 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.9686 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
56.9850 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.0019 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
57.0197 	EXP 	unnamed TextStim: height = 0.03
57.0197 	EXP 	unnamed TextStim: height = 0.03
57.0197 	EXP 	unnamed TextStim: height = 0.03
57.0197 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.0359 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.0523 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.0687 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.0854 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.1020 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.1188 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.1349 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.1519 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.1684 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.1849 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.2016 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.2183 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.2349 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.2520 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.2686 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.2850 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.3015 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.3184 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.3349 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.3517 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.3700 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.3849 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.4013 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.4182 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.4349 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.4514 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.4681 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.4846 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.5014 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.5184 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.5355 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.5516 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.5680 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.5850 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.6014 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.6183 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.6345 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.6513 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.6677 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.6852 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.7019 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.7175 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.7340 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.7510 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.7678 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.7848 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.8009 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.8179 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.8344 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.8511 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.8680 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.8843 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.9016 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.9183 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.9347 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.9507 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.9674 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
57.9840 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.0007 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
58.0178 	EXP 	unnamed TextStim: height = 0.03
58.0178 	EXP 	unnamed TextStim: height = 0.03
58.0178 	EXP 	unnamed TextStim: height = 0.03
58.0178 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.0341 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.0507 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.0682 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.0846 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.1009 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.1174 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.1349 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.1507 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.1673 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.1843 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.2007 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.2179 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.2342 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.2508 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.2668 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.2835 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.3006 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.3175 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.3342 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.3511 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.3679 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.3845 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.4002 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.4171 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.4338 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.4506 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.4673 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.4839 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.5006 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.5177 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.5343 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.5507 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.5675 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.5840 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.6005 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.6172 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.6335 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.6503 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.6673 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.6835 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.7002 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.7175 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.7342 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.7501 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.7671 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.7837 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.8001 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.8174 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.8341 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.8506 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.8678 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.8837 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.9001 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.9172 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.9338 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.9499 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.9663 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.9834 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
58.9999 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
59.0165 	EXP 	unnamed TextStim: height = 0.03
59.0165 	EXP 	unnamed TextStim: height = 0.03
59.0165 	EXP 	unnamed TextStim: height = 0.03
59.0165 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.0341 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.0507 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.0669 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.0834 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.0998 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.1183 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.1342 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.1505 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.1671 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.1839 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.2006 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.2175 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.2341 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.2496 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.2666 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.2831 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.2999 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.3165 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.3332 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.3503 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.3666 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.3834 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.3998 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.4228 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.4328 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.4496 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.4659 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.4824 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.5004 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.5167 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.5333 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.5501 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.5666 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.5831 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.6005 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.6164 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.6333 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.6494 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.6663 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.6827 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.6996 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.7163 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.7328 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.7497 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.7660 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.7822 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.7995 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.8159 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.8327 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.8497 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.8669 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.8829 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.9002 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.9164 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.9330 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.9495 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.9664 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.9824 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
59.9988 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
60.0156 	EXP 	unnamed TextStim: height = 0.03
60.0156 	EXP 	unnamed TextStim: height = 0.03
60.0156 	EXP 	unnamed TextStim: height = 0.03
60.0156 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.0321 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.0494 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.0661 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.0827 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.0991 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.1174 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.1346 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.1493 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.1659 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.1825 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.1991 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.2158 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.2330 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.2490 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.2659 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.2826 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.2992 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.3161 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.3326 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.3491 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.3659 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.3823 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.3994 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.4157 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.4324 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.4494 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.4660 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.4826 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.4990 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.5157 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.5323 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.5492 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.5660 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.5825 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.5990 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.6154 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.6321 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.6491 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.6654 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.6818 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.6987 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.7150 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.7325 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.7492 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.7656 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.7822 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.7983 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.8156 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.8319 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.8489 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.8656 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.8819 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.8986 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.9152 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.9327 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.9488 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.9654 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.9816 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
60.9986 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
63.0706 	EXP 	stroop_instr: autoDraw = True
63.0706 	EXP 	instrText_4: autoDraw = True
64.0836 	DATA 	Keypress: escape
64.2308 	EXP 	01_resting_state: status = STOPPED
64.2459 	EXP 	stroop_instr: autoDraw = False
64.2459 	EXP 	instrText_4: autoDraw = False
64.2461 	EXP 	01_resting_state: status = STOPPED
64.3331 	EXP 	window1: mouseVisible = True
