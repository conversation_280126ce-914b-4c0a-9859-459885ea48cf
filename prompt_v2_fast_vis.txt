The file is set up as a breathing visualizer for slow paced breathing.
I want to modify in the following way (don't modify yet, I'll just explain and ask questions to plan):

I want to select between two modes based on a variable: breath_protocol.

Mode 1 = paced_breathing:
	- keep the current setup:
		# Default breathing pattern settings (in seconds)
		inhale_time = 4.5
		inhale_hold_time = 0.5
		exhale_time = 4.5
		exhale_hold_time = 0.5

Mode 2 = fast_with_breath_hold:
	1 round =
	(
		30 breaths: 1.5 sec inhale, 1.5 sec exhale (no pause) [90 sec total]
		exhale hold (lungs empty): 15 sec
		inhale hold (lungs full): 15 sec
	)
	Repeat 3 rounds
	Total time: 6 minutes
	
Question:
- how would it be best to visualize mode 2 fast_with_breath_hold in terms of the line segments and ball traveling? here is my guess, suggest better if needed:
(
	- one round:
		- within the 1 round's 30 breaths, always just show the 1.5s inhale 1.5s exhale (no pauses) similar to mode 1
		- within the 1 round's holds, visualize a 1s quick inhale, then a 14s hold, then a 1s exhale, then a 14s hold
	- repeat 3 rounds like this
)