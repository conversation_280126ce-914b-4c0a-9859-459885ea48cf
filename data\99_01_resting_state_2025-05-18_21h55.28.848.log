8.5677 	WARNING 	User requested fullscreen with size [1536  864], but screen is actually [1920, 1080]. Using actual size
9.2804 	EXP 	Created window1 = Window(allowGUI=True, allowStencil=True, autoLog=True, backendConf=UNKNOWN, backgroundFit=<method-wrapper '__getattribute__' of attributeSetter object at 0x0000018559677CA0>, backgroundImage=<method-wrapper '__getattribute__' of attributeSetter object at 0x0000018559677C40>, bitsMode=UNKNOWN, blendMode='avg', bpc=(8, 8, 8), color=array([0, 0, 0]), colorSpace='rgb', depthBits=8, fullscr=<method-wrapper '__getattribute__' of attributeSetter object at 0x0000018559677AC0>, gamma=None, gammaErrorPolicy='raise', lms=UNKNOWN, monitor=<psychopy.monitors.calibTools.Monitor object at 0x0000018559C257F0>, multiSample=False, name='window1', numSamples=2, pos=(0, 0), screen=0, size=array([1920, 1080]), stencilBits=8, stereo=False, title='PsychoPy', units='height', useFBO=True, useRetina=False, viewOri=0.0, viewPos=None, viewScale=None, waitBlanking=True, winType='pyglet')
9.2806 	EXP 	window1: mouseVisible = True
9.2806 	EXP 	window1: backgroundImage = ''
9.2806 	EXP 	window1: backgroundFit = 'none'
9.2844 	EXP 	window1: Attempting to measure frame rate of screen (0) ...
9.2845 	EXP 	window1: recordFrameIntervals = False
9.4458 	EXP 	window1: recordFrameIntervals = True
9.6299 	EXP 	Screen (0) actual frame rate measured at 59.86Hz
9.6300 	EXP 	window1: recordFrameIntervals = False
9.6304 	EXP 	window1: mouseVisible = False
12.9069 	EXP 	01_resting_state: status = STARTED
13.2520 	EXP 	window1: mouseVisible = True
13.3557 	EXP 	Created text = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.3559 	EXP 	window1: mouseVisible = True
13.3612 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
13.3613 	EXP 	window1: mouseVisible = True
13.3771 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.3794 	EXP 	Sound exp_end_bip set volume 1.000
13.3796 	EXP 	window1: mouseVisible = True
13.3915 	EXP 	Created text_2 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_2', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.3966 	EXP 	window1: mouseVisible = True
13.4255 	EXP 	window1: mouseVisible = True
13.4334 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.4355 	EXP 	Sound exp_end_bip set volume 1.000
13.4357 	EXP 	window1: mouseVisible = True
13.4437 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.4439 	EXP 	window1: mouseVisible = True
13.4511 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.4531 	EXP 	Sound exp_end_bip set volume 1.000
13.4533 	EXP 	window1: mouseVisible = True
13.4610 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.4629 	EXP 	Sound exp_end_bip set volume 1.000
13.4631 	EXP 	window1: mouseVisible = True
13.4682 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.4684 	EXP 	window1: mouseVisible = True
13.4756 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.4778 	EXP 	Sound exp_end_bip set volume 1.000
13.4781 	EXP 	window1: mouseVisible = True
13.4947 	EXP 	Created stroop_instr = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='stroop_instr', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.4949 	EXP 	window1: mouseVisible = True
13.5060 	EXP 	Created instrText_4 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-2.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_4', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.5062 	EXP 	window1: mouseVisible = True
13.5079 	EXP 	Created stim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.15, italic=False, languageStyle='LTR', name='stim', opacity=1.0, ori=0, pos=array([0., 0.]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
13.5082 	EXP 	window1: mouseVisible = True
13.5083 	EXP 	window1: mouseVisible = True
13.5088 	EXP 	window1: mouseVisible = True
13.5092 	EXP 	window1: mouseVisible = True
13.5146 	EXP 	window1: mouseVisible = True
13.5148 	EXP 	window1: mouseVisible = True
13.5155 	EXP 	window1: mouseVisible = True
13.5161 	EXP 	window1: mouseVisible = True
13.5404 	EXP 	window1: mouseVisible = True
13.5457 	EXP 	Created instrText_2 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-4.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_2', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.5459 	EXP 	window1: mouseVisible = True
13.5461 	EXP 	window1: mouseVisible = True
13.5467 	EXP 	window1: mouseVisible = True
13.5471 	EXP 	window1: mouseVisible = True
13.5515 	EXP 	window1: mouseVisible = True
13.5516 	EXP 	window1: mouseVisible = True
13.5520 	EXP 	window1: mouseVisible = True
13.5524 	EXP 	window1: mouseVisible = True
13.5567 	EXP 	window1: mouseVisible = True
13.5568 	EXP 	window1: mouseVisible = True
13.5574 	EXP 	window1: mouseVisible = True
13.5578 	EXP 	window1: mouseVisible = True
13.5623 	EXP 	window1: mouseVisible = True
13.5625 	EXP 	window1: mouseVisible = True
13.5630 	EXP 	window1: mouseVisible = True
13.5635 	EXP 	window1: mouseVisible = True
13.5675 	EXP 	window1: mouseVisible = True
13.5726 	EXP 	Created instrText_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-3.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_3', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.5727 	EXP 	window1: mouseVisible = True
13.5911 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.5932 	EXP 	Sound exp_end_bip set volume 1.000
13.5934 	EXP 	window1: mouseVisible = True
13.6013 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.6015 	EXP 	window1: mouseVisible = True
13.6099 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.6118 	EXP 	Sound exp_end_bip set volume 1.000
13.6120 	EXP 	window1: mouseVisible = True
13.6183 	EXP 	Created text = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.6185 	EXP 	window1: mouseVisible = True
13.6228 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
13.6231 	EXP 	window1: mouseVisible = True
13.6366 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.6390 	EXP 	Sound exp_end_bip set volume 1.000
13.6392 	EXP 	window1: mouseVisible = True
13.6471 	EXP 	Created EndMessage = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='EndMessage', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='Thank you!', units='height', win=Window(...), wrapWidth=1)
0.0055 	EXP 	video: autoLog = True
0.0055 	EXP 	trial_counter: autoLog = True
0.0055 	EXP 	fbtxt: autoLog = True
0.0055 	EXP 	trial_counter_2: autoLog = True
0.0264 	EXP 	text: autoDraw = True
1.4341 	DATA 	Keypress: f1
1.4445 	EXP 	text: autoDraw = False
1.4445 	EXP 	cross: autoDraw = True
7.5272 	DATA 	Mouse: Left button down, pos=(837,687)
7.5312 	DATA 	Mouse:  Left button up, pos=(837,687)
12.4514 	EXP 	Sound exp_end_bip started
12.4516 	EXP 	cross: autoDraw = False
12.4516 	EXP 	cross: autoDraw = False
12.4516 	EXP 	text_q_stress: autoDraw = True
13.1730 	DATA 	Keypress: f1
13.3775 	EXP 	Sound exp_end_bip stopped
13.3775 	EXP 	Sound exp_end_bip paused
13.3876 	EXP 	text_q_stress: autoDraw = False
13.3876 	EXP 	text_2: autoDraw = True
14.3470 	DATA 	Keypress: f1
14.3549 	EXP 	text_2: autoDraw = False
14.3549 	EXP 	video: autoDraw = True
16.6193 	EXP 	Sound exp_end_bip reached end of file
16.6328 	EXP 	Sound exp_end_bip started
16.6267 	EXP 	video: autoDraw = False
16.6267 	EXP 	video: autoDraw = False
16.6267 	EXP 	text_q_stress: autoDraw = True
17.2930 	EXP 	Sound exp_end_bip stopped
17.2931 	EXP 	Sound exp_end_bip paused
17.2967 	DATA 	Keypress: f1
17.3013 	EXP 	text_q_stress: autoDraw = False
17.3013 	EXP 	text_3: autoDraw = True
17.8998 	EXP 	window1: mouseVisible = True
17.9038 	EXP 	Created unnamed ShapeStim = ShapeStim(__class__=<class 'psychopy.visual.shape.ShapeStim'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=False, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0, 0, 0]), fillColorSpace='rgb', fillRGB=array([0, 0, 0]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=3, name='unnamed ShapeStim', opacity=0.35, ori=0.0, pos=array([0., 0.]), size=array([1., 1.]), units='height', vertices=ndarray(...), win=Window(...), windingRule=None)
17.9121 	EXP 	window1: mouseVisible = True
17.9217 	EXP 	Created unnamed Circle = Circle(__class__=<class 'psychopy.visual.circle.Circle'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=2, name='unnamed Circle', opacity=1.0, ori=0.0, pos=array([0., 0.]), size=array([1., 1.]), units='height', vertices=ndarray(...), win=Window(...))
17.9219 	EXP 	window1: mouseVisible = True
17.9240 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.05882353, 0.61568627, 0.84313725]), fillColorSpace='rgb', fillRGB=array([0.05882353, 0.61568627, 0.84313725]), interpolate=True, lineColor=array([0., 0., 0.]), lineColorSpace='rgb', lineRGB=array([0, 0, 0]), lineWidth=1.5, name='unnamed Rect', opacity=0.1, ori=0.0, pos=array([0., 0.]), size=array([1.77777778, 0.5       ]), units='height', vertices=ndarray(...), win=Window(...))
17.9242 	EXP 	window1: mouseVisible = True
17.9329 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([ 0.  , -0.35]), rgb=UNKNOWN, text='Inhale', units='height', win=Window(...), wrapWidth=1)
17.9331 	EXP 	window1: mouseVisible = True
17.9351 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([0. , 0.4]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
17.9353 	EXP 	window1: mouseVisible = True
17.9376 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.0037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9377 	EXP 	window1: mouseVisible = True
17.9400 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.0287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9401 	EXP 	window1: mouseVisible = True
17.9429 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.0537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9430 	EXP 	window1: mouseVisible = True
17.9451 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.0787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9452 	EXP 	window1: mouseVisible = True
17.9473 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.1037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9475 	EXP 	window1: mouseVisible = True
17.9493 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.1287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9494 	EXP 	window1: mouseVisible = True
17.9512 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.1537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9513 	EXP 	window1: mouseVisible = True
17.9529 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.1787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9530 	EXP 	window1: mouseVisible = True
17.9544 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.2037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9545 	EXP 	window1: mouseVisible = True
17.9560 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.2287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9561 	EXP 	window1: mouseVisible = True
17.9575 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.2537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9576 	EXP 	window1: mouseVisible = True
17.9592 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.2787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9593 	EXP 	window1: mouseVisible = True
17.9609 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.3037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9610 	EXP 	window1: mouseVisible = True
17.9625 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.3287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9626 	EXP 	window1: mouseVisible = True
17.9643 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.3537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9644 	EXP 	window1: mouseVisible = True
17.9658 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.3787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9659 	EXP 	window1: mouseVisible = True
17.9673 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.4037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9674 	EXP 	window1: mouseVisible = True
17.9688 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.4287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9688 	EXP 	window1: mouseVisible = True
17.9703 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.4537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9704 	EXP 	window1: mouseVisible = True
17.9720 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.4787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9721 	EXP 	window1: mouseVisible = True
17.9735 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.5037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9736 	EXP 	window1: mouseVisible = True
17.9751 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.5287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9752 	EXP 	window1: mouseVisible = True
17.9770 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.5537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9773 	EXP 	window1: mouseVisible = True
17.9789 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.5787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9790 	EXP 	window1: mouseVisible = True
17.9804 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.6037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9805 	EXP 	window1: mouseVisible = True
17.9820 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.6287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9821 	EXP 	window1: mouseVisible = True
17.9835 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.6537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9836 	EXP 	window1: mouseVisible = True
17.9851 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.6787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9852 	EXP 	window1: mouseVisible = True
17.9867 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.7037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9867 	EXP 	window1: mouseVisible = True
17.9883 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.7287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
17.9884 	EXP 	window1: mouseVisible = True
17.9972 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([-0.3962963, -0.4      ]), rgb=UNKNOWN, text='Round: 1/3', units='height', win=Window(...), wrapWidth=1)
17.9974 	EXP 	window1: mouseVisible = True
17.9975 	WARNING 	TextStim.alignHoriz is deprecated. Use alignText and anchorHoriz attributes instead
18.0044 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz='left', alignText='left', alignVert=method-wrapper(...), anchorHoriz='left', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([-0.1962963, -0.4      ]), rgb=UNKNOWN, text='Breaths remaining:', units='height', win=Window(...), wrapWidth=1)
18.0350 	DATA 	Keypress: f1
18.0408 	EXP 	text_3: autoDraw = False
18.0408 	EXP 	unnamed TextStim: height = 0.05
18.0408 	EXP 	unnamed TextStim: height = 0.05
18.0408 	EXP 	unnamed TextStim: height = 0.05
18.0408 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.0408 	EXP 	unnamed TextStim: height = 0.03
18.0408 	EXP 	unnamed TextStim: height = 0.03
18.0408 	EXP 	unnamed TextStim: height = 0.03
18.0408 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.0408 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.0621 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.0621 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.0621 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.0825 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.0825 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.0825 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.1038 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.1038 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.1038 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.1271 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.1271 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.1271 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.1490 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.1490 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.1490 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.1688 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.1688 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.1688 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.1890 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.1890 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.1890 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.2088 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.2088 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.2088 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.2326 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.2326 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.2326 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.2523 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.2523 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.2523 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.2761 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.2761 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.2761 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.3013 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.3013 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.3013 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.3245 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.3245 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.3245 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.3496 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.3496 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.3496 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.3739 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.3739 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.3739 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.3952 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.3952 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.3952 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.4151 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.4151 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.4151 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.4385 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.4385 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.4385 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.4622 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.4622 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.4622 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.4865 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.4865 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.4865 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.5072 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.5072 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.5072 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.5272 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.5272 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.5272 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.5497 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.5497 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.5497 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.5740 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.5740 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.5740 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.6010 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.6010 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.6010 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.6256 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.6256 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.6256 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.6470 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.6470 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.6470 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.6716 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.6716 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.6716 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.6945 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.6945 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.6945 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.7187 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.7187 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.7187 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.7390 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.7390 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.7390 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.7608 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.7608 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.7608 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.7817 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.7817 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.7817 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.8033 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.8033 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.8033 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.8236 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.8236 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.8236 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.8444 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.8444 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.8444 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.8665 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.8665 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.8665 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.8879 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.8879 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.8879 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.9086 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.9086 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.9086 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.9305 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.9305 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.9305 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.9556 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.9556 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.9556 	EXP 	unnamed TextStim: text = 'Round: 1/3'
18.9813 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
18.9813 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
18.9813 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.0069 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.0069 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
19.0069 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.0380 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.0380 	EXP 	unnamed TextStim: height = 0.03
19.0380 	EXP 	unnamed TextStim: height = 0.03
19.0380 	EXP 	unnamed TextStim: height = 0.03
19.0380 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.0380 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.0654 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.0654 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.0654 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.0870 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.0870 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.0870 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.1107 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.1107 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.1107 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.1326 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.1326 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.1326 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.1540 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.1540 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.1540 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.1752 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.1752 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.1752 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.1980 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.1980 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.1980 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.2210 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.2210 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.2210 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.2421 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.2421 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.2421 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.2652 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.2652 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.2652 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.2941 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.2941 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.2941 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.3194 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.3194 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.3194 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.3439 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.3439 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.3439 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.3664 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.3664 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.3664 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.3887 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.3887 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.3887 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.4136 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.4136 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.4136 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.4356 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.4356 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.4356 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.4573 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.4573 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.4573 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.4792 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.4792 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.4792 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.5001 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.5001 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.5001 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.5231 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
19.5231 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.5231 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.5572 	EXP 	unnamed TextStim: height = 0.05
19.5572 	EXP 	unnamed TextStim: height = 0.05
19.5572 	EXP 	unnamed TextStim: height = 0.05
19.5572 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.5572 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.5572 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.5860 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.5860 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.5860 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.6117 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.6117 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.6117 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.6356 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.6356 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.6356 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.6574 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.6574 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.6574 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.6784 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.6784 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.6784 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.6993 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.6993 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.6993 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.7206 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.7206 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.7206 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.7399 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.7399 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.7399 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.7600 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.7600 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.7600 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.7812 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.7812 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.7812 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.8078 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.8078 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.8078 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.8322 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.8322 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.8322 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.8607 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.8607 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.8607 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.8860 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.8860 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.8860 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.9090 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.9090 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.9090 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.9317 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.9317 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.9317 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.9522 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.9522 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.9522 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.9721 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.9721 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.9721 	EXP 	unnamed TextStim: text = 'Round: 1/3'
19.9931 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
19.9931 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
19.9931 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.0154 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.0154 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
20.0154 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.0447 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.0447 	EXP 	unnamed TextStim: height = 0.03
20.0447 	EXP 	unnamed TextStim: height = 0.03
20.0447 	EXP 	unnamed TextStim: height = 0.03
20.0447 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.0447 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.0719 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.0719 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.0719 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.0970 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.0970 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.0970 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.1214 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.1214 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.1214 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.1466 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.1466 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.1466 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.1691 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.1691 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.1691 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.1908 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.1908 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.1908 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.2172 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.2172 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.2172 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.2432 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.2432 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.2432 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.2699 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.2699 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.2699 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.2931 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.2931 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.2931 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.3142 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.3142 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.3142 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.3344 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.3344 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.3344 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.3566 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.3566 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.3566 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.3817 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.3817 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.3817 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.4037 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.4037 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.4037 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.4246 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.4246 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.4246 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.4473 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.4473 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.4473 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.4686 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.4686 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.4686 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.4900 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.4900 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.4900 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.5177 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.5177 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.5177 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.5387 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.5387 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.5387 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.5608 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.5608 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.5608 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.5829 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.5829 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.5829 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.6073 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.6073 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.6073 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.6323 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.6323 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.6323 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.6537 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.6537 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.6537 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.6759 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.6759 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.6759 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.7018 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.7018 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.7018 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.7233 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.7233 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.7233 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.7436 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.7436 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.7436 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.7634 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.7634 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.7634 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.7836 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.7836 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.7836 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.8061 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.8061 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.8061 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.8319 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.8319 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.8319 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.8538 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.8538 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.8538 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.8755 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.8755 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.8755 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.8962 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.8962 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.8962 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.9179 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.9179 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.9179 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.9392 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.9392 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.9392 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.9639 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.9639 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.9639 	EXP 	unnamed TextStim: text = 'Round: 1/3'
20.9862 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
20.9862 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
20.9862 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.0088 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
21.0088 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
21.0088 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.0396 	EXP 	unnamed TextStim: height = 0.05
21.0396 	EXP 	unnamed TextStim: height = 0.05
21.0396 	EXP 	unnamed TextStim: height = 0.05
21.0396 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.0396 	EXP 	unnamed TextStim: height = 0.03
21.0396 	EXP 	unnamed TextStim: height = 0.03
21.0396 	EXP 	unnamed TextStim: height = 0.03
21.0396 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.0396 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.0666 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.0666 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.0666 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.0949 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.0949 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.0949 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.1183 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.1183 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.1183 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.1399 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.1399 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.1399 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.1608 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.1608 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.1608 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.1811 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.1811 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.1811 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.2017 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.2017 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.2017 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.2209 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.2209 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.2209 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.2489 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.2489 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.2489 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.2716 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.2716 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.2716 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.2920 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.2920 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.2920 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.3146 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.3146 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.3146 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.3406 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.3406 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.3406 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.3654 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.3654 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.3654 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.3881 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.3881 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.3881 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.4121 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.4121 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.4121 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.4362 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.4362 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.4362 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.4601 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.4601 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.4601 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.4827 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.4827 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.4827 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.5031 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.5031 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.5031 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.5252 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.5252 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.5252 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.5460 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.5460 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.5460 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.5668 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.5668 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.5668 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.5892 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.5892 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.5892 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.6121 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.6121 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.6121 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.6340 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.6340 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.6340 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.6580 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.6580 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.6580 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.6788 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.6788 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.6788 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.7016 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.7016 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.7016 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.7218 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.7218 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.7218 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.7463 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.7463 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.7463 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.7673 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.7673 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.7673 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.7877 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.7877 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.7877 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.8131 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.8131 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.8131 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.8373 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.8373 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.8373 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.8616 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.8616 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.8616 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.8828 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.8828 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.8828 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.9077 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.9077 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.9077 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.9320 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.9320 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.9320 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.9534 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.9534 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.9534 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.9745 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.9745 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.9745 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.9961 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
21.9961 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
21.9961 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.0208 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.0208 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
22.0208 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.0488 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.0488 	EXP 	unnamed TextStim: height = 0.03
22.0488 	EXP 	unnamed TextStim: height = 0.03
22.0488 	EXP 	unnamed TextStim: height = 0.03
22.0488 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.0488 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.0716 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.0716 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.0716 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.0917 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.0917 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.0917 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.1135 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.1135 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.1135 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.1341 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.1341 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.1341 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.1544 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.1544 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.1544 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.1764 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.1764 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.1764 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.1988 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.1988 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.1988 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.2210 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.2210 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.2210 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.2468 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.2468 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.2468 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.2708 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.2708 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.2708 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.2964 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.2964 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.2964 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.3173 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.3173 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.3173 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.3375 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.3375 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.3375 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.3616 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.3616 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.3616 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.3842 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.3842 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.3842 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.4069 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.4069 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.4069 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.4314 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.4314 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.4314 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.4580 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.4580 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.4580 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.4828 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.4828 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.4828 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.5043 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.5043 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.5043 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.5266 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
22.5266 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.5266 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.5499 	EXP 	unnamed TextStim: height = 0.05
22.5499 	EXP 	unnamed TextStim: height = 0.05
22.5499 	EXP 	unnamed TextStim: height = 0.05
22.5499 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.5499 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.5499 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.5711 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.5711 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.5711 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.5921 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.5921 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.5921 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.6138 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.6138 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.6138 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.6354 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.6354 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.6354 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.6572 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.6572 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.6572 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.6785 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.6785 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.6785 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.7024 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.7024 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.7024 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.7288 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.7288 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.7288 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.7549 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.7549 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.7549 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.7790 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.7790 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.7790 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.8036 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.8036 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.8036 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.8259 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.8259 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.8259 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.8483 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.8483 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.8483 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.8714 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.8714 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.8714 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.8936 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.8936 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.8936 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.9139 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.9139 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.9139 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.9349 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.9349 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.9349 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.9591 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.9591 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.9591 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.9818 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
22.9818 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
22.9818 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.0027 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.0027 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
23.0027 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.0229 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.0229 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
23.0229 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.0476 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.0476 	EXP 	unnamed TextStim: height = 0.03
23.0476 	EXP 	unnamed TextStim: height = 0.03
23.0476 	EXP 	unnamed TextStim: height = 0.03
23.0476 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.0476 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.0683 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.0683 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.0683 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.0906 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.0906 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.0906 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.1136 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.1136 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.1136 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.1352 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.1352 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.1352 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.1568 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.1568 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.1568 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.1809 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.1809 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.1809 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.2035 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.2035 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.2035 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.2251 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.2251 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.2251 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.2490 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.2490 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.2490 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.2693 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.2693 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.2693 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.2908 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.2908 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.2908 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.3113 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.3113 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.3113 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.3364 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.3364 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.3364 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.3627 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.3627 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.3627 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.3849 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.3849 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.3849 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.4067 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.4067 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.4067 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.4293 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.4293 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.4293 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.4536 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.4536 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.4536 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.4743 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.4743 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.4743 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.4948 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.4948 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.4948 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.5159 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.5159 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.5159 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.5354 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.5354 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.5354 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.5573 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.5573 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.5573 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.5807 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.5807 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.5807 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.6068 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.6068 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.6068 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.6286 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.6286 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.6286 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.6516 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.6516 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.6516 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.6759 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.6759 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.6759 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.6974 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.6974 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.6974 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.7214 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.7214 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.7214 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.7483 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.7483 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.7483 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.7681 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.7681 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.7681 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.7892 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.7892 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.7892 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.8105 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.8105 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.8105 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.8322 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.8322 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.8322 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.8521 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.8521 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.8521 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.8715 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.8715 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.8715 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.8931 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.8931 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.8931 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.9135 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.9135 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.9135 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.9328 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.9328 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.9328 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.9534 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.9534 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.9534 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.9759 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.9759 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.9759 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.9956 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
23.9956 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
23.9956 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.0190 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
24.0190 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
24.0190 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.0541 	EXP 	unnamed TextStim: height = 0.05
24.0541 	EXP 	unnamed TextStim: height = 0.05
24.0541 	EXP 	unnamed TextStim: height = 0.05
24.0541 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.0541 	EXP 	unnamed TextStim: height = 0.03
24.0541 	EXP 	unnamed TextStim: height = 0.03
24.0541 	EXP 	unnamed TextStim: height = 0.03
24.0541 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.0541 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.0765 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.0765 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.0765 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.0959 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.0959 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.0959 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.1147 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.1147 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.1147 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.1370 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.1370 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.1370 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.1621 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.1621 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.1621 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.1858 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.1858 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.1858 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.2096 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.2096 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.2096 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.2330 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.2330 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.2330 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.2526 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.2526 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.2526 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.2742 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.2742 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.2742 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.2945 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.2945 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.2945 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.3150 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.3150 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.3150 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.3344 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.3344 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.3344 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.3556 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.3556 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.3556 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.3780 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.3780 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.3780 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.3998 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.3998 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.3998 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.4200 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.4200 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.4200 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.4434 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.4434 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.4434 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.4706 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.4706 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.4706 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.4928 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.4928 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.4928 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.5156 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.5156 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.5156 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.5409 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.5409 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.5409 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.5624 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.5624 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.5624 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.5827 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.5827 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.5827 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.6094 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.6094 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.6094 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.6365 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.6365 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.6365 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.6580 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.6580 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.6580 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.6796 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.6796 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.6796 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.7019 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.7019 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.7019 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.7239 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.7239 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.7239 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.7517 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.7517 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.7517 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.7750 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.7750 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.7750 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.7979 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.7979 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.7979 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.8214 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.8214 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.8214 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.8430 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.8430 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.8430 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.8621 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.8621 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.8621 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.8810 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.8810 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.8810 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.9033 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.9033 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.9033 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.9253 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.9253 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.9253 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.9468 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.9468 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.9468 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.9664 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.9664 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.9664 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.9853 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
24.9853 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
24.9853 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.0100 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.0100 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
25.0100 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.0348 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.0348 	EXP 	unnamed TextStim: height = 0.03
25.0348 	EXP 	unnamed TextStim: height = 0.03
25.0348 	EXP 	unnamed TextStim: height = 0.03
25.0348 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.0348 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.0613 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.0613 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.0613 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.0830 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.0830 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.0830 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.1055 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.1055 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.1055 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.1303 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.1303 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.1303 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.1523 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.1523 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.1523 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.1745 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.1745 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.1745 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.2011 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.2011 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.2011 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.2273 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.2273 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.2273 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.2524 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.2524 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.2524 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.2723 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.2723 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.2723 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.2932 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.2932 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.2932 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.3133 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.3133 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.3133 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.3345 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.3345 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.3345 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.3570 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.3570 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.3570 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.3820 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.3820 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.3820 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.4071 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.4071 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.4071 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.4298 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.4298 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.4298 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.4552 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.4552 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.4552 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.4775 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.4775 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.4775 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.5032 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.5032 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.5032 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.5297 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
25.5297 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.5297 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.5536 	EXP 	unnamed TextStim: height = 0.05
25.5536 	EXP 	unnamed TextStim: height = 0.05
25.5536 	EXP 	unnamed TextStim: height = 0.05
25.5536 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.5536 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.5536 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.5790 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.5790 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.5790 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.6058 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.6058 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.6058 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.6290 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.6290 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.6290 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.6554 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.6554 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.6554 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.6828 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.6828 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.6828 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.7055 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.7055 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.7055 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.7296 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.7296 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.7296 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.7574 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.7574 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.7574 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.7811 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.7811 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.7811 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.8025 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.8025 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.8025 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.8287 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.8287 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.8287 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.8543 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.8543 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.8543 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.8788 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.8788 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.8788 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.8994 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.8994 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.8994 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.9218 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.9218 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.9218 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.9454 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.9454 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.9454 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.9672 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.9672 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.9672 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.9884 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
25.9884 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
25.9884 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.0119 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.0119 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
26.0119 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.0383 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.0383 	EXP 	unnamed TextStim: height = 0.03
26.0383 	EXP 	unnamed TextStim: height = 0.03
26.0383 	EXP 	unnamed TextStim: height = 0.03
26.0383 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.0383 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.0600 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.0600 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.0600 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.0837 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.0837 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.0837 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.1056 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.1056 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.1056 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.1313 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.1313 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.1313 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.1530 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.1530 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.1530 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.1755 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.1755 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.1755 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.1990 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.1990 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.1990 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.2210 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.2210 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.2210 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.2430 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.2430 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.2430 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.2658 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.2658 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.2658 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.2881 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.2881 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.2881 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.3114 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.3114 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.3114 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.3344 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.3344 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.3344 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.3544 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.3544 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.3544 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.3758 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.3758 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.3758 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.3969 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.3969 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.3969 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.4168 	DATA 	Keypress: quoteleft
26.4215 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.4215 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.4215 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.4480 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.4480 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.4480 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.4690 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.4690 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.4690 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.4931 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.4931 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.4931 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.5130 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.5130 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.5130 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.5336 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.5336 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.5336 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.5571 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.5571 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.5571 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.5811 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.5811 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.5811 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.6035 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.6035 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.6035 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.6272 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.6272 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.6272 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.6500 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.6500 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.6500 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.6788 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.6788 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.6788 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.7074 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.7074 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.7074 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.7333 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.7333 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.7333 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.7601 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
26.7601 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
26.7601 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.7825 	DATA 	Keypress: escape
27.6457 	EXP 	window1: mouseVisible = True
