7.4363 	WARNING 	User requested fullscreen with size [1536  864], but screen is actually [1920, 1080]. Using actual size
8.2101 	EXP 	Created window1 = Window(allowGUI=True, allowStencil=True, autoLog=True, backendConf=UNKNOWN, backgroundFit=<method-wrapper '__getattribute__' of attributeSetter object at 0x000002038EB79C40>, backgroundImage=<method-wrapper '__getattribute__' of attributeSetter object at 0x000002038EB79BE0>, bitsMode=UNKNOWN, blendMode='avg', bpc=(8, 8, 8), color=array([0, 0, 0]), colorSpace='rgb', depthBits=8, fullscr=<method-wrapper '__getattribute__' of attributeSetter object at 0x000002038EB79A30>, gamma=None, gammaErrorPolicy='raise', lms=UNKNOWN, monitor=<psychopy.monitors.calibTools.Monitor object at 0x000002039A6C0550>, multiSample=False, name='window1', numSamples=2, pos=(0, 0), screen=0, size=array([1920, 1080]), stencilBits=8, stereo=False, title='PsychoPy', units='height', useFBO=True, useRetina=False, viewOri=0.0, viewPos=None, viewScale=None, waitBlanking=True, winType='pyglet')
8.2102 	EXP 	window1: mouseVisible = True
8.2102 	EXP 	window1: backgroundImage = ''
8.2102 	EXP 	window1: backgroundFit = 'none'
8.2112 	EXP 	window1: Attempting to measure frame rate of screen (0) ...
8.2113 	EXP 	window1: recordFrameIntervals = False
8.3759 	EXP 	window1: recordFrameIntervals = True
8.4345 	WARNING 	t of last frame was 25.17ms (=1/39)
8.6090 	EXP 	Screen (0) actual frame rate measured at 60.79Hz
8.6091 	EXP 	window1: recordFrameIntervals = False
8.6092 	EXP 	window1: mouseVisible = False
11.2264 	EXP 	01_resting_state: status = STARTED
11.5964 	EXP 	window1: mouseVisible = True
11.6749 	EXP 	Created text = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.6751 	EXP 	window1: mouseVisible = True
11.6811 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
11.6813 	EXP 	window1: mouseVisible = True
11.6962 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.6989 	EXP 	Sound exp_end_bip set volume 1.000
11.6991 	EXP 	window1: mouseVisible = True
11.7118 	EXP 	Created text_2 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_2', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.7184 	EXP 	window1: mouseVisible = True
11.7470 	EXP 	window1: mouseVisible = True
11.7570 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.7594 	EXP 	Sound exp_end_bip set volume 1.000
11.7597 	EXP 	window1: mouseVisible = True
11.7683 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.7686 	EXP 	window1: mouseVisible = True
11.7765 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.7787 	EXP 	Sound exp_end_bip set volume 1.000
11.7789 	EXP 	window1: mouseVisible = True
11.7878 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.7901 	EXP 	Sound exp_end_bip set volume 1.000
11.7904 	EXP 	window1: mouseVisible = True
11.7960 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.7963 	EXP 	window1: mouseVisible = True
11.8042 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.8066 	EXP 	Sound exp_end_bip set volume 1.000
11.8069 	EXP 	window1: mouseVisible = True
11.8252 	EXP 	Created stroop_instr = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='stroop_instr', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.8254 	EXP 	window1: mouseVisible = True
11.8385 	EXP 	Created instrText_4 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-2.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_4', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.8388 	EXP 	window1: mouseVisible = True
11.8409 	EXP 	Created stim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.15, italic=False, languageStyle='LTR', name='stim', opacity=1.0, ori=0, pos=array([0., 0.]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
11.8413 	EXP 	window1: mouseVisible = True
11.8415 	EXP 	window1: mouseVisible = True
11.8420 	EXP 	window1: mouseVisible = True
11.8425 	EXP 	window1: mouseVisible = True
11.8477 	EXP 	window1: mouseVisible = True
11.8479 	EXP 	window1: mouseVisible = True
11.8484 	EXP 	window1: mouseVisible = True
11.8488 	EXP 	window1: mouseVisible = True
11.8740 	EXP 	window1: mouseVisible = True
11.8793 	EXP 	Created instrText_2 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-4.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_2', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.8796 	EXP 	window1: mouseVisible = True
11.8798 	EXP 	window1: mouseVisible = True
11.8803 	EXP 	window1: mouseVisible = True
11.8807 	EXP 	window1: mouseVisible = True
11.8862 	EXP 	window1: mouseVisible = True
11.8865 	EXP 	window1: mouseVisible = True
11.8873 	EXP 	window1: mouseVisible = True
11.8877 	EXP 	window1: mouseVisible = True
11.8922 	EXP 	window1: mouseVisible = True
11.8924 	EXP 	window1: mouseVisible = True
11.8929 	EXP 	window1: mouseVisible = True
11.8933 	EXP 	window1: mouseVisible = True
11.8973 	EXP 	window1: mouseVisible = True
11.8975 	EXP 	window1: mouseVisible = True
11.8979 	EXP 	window1: mouseVisible = True
11.8983 	EXP 	window1: mouseVisible = True
11.9042 	EXP 	window1: mouseVisible = True
11.9093 	EXP 	Created instrText_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-3.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_3', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.9095 	EXP 	window1: mouseVisible = True
11.9263 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.9283 	EXP 	Sound exp_end_bip set volume 1.000
11.9285 	EXP 	window1: mouseVisible = True
11.9401 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.9404 	EXP 	window1: mouseVisible = True
11.9490 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.9517 	EXP 	Sound exp_end_bip set volume 1.000
11.9520 	EXP 	window1: mouseVisible = True
11.9596 	EXP 	Created text = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.9599 	EXP 	window1: mouseVisible = True
11.9643 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
11.9646 	EXP 	window1: mouseVisible = True
11.9785 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.9807 	EXP 	Sound exp_end_bip set volume 1.000
11.9809 	EXP 	window1: mouseVisible = True
11.9893 	EXP 	Created EndMessage = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='EndMessage', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='Thank you!', units='height', win=Window(...), wrapWidth=1)
0.0054 	EXP 	video: autoLog = True
0.0054 	EXP 	trial_counter: autoLog = True
0.0054 	EXP 	fbtxt: autoLog = True
0.0054 	EXP 	trial_counter_2: autoLog = True
0.0169 	EXP 	text: autoDraw = True
10.0280 	DATA 	Keypress: f1
10.0408 	EXP 	text: autoDraw = False
10.0408 	EXP 	cross: autoDraw = True
21.0487 	EXP 	Sound exp_end_bip started
21.0487 	EXP 	cross: autoDraw = False
21.0487 	EXP 	cross: autoDraw = False
21.0487 	EXP 	text_q_stress: autoDraw = True
22.2156 	EXP 	Sound exp_end_bip stopped
22.4160 	DATA 	Keypress: f1
22.4298 	EXP 	text_q_stress: autoDraw = False
22.4298 	EXP 	text_2: autoDraw = True
23.9800 	DATA 	Keypress: f1
23.9888 	EXP 	text_2: autoDraw = False
23.9888 	EXP 	video: autoDraw = True
24.7480 	DATA 	Keypress: f1
26.2630 	EXP 	Sound exp_end_bip started
26.2612 	EXP 	video: autoDraw = False
26.2612 	EXP 	video: autoDraw = False
26.2612 	EXP 	text_q_stress: autoDraw = True
27.3162 	EXP 	Sound exp_end_bip stopped
27.3163 	EXP 	Sound exp_end_bip paused
27.3192 	DATA 	Keypress: f1
27.3237 	EXP 	text_q_stress: autoDraw = False
27.3237 	EXP 	text_3: autoDraw = True
27.8459 	EXP 	window1: mouseVisible = True
27.8477 	EXP 	Created unnamed ShapeStim = ShapeStim(__class__=<class 'psychopy.visual.shape.ShapeStim'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=False, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0, 0, 0]), fillColorSpace='rgb', fillRGB=array([0, 0, 0]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=3, name='unnamed ShapeStim', opacity=0.35, ori=0.0, pos=array([0., 0.]), size=array([1., 1.]), units='height', vertices=ndarray(...), win=Window(...), windingRule=None)
27.8514 	EXP 	window1: mouseVisible = True
27.8553 	EXP 	Created unnamed Circle = Circle(__class__=<class 'psychopy.visual.circle.Circle'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=2, name='unnamed Circle', opacity=1.0, ori=0.0, pos=array([0., 0.]), size=array([1., 1.]), units='height', vertices=ndarray(...), win=Window(...))
27.8555 	EXP 	window1: mouseVisible = True
27.8587 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.05882353, 0.61568627, 0.84313725]), fillColorSpace='rgb', fillRGB=array([0.05882353, 0.61568627, 0.84313725]), interpolate=True, lineColor=array([0., 0., 0.]), lineColorSpace='rgb', lineRGB=array([0, 0, 0]), lineWidth=1.5, name='unnamed Rect', opacity=0.1, ori=0.0, pos=array([0., 0.]), size=array([1.77777778, 0.5       ]), units='height', vertices=ndarray(...), win=Window(...))
27.8588 	EXP 	window1: mouseVisible = True
27.8639 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([ 0.  , -0.35]), rgb=UNKNOWN, text='Inhale', units='height', win=Window(...), wrapWidth=1)
27.8640 	EXP 	window1: mouseVisible = True
27.8649 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([0. , 0.4]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
27.8650 	EXP 	window1: mouseVisible = True
27.8660 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([-0.0462963, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8660 	EXP 	window1: mouseVisible = True
27.8670 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([-0.0212963, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8670 	EXP 	window1: mouseVisible = True
27.8679 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.0037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8680 	EXP 	window1: mouseVisible = True
27.8689 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.0287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8690 	EXP 	window1: mouseVisible = True
27.8699 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.0537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8699 	EXP 	window1: mouseVisible = True
27.8709 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.0787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8710 	EXP 	window1: mouseVisible = True
27.8722 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.1037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8723 	EXP 	window1: mouseVisible = True
27.8733 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.1287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8733 	EXP 	window1: mouseVisible = True
27.8743 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.1537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8743 	EXP 	window1: mouseVisible = True
27.8752 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.1787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8753 	EXP 	window1: mouseVisible = True
27.8762 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.2037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8762 	EXP 	window1: mouseVisible = True
27.8772 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.2287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8772 	EXP 	window1: mouseVisible = True
27.8781 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.2537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8782 	EXP 	window1: mouseVisible = True
27.8791 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.2787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8791 	EXP 	window1: mouseVisible = True
27.8801 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.3037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8802 	EXP 	window1: mouseVisible = True
27.8811 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.3287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8811 	EXP 	window1: mouseVisible = True
27.8820 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.3537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8821 	EXP 	window1: mouseVisible = True
27.8830 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.3787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8831 	EXP 	window1: mouseVisible = True
27.8842 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.4037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8843 	EXP 	window1: mouseVisible = True
27.8852 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.4287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8852 	EXP 	window1: mouseVisible = True
27.8861 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.4537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8862 	EXP 	window1: mouseVisible = True
27.8870 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.4787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8871 	EXP 	window1: mouseVisible = True
27.8884 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.5037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8885 	EXP 	window1: mouseVisible = True
27.8902 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.5287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8903 	EXP 	window1: mouseVisible = True
27.8912 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.5537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8913 	EXP 	window1: mouseVisible = True
27.8922 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.5787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8922 	EXP 	window1: mouseVisible = True
27.8934 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.6037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8935 	EXP 	window1: mouseVisible = True
27.8944 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.6287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8945 	EXP 	window1: mouseVisible = True
27.8954 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.6537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8954 	EXP 	window1: mouseVisible = True
27.8964 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([ 0.6787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
27.8964 	EXP 	window1: mouseVisible = True
27.8974 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([-0.59444444, -0.4       ]), size=array([0.03, 0.06]), units='height', vertices=ndarray(...), win=Window(...))
27.8974 	EXP 	window1: mouseVisible = True
27.8984 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([-0.55444444, -0.4       ]), size=array([0.03, 0.06]), units='height', vertices=ndarray(...), win=Window(...))
27.8984 	EXP 	window1: mouseVisible = True
27.8993 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.00392157, 0.00392157, 0.00392157]), fillColorSpace='rgb', fillRGB=array([0.00392157, 0.00392157, 0.00392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=0.3, ori=0.0, pos=array([-0.51444444, -0.4       ]), size=array([0.03, 0.06]), units='height', vertices=ndarray(...), win=Window(...))
27.8994 	EXP 	window1: mouseVisible = True
27.8995 	WARNING 	TextStim.alignHoriz is deprecated. Use alignText and anchorHoriz attributes instead
27.9045 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz='left', alignText='left', alignVert=method-wrapper(...), anchorHoriz='left', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([-0.89444444, -0.4       ]), rgb=UNKNOWN, text='Round:', units='height', win=Window(...), wrapWidth=1)
27.9047 	EXP 	window1: mouseVisible = True
27.9048 	WARNING 	TextStim.alignHoriz is deprecated. Use alignText and anchorHoriz attributes instead
27.9087 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz='left', alignText='left', alignVert=method-wrapper(...), anchorHoriz='left', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([-0.2462963, -0.4      ]), rgb=UNKNOWN, text='Breaths:', units='height', win=Window(...), wrapWidth=1)
27.9357 	DATA 	Keypress: f1
27.9400 	EXP 	text_3: autoDraw = False
27.9400 	EXP 	unnamed TextStim: height = 0.05
27.9400 	EXP 	unnamed TextStim: height = 0.05
27.9400 	EXP 	unnamed TextStim: height = 0.05
27.9400 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
27.9400 	EXP 	unnamed TextStim: height = 0.03
27.9400 	EXP 	unnamed TextStim: height = 0.03
27.9400 	EXP 	unnamed TextStim: height = 0.03
27.9400 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
27.9400 	EXP 	unnamed Rect: fillColor = 'lightblue'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'lightblue'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9400 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
27.9611 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
27.9611 	EXP 	unnamed Rect: fillColor = 'lightblue'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'lightblue'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9611 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
27.9798 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
27.9798 	EXP 	unnamed Rect: fillColor = 'lightblue'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'lightblue'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9798 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
27.9998 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
27.9998 	EXP 	unnamed Rect: fillColor = 'lightblue'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'lightblue'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
27.9998 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.0186 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.0186 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0186 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.0378 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.0378 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0378 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.0615 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.0615 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0615 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.0835 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.0835 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.0835 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.1054 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.1054 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1054 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.1273 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.1273 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1273 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.1478 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.1478 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1478 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.1712 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.1712 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1712 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.1951 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.1951 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.1951 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.2155 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.2155 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2155 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.2385 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.2385 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2385 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.2626 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.2626 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2626 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.2827 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.2827 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.2827 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.3034 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.3034 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3034 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.3270 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.3270 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3270 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.3480 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.3480 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3480 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.3683 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.3683 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3683 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.3887 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.3887 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.3887 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.4122 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.4122 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4122 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.4341 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.4341 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4341 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.4554 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.4554 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4554 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.4785 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.4785 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4785 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.4993 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.4993 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.4993 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.5212 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.5212 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5212 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.5457 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.5457 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5457 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.5685 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.5685 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5685 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.5965 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.5965 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.5965 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.6169 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.6169 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6169 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.6376 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.6376 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6376 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.6631 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.6631 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6631 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.6850 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.6850 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.6850 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.7122 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.7122 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7122 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.7349 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.7349 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7349 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.7590 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.7590 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7590 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.7829 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.7829 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.7829 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.8081 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.8081 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8081 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.8305 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.8305 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8305 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.8538 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.8538 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8538 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.8779 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.8779 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8779 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.8990 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.8990 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.8990 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.9236 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
28.9236 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9236 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.9551 	EXP 	unnamed TextStim: height = 0.03
28.9551 	EXP 	unnamed TextStim: height = 0.03
28.9551 	EXP 	unnamed TextStim: height = 0.03
28.9551 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
28.9551 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9551 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
28.9787 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
28.9787 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'lightblue'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
28.9787 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
29.0015 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.0015 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0015 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
29.0252 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.0252 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0252 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
29.0476 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.0476 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0476 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
29.0701 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.0701 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0701 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
29.0958 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.0958 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.0958 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
29.1169 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.1169 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1169 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
29.1385 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.1385 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1385 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
29.1625 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.1625 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1625 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
29.1879 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.1879 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.1879 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
29.2119 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.2119 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2119 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
29.2343 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.2343 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2343 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
29.2568 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.2568 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2568 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
29.2796 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.2796 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.2796 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
29.3016 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.3016 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3016 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
29.3276 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.3276 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3276 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
29.3480 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.3480 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3480 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
29.3718 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.3718 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3718 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
29.3957 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.3957 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.3957 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
29.4181 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.4181 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4181 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed TextStim: height = 0.05
29.4486 	EXP 	unnamed TextStim: height = 0.05
29.4486 	EXP 	unnamed TextStim: height = 0.05
29.4486 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.4486 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.4486 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4486 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.4707 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.4707 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4707 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.4945 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.4945 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.4945 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.5166 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.5166 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5166 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.5401 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.5401 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5401 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.5631 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.5631 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5631 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.5854 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.5854 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.5854 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.6088 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.6088 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6088 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.6293 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.6293 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6293 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.6508 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.6508 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6508 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.6732 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.6732 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6732 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.6951 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.6951 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.6951 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.7164 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.7164 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7164 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.7364 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.7364 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7364 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.7605 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.7605 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7605 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.7831 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.7831 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.7831 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.8070 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.8070 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8070 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.8273 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.8273 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8273 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.8468 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.8468 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8468 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.8684 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.8684 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8684 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.8886 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.8886 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.8886 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.9104 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
29.9104 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9104 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.9393 	EXP 	unnamed TextStim: height = 0.03
29.9393 	EXP 	unnamed TextStim: height = 0.03
29.9393 	EXP 	unnamed TextStim: height = 0.03
29.9393 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
29.9393 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9393 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.9623 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
29.9623 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9623 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
29.9844 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
29.9844 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'lightblue'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
29.9844 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.0068 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.0068 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0068 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.0281 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.0281 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0281 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.0500 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.0500 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0500 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.0714 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.0714 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0714 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.0949 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.0949 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.0949 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.1178 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.1178 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1178 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.1387 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.1387 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1387 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.1611 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.1611 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1611 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.1816 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.1816 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.1816 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.2073 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.2073 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2073 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.2295 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.2295 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2295 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.2497 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.2497 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2497 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.2716 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.2716 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2716 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.2925 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.2925 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.2925 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.3132 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.3132 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3132 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.3382 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.3382 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3382 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.3589 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.3589 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3589 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.3830 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.3830 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.3830 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.4046 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.4046 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4046 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.4259 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.4259 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4259 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.4479 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.4479 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4479 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.4694 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.4694 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4694 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.4925 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.4925 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.4925 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.5137 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.5137 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5137 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.5343 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.5343 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5343 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.5559 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.5559 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5559 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.5765 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.5765 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5765 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.5963 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.5963 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.5963 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.6161 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.6161 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6161 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.6358 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.6358 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6358 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.6573 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.6573 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6573 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.6769 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.6769 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6769 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.6964 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.6964 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.6964 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.7176 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.7176 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7176 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.7400 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.7400 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7400 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.7603 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.7603 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7603 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.7832 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.7832 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.7832 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.8168 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.8168 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8168 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.8412 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.8412 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8412 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.8644 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.8644 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8644 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.8843 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.8843 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.8843 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.9068 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.9068 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9068 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
30.9292 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
30.9292 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9292 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed TextStim: height = 0.05
30.9618 	EXP 	unnamed TextStim: height = 0.05
30.9618 	EXP 	unnamed TextStim: height = 0.05
30.9618 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
30.9618 	EXP 	unnamed TextStim: height = 0.03
30.9618 	EXP 	unnamed TextStim: height = 0.03
30.9618 	EXP 	unnamed TextStim: height = 0.03
30.9618 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
30.9618 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.9618 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9618 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
30.9826 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
30.9826 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.9826 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'lightblue'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
30.9826 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.0019 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.0019 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.0019 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0019 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.0246 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.0246 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.0246 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0246 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.0448 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.0448 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.0448 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0448 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.0640 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.0640 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.0640 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0640 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.0838 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.0838 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.0838 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.0838 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.1051 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.1051 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.1051 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1051 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.1265 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.1265 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.1265 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1265 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.1472 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.1472 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.1472 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1472 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.1687 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.1687 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.1687 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1687 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.1914 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.1914 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.1914 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.1914 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.2110 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.2110 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.2110 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2110 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.2308 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.2308 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.2308 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2308 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.2505 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.2505 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.2505 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2505 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.2719 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.2719 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.2719 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2719 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.2920 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.2920 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.2920 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.2920 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.3114 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.3114 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.3114 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3114 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.3320 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.3320 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.3320 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3320 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.3559 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.3559 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.3559 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3559 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.3770 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.3770 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.3770 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3770 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.3991 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.3991 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.3991 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.3991 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.4204 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.4204 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.4204 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4204 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.4414 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.4414 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.4414 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4414 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.4619 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.4619 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.4619 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4619 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.4841 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.4841 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.4841 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.4841 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.5056 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.5056 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.5056 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5056 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.5261 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.5261 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.5261 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5261 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.5473 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.5473 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.5473 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5473 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.5682 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.5682 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.5682 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5682 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.5909 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.5909 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.5909 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.5909 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.6140 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.6140 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.6140 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6140 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.6351 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.6351 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.6351 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6351 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.6585 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.6585 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.6585 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6585 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.6802 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.6802 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.6802 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.6802 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.7016 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.7016 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.7016 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7016 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.7267 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.7267 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.7267 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7267 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.7481 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.7481 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.7481 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7481 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.7699 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.7699 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.7699 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7699 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.7934 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.7934 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.7934 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.7934 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.8152 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.8152 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.8152 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8152 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.8361 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.8361 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.8361 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8361 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.8593 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.8593 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.8593 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8593 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.8815 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.8815 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.8815 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.8815 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.9023 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.9023 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.9023 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9023 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.9261 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
31.9261 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.9261 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9261 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.9523 	EXP 	unnamed TextStim: height = 0.03
31.9523 	EXP 	unnamed TextStim: height = 0.03
31.9523 	EXP 	unnamed TextStim: height = 0.03
31.9523 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
31.9523 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.9523 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9523 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.9762 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
31.9762 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.9762 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9762 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
31.9976 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
31.9976 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.9976 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'lightblue'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
31.9976 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
32.0184 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.0184 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.0184 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0184 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
32.0418 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.0418 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.0418 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0418 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
32.0642 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.0642 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.0642 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0642 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
32.0855 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.0855 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.0855 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.0855 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
32.1092 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.1092 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.1092 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1092 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
32.1317 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.1317 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.1317 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1317 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
32.1548 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.1548 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.1548 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1548 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
32.1771 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.1771 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.1771 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1771 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
32.1979 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.1979 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.1979 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.1979 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
32.2288 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.2288 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.2288 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2288 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
32.2543 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.2543 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.2543 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2543 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
32.2778 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.2778 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.2778 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.2778 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
32.3025 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.3025 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.3025 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3025 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
32.3293 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.3293 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.3293 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3293 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
32.3522 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.3522 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.3522 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3522 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
32.3762 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.3762 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.3762 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3762 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
32.3975 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.3975 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.3975 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.3975 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
32.4198 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.4198 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.4198 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4198 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed TextStim: height = 0.05
32.4448 	EXP 	unnamed TextStim: height = 0.05
32.4448 	EXP 	unnamed TextStim: height = 0.05
32.4448 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.4448 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.4448 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.4448 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4448 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.4673 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.4673 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.4673 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4673 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.4924 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.4924 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.4924 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.4924 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.5152 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.5152 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.5152 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5152 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.5376 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.5376 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.5376 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5376 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.5594 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.5594 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.5594 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5594 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.5811 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.5811 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.5811 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.5811 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.6047 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.6047 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.6047 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6047 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.6282 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.6282 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.6282 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6282 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.6490 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.6490 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.6490 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6490 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.6706 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.6706 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.6706 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6706 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.6952 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.6952 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.6952 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.6952 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.7166 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.7166 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.7166 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7166 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.7409 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.7409 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.7409 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7409 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.7642 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.7642 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.7642 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7642 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.7868 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.7868 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.7868 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.7868 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.8112 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.8112 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.8112 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8112 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.8358 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.8358 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.8358 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8358 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.8579 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.8579 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.8579 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8579 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.8777 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.8777 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.8777 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8777 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.8980 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.8980 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.8980 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.8980 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.9224 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
32.9224 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.9224 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9224 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.9472 	EXP 	unnamed TextStim: height = 0.03
32.9472 	EXP 	unnamed TextStim: height = 0.03
32.9472 	EXP 	unnamed TextStim: height = 0.03
32.9472 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
32.9472 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.9472 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9472 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.9706 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
32.9706 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.9706 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9706 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
32.9923 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
32.9923 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.9923 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'lightblue'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
32.9923 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
33.0135 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
33.0135 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.0135 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0135 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
33.0354 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
33.0354 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.0354 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0354 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
33.0579 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
33.0579 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.0579 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0579 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
33.0800 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
33.0800 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.0800 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.0800 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
33.1022 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
33.1022 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.1022 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1022 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
33.1246 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
33.1246 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.1246 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1246 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
33.1452 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
33.1452 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.1452 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1452 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
33.1656 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
33.1656 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.1656 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1656 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
33.1897 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
33.1897 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.1897 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.1897 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
33.2113 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
33.2113 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.2113 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2113 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
33.2358 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
33.2358 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.2358 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2358 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
33.2578 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
33.2578 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.2578 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2578 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
33.2819 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
33.2819 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.2819 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.2819 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
33.3074 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
33.3074 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.3074 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'lightblue'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3074 	EXP 	unnamed Rect: fillColor = 'gray'
33.3253 	DATA 	Keypress: escape
34.1655 	EXP 	window1: mouseVisible = True
