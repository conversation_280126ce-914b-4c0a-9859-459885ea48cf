# Paced Breathing Visualization for PsychoPy (height units)
# To be pasted into a code component in a PsychoPy routine
# Note: This code assumes 'win' (window object) is already defined in the PsychoPy environment
# IDE warnings about undefined variables can be ignored as they will be defined in the PsychoPy environment

from psychopy import visual, core, event
import numpy as np

# Check which breathing protocol to use (should be defined in experiment)
# breath_protocol should be either "paced_breathing", "fast_with_breath_hold", or "resting_state"
breath_protocol = "fast_with_breath_hold"  # Default value

# Breathing settings based on protocol
if breath_protocol == "paced_breathing":
    # Mode 1: Original paced breathing
    inhale_time = 4.5
    inhale_hold_time = 0.5
    exhale_time = 4.5
    exhale_hold_time = 0.5
    total_cycle_time = inhale_time + inhale_hold_time + exhale_time + exhale_hold_time

    # Check if breath_duration is defined in the experiment
    try:
        # Try to access breath_duration
        total_duration = breath_duration  # Use variable from experiment
    except NameError:
        # If not defined, set a default value
        total_duration = 360  # Default to 6 minutes
        print("Warning: breath_duration not defined, using default: 300 seconds")

elif breath_protocol == "fast_with_breath_hold":
    # Mode 2: Fast breathing with holds
    # Each round: 30 rapid breaths + exhale hold + inhale hold
    fast_inhale_time = 1.5
    fast_exhale_time = 1.5
    fast_cycle_time = fast_inhale_time + fast_exhale_time
    num_fast_breaths = 30
    fast_breathing_duration = num_fast_breaths * fast_cycle_time  # 90 seconds
    exhale_hold_duration = 15
    inhale_hold_duration = 15
    round_duration = fast_breathing_duration + exhale_hold_duration + inhale_hold_duration  # 120 seconds
    num_rounds = 3

    # Use breath_duration as hard cutoff, same as paced_breathing mode
    try:
        # Try to access breath_duration
        total_duration = breath_duration  # Use variable from experiment
    except NameError:
        # If not defined, set a default value
        total_duration = 360  # Default to 6 minutes
        print("Warning: breath_duration not defined, using default: 360 seconds")

    # Define these variables for the fast_with_breath_hold mode as well
    # to prevent "referenced before assignment" errors
    inhale_time = 4.5
    inhale_hold_time = 0.5
    exhale_time = 4.5
    exhale_hold_time = 0.5
    total_cycle_time = inhale_time + inhale_hold_time + exhale_time + exhale_hold_time

elif breath_protocol == "resting_state":
    # Mode 3: Resting state with crosshair
    # Use breath_duration as duration, same as other modes
    try:
        # Try to access breath_duration
        total_duration = breath_duration  # Use variable from experiment
    except NameError:
        # If not defined, set a default value
        total_duration = 360  # Default to 6 minutes
        print("Warning: breath_duration not defined, using default: 360 seconds")

    # Define these variables to prevent "referenced before assignment" errors
    inhale_time = 4.5
    inhale_hold_time = 0.5
    exhale_time = 4.5
    exhale_hold_time = 0.5
    total_cycle_time = inhale_time + inhale_hold_time + exhale_time + exhale_hold_time

else:
    # Default case - treat as resting state
    print(f"Warning: Unknown breath_protocol '{breath_protocol}', defaulting to resting_state")
    breath_protocol = "resting_state"

    # Use breath_duration as duration, same as other modes
    try:
        # Try to access breath_duration
        total_duration = breath_duration  # Use variable from experiment
    except NameError:
        # If not defined, set a default value
        total_duration = 360  # Default to 6 minutes
        print("Warning: breath_duration not defined, using default: 360 seconds")

    # Define these variables to prevent "referenced before assignment" errors
    inhale_time = 4.5
    inhale_hold_time = 0.5
    exhale_time = 4.5
    exhale_hold_time = 0.5
    total_cycle_time = inhale_time + inhale_hold_time + exhale_time + exhale_hold_time

# Get window dimensions in height units
# In height units, the height is always 1.0 (from -0.5 to 0.5)
# The width depends on the aspect ratio
win_height = 1.0
win_width = win.size[0] / win.size[1]  # This gives us the aspect ratio

# Normalize horizontal distance to window width
# Leave some margin on both sides (10% of width)
margin = 0.1 * win_width
usable_width = win_width - 2 * margin
x_start = -win_width/2 + margin
x_end = win_width/2 - margin

# Vertical parameters (in height units)
y_min = -0.25  # Bottom position for exhale
y_max = 0.25   # Top position for inhale
y_range = y_max - y_min

# Create visual components based on protocol
if breath_protocol == "resting_state":
    # For resting state, create only the crosshair (matching the original resting state routine)
    cross = visual.TextStim(
        win=win,
        name='cross',
        text='+',
        font='Open Sans',
        pos=(0, 0),
        height=0.05,
        wrapWidth=None,
        ori=0.0,
        color='white',
        colorSpace='rgb',
        opacity=None,
        languageStyle='LTR',
        depth=-1.0
    )

    # Set dummy variables for other components to prevent errors
    path_line = None
    ball = None
    background = None
    vertices = []

else:
    # Create the breathing path line segments for paced_breathing and fast_with_breath_hold
    # Calculate segment widths based on their duration
    if breath_protocol == "paced_breathing":
        # Original path vertices for paced breathing
        inhale_width = (inhale_time / total_cycle_time) * usable_width
        inhale_hold_width = (inhale_hold_time / total_cycle_time) * usable_width
        exhale_width = (exhale_time / total_cycle_time) * usable_width
        exhale_hold_width = (exhale_hold_time / total_cycle_time) * usable_width

        vertices = [
            [x_start, y_min],                                  # Start point
            [x_start + inhale_width, y_max],                   # End of inhale
            [x_start + inhale_width + inhale_hold_width, y_max],  # End of inhale hold
            [x_start + inhale_width + inhale_hold_width + exhale_width, y_min],  # End of exhale
            [x_end, y_min]                                     # End of exhale hold
        ]
    else:  # fast_with_breath_hold
        # For fast_with_breath_hold, we need two different path visualizations:
        # 1. Fast breathing path (first 90 seconds of each round)
        # 2. Hold phases path (last 30 seconds of each round)

        # Calculate the current phase based on time
        current_time = 0  # This will be updated in the animation loop
        round_num = int(current_time / round_duration)
        round_time = current_time - (round_num * round_duration)

        # Fast breathing path parameters
        fast_inhale_width = (fast_inhale_time / fast_cycle_time) * usable_width

        if round_time < fast_breathing_duration:
            # Fast breathing path - just up and down, no holds
            vertices = [
                [x_start, y_min],                      # Start point
                [x_start + fast_inhale_width, y_max],  # End of inhale
                [x_end, y_min]                         # End of exhale
            ]
        else:
            # Hold phases path - spanning the entire usable width
            # Define the segments of the hold cycle
            hold_cycle_time = exhale_hold_duration + inhale_hold_duration  # 30 seconds total
            exhale_hold_segment = 14.5  # seconds
            inhale_transition = 0.5     # seconds
            inhale_hold_segment = 14.5  # seconds
            exhale_transition = 0.5     # seconds

            # Calculate segment widths based on the entire usable width
            exhale_hold_width = (exhale_hold_segment / hold_cycle_time) * usable_width
            inhale_trans_width = (inhale_transition / hold_cycle_time) * usable_width
            inhale_hold_width = (inhale_hold_segment / hold_cycle_time) * usable_width
            exhale_trans_width = (exhale_transition / hold_cycle_time) * usable_width

            vertices = [
                [x_start, y_min],                                                # Start of exhale hold
                [x_start + exhale_hold_width, y_min],                            # End of exhale hold
                [x_start + exhale_hold_width + inhale_trans_width, y_max],       # End of inhale transition
                [x_start + exhale_hold_width + inhale_trans_width + inhale_hold_width, y_max],  # End of inhale hold
                [x_end, y_min]                                                   # End of exhale transition
            ]

    # Create the path line
    path_line = visual.ShapeStim(
        win=win,
        vertices=vertices,
        closeShape=False,
        lineWidth=3,
        lineColor='white',
        opacity=0.7
    )

    # Create the ball that will travel along the path
    ball = visual.Circle(
        win=win,
        radius=0.015,  # Adjusted for height units
        fillColor='lightblue',
        lineColor='white',
        lineWidth=2
    )

    # Create background shading rectangle
    background = visual.Rect(
        win=win,
        width=win_width,
        height=y_range,
        fillColor='skyblue',
        opacity=0.2,
        pos=[0, y_min + y_range/2]  # Start at the bottom
    )

    # Set dummy variable for crosshair
    cross = None

# Function to calculate ball position at a given time
def get_ball_position(current_time):
    if breath_protocol == "resting_state":
        # For resting state, no ball movement needed
        return 0, 0, "Resting State"
    elif breath_protocol == "paced_breathing":
        # Define breathing parameters for paced breathing
        inhale_time = 4.5
        inhale_hold_time = 0.5
        exhale_time = 4.5
        exhale_hold_time = 0.5
        total_cycle_time = inhale_time + inhale_hold_time + exhale_time + exhale_hold_time

        # Original ball position calculation
        cycle_time = current_time % total_cycle_time

        # Calculate segment widths here to ensure they're defined
        inhale_width = (inhale_time / total_cycle_time) * usable_width
        inhale_hold_width = (inhale_hold_time / total_cycle_time) * usable_width
        exhale_width = (exhale_time / total_cycle_time) * usable_width
        exhale_hold_width = (exhale_hold_time / total_cycle_time) * usable_width

        # Determine which segment we're in
        if cycle_time < inhale_time:
            # Inhale segment - moving up
            progress = cycle_time / inhale_time
            x = x_start + progress * inhale_width
            y = y_min + progress * y_range
            phase = "Inhale"

        elif cycle_time < inhale_time + inhale_hold_time:
            # Inhale hold segment - staying at the top
            progress = (cycle_time - inhale_time) / inhale_hold_time
            x = x_start + inhale_width + progress * inhale_hold_width
            y = y_max
            phase = "Hold"

        elif cycle_time < inhale_time + inhale_hold_time + exhale_time:
            # Exhale segment - moving down
            progress = (cycle_time - inhale_time - inhale_hold_time) / exhale_time
            x = x_start + inhale_width + inhale_hold_width + progress * exhale_width
            y = y_max - progress * y_range
            phase = "Exhale"

        else:
            # Exhale hold segment - staying at the bottom
            progress = (cycle_time - inhale_time - inhale_hold_time - exhale_time) / exhale_hold_time
            x = x_start + inhale_width + inhale_hold_width + exhale_width + progress * exhale_hold_width
            y = y_min
            phase = "Hold"

    else:  # fast_with_breath_hold
        # Define breathing parameters for fast breathing with holds
        fast_inhale_time = 1.5
        fast_exhale_time = 1.5
        fast_cycle_time = fast_inhale_time + fast_exhale_time
        num_fast_breaths = 30
        fast_breathing_duration = num_fast_breaths * fast_cycle_time  # 90 seconds
        exhale_hold_duration = 15
        inhale_hold_duration = 15
        round_duration = fast_breathing_duration + exhale_hold_duration + inhale_hold_duration  # 120 seconds
        num_rounds = 3

        # Calculate which round we're in
        round_num = int(current_time / round_duration)
        if round_num >= num_rounds:
            round_num = num_rounds - 1

        # Calculate time within the current round
        round_time = current_time - (round_num * round_duration)

        # Determine which phase we're in
        if round_time < fast_breathing_duration:
            # Fast breathing phase
            cycle_num = int(round_time / fast_cycle_time)
            cycle_time = round_time % fast_cycle_time

            # Fast breathing path parameters - ensure consistent with path creation
            fast_inhale_width = (fast_inhale_time / fast_cycle_time) * usable_width

            if cycle_time < fast_inhale_time:
                # Inhale segment - moving up
                progress = cycle_time / fast_inhale_time
                x = x_start + progress * fast_inhale_width
                y = y_min + progress * y_range
                phase = "Inhale"
            else:
                # Exhale segment - moving down
                progress = (cycle_time - fast_inhale_time) / fast_exhale_time
                # Use the remaining width to ensure ball reaches end point
                remaining_width = usable_width - fast_inhale_width
                x = x_start + fast_inhale_width + progress * remaining_width
                y = y_max - progress * y_range
                phase = "Exhale"

            # Add breath counter to phase
            phase += f" ({cycle_num+1}/{num_fast_breaths})"

        else:
            # Hold phases (combined exhale hold and inhale hold)
            hold_time = round_time - fast_breathing_duration

            # Calculate position in the hold cycle
            hold_cycle_time = exhale_hold_duration + inhale_hold_duration  # 30 seconds total

            # Define the segments of the hold cycle - ensure consistent with path creation
            exhale_hold_segment = 14.5  # seconds
            inhale_transition = 0.5     # seconds
            inhale_hold_segment = 14.5  # seconds
            exhale_transition = 0.5     # seconds

            # Calculate segment widths using the entire usable width - ensure consistent with path creation
            exhale_hold_width = (exhale_hold_segment / hold_cycle_time) * usable_width
            inhale_trans_width = (inhale_transition / hold_cycle_time) * usable_width
            inhale_hold_width = (inhale_hold_segment / hold_cycle_time) * usable_width
            exhale_trans_width = (exhale_transition / hold_cycle_time) * usable_width

            # Determine which segment of the hold cycle we're in
            if hold_time < exhale_hold_segment:
                # Exhale hold (bottom)
                progress = hold_time / exhale_hold_segment
                x = x_start + progress * exhale_hold_width
                y = y_min
                phase = f"Exhaled Hold ({int(exhale_hold_segment - hold_time)}s)"

            elif hold_time < exhale_hold_segment + inhale_transition:
                # Quick inhale transition (moving up)
                progress = (hold_time - exhale_hold_segment) / inhale_transition
                x = x_start + exhale_hold_width + progress * inhale_trans_width
                y = y_min + progress * y_range
                phase = "Inhale"

            elif hold_time < exhale_hold_segment + inhale_transition + inhale_hold_segment:
                # Inhale hold (top)
                progress = (hold_time - exhale_hold_segment - inhale_transition) / inhale_hold_segment
                x = x_start + exhale_hold_width + inhale_trans_width + progress * inhale_hold_width
                y = y_max
                phase = f"Inhaled Hold ({int(inhale_hold_segment - (progress * inhale_hold_segment))}s)"

            else:
                # Quick exhale transition (moving down)
                progress = (hold_time - exhale_hold_segment - inhale_transition - inhale_hold_segment) / exhale_transition
                x = x_start + exhale_hold_width + inhale_trans_width + inhale_hold_width + progress * exhale_trans_width
                y = y_max - progress * y_range
                phase = "Exhale"

        # Add round information
        phase = f"Round {round_num+1}/{num_rounds}: " + phase

    return x, y, phase

# Create text display for the breathing phase
phase_text = visual.TextStim(
    win=win,
    text="Inhale",
    pos=[0, -0.35],
    color='white',
    height=0.05  # Adjusted for height units
)

# Create a timer display
timer_text = visual.TextStim(
    win=win,
    text="",
    pos=[0, 0.4],
    color='white',
    height=0.03  # Adjusted for height units
)

# Create breath counter rectangles for fast_with_breath_hold mode
breath_rects = []
round_rects = []
if breath_protocol == "fast_with_breath_hold":
    # Calculate rectangle dimensions and positions
    rect_height = 0.05  # Height of each rectangle
    rect_width = 0.02   # Width of each rectangle
    rect_spacing = 0.005  # Space between rectangles

    # Position in right 2/3 of screen
    screen_left = -win_width/2
    screen_right = win_width/2
    left_third_end = screen_left + win_width/3

    # Calculate total width needed for all breath rectangles
    total_rect_width = num_fast_breaths * (rect_width + rect_spacing) - rect_spacing

    # Center the rectangles in the right 2/3 of the screen
    rect_area_start = left_third_end
    rect_area_end = screen_right
    rect_area_width = rect_area_end - rect_area_start

    # Start position for the first rectangle - leave space for the "Breaths:" text
    rect_start_x = left_third_end + 0.25  # Adjusted to leave space for text

    # Create all breath rectangles - initially gray with white outline
    for i in range(num_fast_breaths):
        rect = visual.Rect(
            win=win,
            width=rect_width,
            height=rect_height,
            fillColor='gray',  # Start with gray fill
            lineColor='white',
            lineWidth=1,
            opacity=0.3,  # Make the gray semi-transparent
            pos=[rect_start_x + i * (rect_width + rect_spacing), -0.4]
        )
        breath_rects.append(rect)

    # Create round counter rectangles
    # Calculate dimensions for round rectangles (slightly larger)
    round_rect_height = 0.06
    round_rect_width = 0.03
    round_rect_spacing = 0.01

    # Position round rectangles in the left 1/4 of the screen
    left_quarter_end = screen_left + win_width/4
    round_rect_start_x = left_quarter_end - 0.15  # Positioned to the left of the round text

    # Create round rectangles - initially gray with white outline
    for i in range(num_rounds):
        rect = visual.Rect(
            win=win,
            width=round_rect_width,
            height=round_rect_height,
            fillColor='gray',  # Start with gray fill
            lineColor='white',
            lineWidth=1,
            opacity=0.3,  # Make the gray semi-transparent
            pos=[round_rect_start_x + i * (round_rect_width + round_rect_spacing), -0.4]
        )
        round_rects.append(rect)

# Create round counter text for fast_with_breath_hold mode
round_text = None
breaths_remaining_text = None
if breath_protocol == "fast_with_breath_hold":
    # Round counter text - positioned at the right side of the left 1/4
    left_quarter_end = screen_left + win_width/4
    round_text = visual.TextStim(
        win=win,
        text="Round:",
        pos=[left_quarter_end - 0.35, -0.4],  # Moved further left so text ends ~2 chars before previous start
        color='white',
        height=0.05,  # Adjusted for height units
        alignHoriz='left'  # Align text to the left for consistent positioning
    )

    # Breaths remaining text - positioned before the rectangles
    breaths_remaining_text = visual.TextStim(
        win=win,
        text="Breaths:",
        pos=[left_third_end + 0.05, -0.4],  # Left side of right 2/3, moved left
        color='white',
        height=0.05,  # Adjusted for height units
        alignHoriz='left'
    )

# Main animation loop
timer = core.Clock()
continue_routine = True

try:
    while continue_routine:
        # Get current time
        t = timer.getTime()

        # Check if we've reached the total duration
        if t >= total_duration:
            continue_routine = False
            break

        # For fast_with_breath_hold mode, update the path visualization based on the current phase
        if breath_protocol == "fast_with_breath_hold":
            # Define these parameters inside the loop to ensure they're available
            fast_inhale_time = 1.5
            fast_exhale_time = 1.5
            fast_cycle_time = fast_inhale_time + fast_exhale_time
            num_fast_breaths = 30
            fast_breathing_duration = num_fast_breaths * fast_cycle_time  # 90 seconds
            exhale_hold_duration = 15
            inhale_hold_duration = 15
            round_duration = fast_breathing_duration + exhale_hold_duration + inhale_hold_duration  # 120 seconds
            num_rounds = 3

            round_num = int(t / round_duration)
            if round_num >= num_rounds:
                round_num = num_rounds - 1

            round_time = t - (round_num * round_duration)

            # Check if we need to switch path visualization
            is_hold_phase = round_time >= fast_breathing_duration

            # Check if path_line exists and has vertices before accessing them
            if hasattr(path_line, 'vertices') and len(path_line.vertices) > 0:
                # Fast breathing path parameters
                fast_inhale_width = (fast_inhale_time / fast_cycle_time) * usable_width

                # Calculate hold phase parameters - using the entire usable width
                hold_cycle_time = exhale_hold_duration + inhale_hold_duration
                exhale_hold_segment = 14.5
                inhale_transition = 0.5
                inhale_hold_segment = 14.5
                exhale_transition = 0.5

                # Calculate segment widths using the entire usable width
                exhale_hold_width = (exhale_hold_segment / hold_cycle_time) * usable_width
                inhale_trans_width = (inhale_transition / hold_cycle_time) * usable_width
                inhale_hold_width = (inhale_hold_segment / hold_cycle_time) * usable_width
                exhale_trans_width = (exhale_transition / hold_cycle_time) * usable_width

                # Check the current phase and update path if needed
                if is_hold_phase and len(path_line.vertices) == 3:
                    # Switch to hold phase path - spanning the entire usable width
                    path_line.vertices = [
                        [x_start, y_min],                                                # Start of exhale hold
                        [x_start + exhale_hold_width, y_min],                            # End of exhale hold
                        [x_start + exhale_hold_width + inhale_trans_width, y_max],       # End of inhale transition
                        [x_start + exhale_hold_width + inhale_trans_width + inhale_hold_width, y_max],  # End of inhale hold
                        [x_end, y_min]                                                   # End of exhale transition
                    ]

                elif not is_hold_phase and len(path_line.vertices) == 5:
                    # Switch back to fast breathing path
                    path_line.vertices = [
                        [x_start, y_min],
                        [x_start + fast_inhale_width, y_max],
                        [x_end, y_min]
                    ]

        # Update timer text - show remaining time
        time_left = int(total_duration - t)
        timer_text.text = f"Time remaining: {time_left}s"

        # Draw stimuli based on protocol
        if breath_protocol == "resting_state":
            # For resting state, only draw the crosshair and timer
            if cross:
                cross.draw()
            timer_text.draw()

        else:
            # For breathing protocols, draw the breathing visualization
            # Calculate current ball position
            x, y, phase = get_ball_position(t)

            # Update ball position
            if ball:
                ball.pos = [x, y]

            # Update background height and position
            if background:
                background.height = y - y_min
                background.pos = [0, y_min + (y - y_min)/2]

            # Update phase text
            phase_text.text = phase

            # Draw breathing visualization components
            if background:
                background.draw()
            if path_line:
                path_line.draw()
            if ball:
                ball.draw()
            timer_text.draw()  # Keep timer text

            # Draw breath counter rectangles and round counter for fast_with_breath_hold mode
            if breath_protocol == "fast_with_breath_hold":
                # Draw round text (static "Round:" label)
                if round_text:
                    round_text.draw()

                # Draw breaths remaining text
                if breaths_remaining_text:
                    breaths_remaining_text.draw()

                # Update and draw breath rectangles
                if breath_rects:
                    # Calculate how many breaths have been completed in the current round
                    if round_time < fast_breathing_duration:
                        completed_breaths = int(round_time / fast_cycle_time)
                    else:
                        completed_breaths = num_fast_breaths  # All breaths completed

                    # Draw all breath rectangles - advance by 1 so current breath is already filled
                    for i, rect in enumerate(breath_rects):
                        # For the current breath cycle and completed ones
                        if i <= completed_breaths:  # Changed from < to <= to include current breath
                            # Fill current and completed breath rectangles with lightblue
                            rect.fillColor = 'lightblue'
                            rect.opacity = 1.0
                        else:
                            # Keep future breath rectangles gray and semi-transparent
                            rect.fillColor = 'gray'
                            rect.opacity = 0.3
                        rect.draw()

                # Update and draw round rectangles
                if round_rects:
                    # Draw all round rectangles
                    for i, rect in enumerate(round_rects):
                        if i <= round_num:  # Fill current and previous rounds
                            # Fill completed round rectangles with same blue as breath counter
                            rect.fillColor = 'lightblue'
                            rect.opacity = 1.0
                        else:
                            # Keep future round rectangles gray and semi-transparent
                            rect.fillColor = 'gray'
                            rect.opacity = 0.3
                        rect.draw()
            else:
                # For paced_breathing mode, still show the phase text
                phase_text.draw()

        # Check for quit (the Esc key)
        keys = event.getKeys(keyList=["escape"])
        if 'escape' in keys:
            continue_routine = False
            core.quit()  # This will exit the experiment

        # Refresh the screen
        win.flip()
except Exception as e:
    print(f"Error in animation loop: {e}")
    # Handle the error appropriately for your experiment
finally:
    # Clean up
    import gc
    gc.collect()  # Force garbage collection

# Just clear the screen and wait
try:
    win.flip()
    core.wait(2.0)
except Exception as e:
    print(f"Error in final screen clear: {e}")
