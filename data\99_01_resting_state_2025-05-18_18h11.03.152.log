6.9370 	WARNING 	User requested fullscreen with size [1536  864], but screen is actually [1920, 1080]. Using actual size
7.5758 	EXP 	Created window1 = Window(allowGUI=True, allowStencil=True, autoLog=True, backendConf=UNKNOWN, backgroundFit=<method-wrapper '__getattribute__' of attributeSetter object at 0x000002434B587C70>, backgroundImage=<method-wrapper '__getattribute__' of attributeSetter object at 0x000002434B587C10>, bitsMode=UNKNOWN, blendMode='avg', bpc=(8, 8, 8), color=array([0, 0, 0]), colorSpace='rgb', depthBits=8, fullscr=<method-wrapper '__getattribute__' of attributeSetter object at 0x000002434B587A60>, gamma=None, gammaErrorPolicy='raise', lms=UNKNOWN, monitor=<psychopy.monitors.calibTools.Monitor object at 0x0000024351C63760>, multiSample=False, name='window1', numSamples=2, pos=(0, 0), screen=0, size=array([1920, 1080]), stencilBits=8, stereo=False, title='PsychoPy', units='height', useFBO=True, useRetina=False, viewOri=0.0, viewPos=None, viewScale=None, waitBlanking=True, winType='pyglet')
7.5760 	EXP 	window1: mouseVisible = True
7.5761 	EXP 	window1: backgroundImage = ''
7.5761 	EXP 	window1: backgroundFit = 'none'
7.5792 	EXP 	window1: Attempting to measure frame rate of screen (0) ...
7.5793 	EXP 	window1: recordFrameIntervals = False
7.7418 	EXP 	window1: recordFrameIntervals = True
7.9249 	EXP 	Screen (0) actual frame rate measured at 60.13Hz
7.9250 	EXP 	window1: recordFrameIntervals = False
7.9253 	EXP 	window1: mouseVisible = False
10.6571 	EXP 	01_resting_state: status = STARTED
11.0625 	EXP 	window1: mouseVisible = True
11.1491 	EXP 	Created text = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.1493 	EXP 	window1: mouseVisible = True
11.1555 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
11.1556 	EXP 	window1: mouseVisible = True
11.1696 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.1716 	EXP 	Sound exp_end_bip set volume 1.000
11.1718 	EXP 	window1: mouseVisible = True
11.1821 	EXP 	Created text_2 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_2', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.1872 	EXP 	window1: mouseVisible = True
11.2138 	EXP 	window1: mouseVisible = True
11.2213 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.2233 	EXP 	Sound exp_end_bip set volume 1.000
11.2235 	EXP 	window1: mouseVisible = True
11.2306 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.2308 	EXP 	window1: mouseVisible = True
11.2376 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.2396 	EXP 	Sound exp_end_bip set volume 1.000
11.2398 	EXP 	window1: mouseVisible = True
11.2463 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.2480 	EXP 	Sound exp_end_bip set volume 1.000
11.2482 	EXP 	window1: mouseVisible = True
11.2526 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.2528 	EXP 	window1: mouseVisible = True
11.2586 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.2604 	EXP 	Sound exp_end_bip set volume 1.000
11.2606 	EXP 	window1: mouseVisible = True
11.2742 	EXP 	Created stroop_instr = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='stroop_instr', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.2743 	EXP 	window1: mouseVisible = True
11.2836 	EXP 	Created instrText_4 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-2.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_4', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.2837 	EXP 	window1: mouseVisible = True
11.2850 	EXP 	Created stim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.15, italic=False, languageStyle='LTR', name='stim', opacity=1.0, ori=0, pos=array([0., 0.]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
11.2852 	EXP 	window1: mouseVisible = True
11.2853 	EXP 	window1: mouseVisible = True
11.2857 	EXP 	window1: mouseVisible = True
11.2860 	EXP 	window1: mouseVisible = True
11.2899 	EXP 	window1: mouseVisible = True
11.2900 	EXP 	window1: mouseVisible = True
11.2905 	EXP 	window1: mouseVisible = True
11.2908 	EXP 	window1: mouseVisible = True
11.3093 	EXP 	window1: mouseVisible = True
11.3133 	EXP 	Created instrText_2 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-4.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_2', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.3134 	EXP 	window1: mouseVisible = True
11.3136 	EXP 	window1: mouseVisible = True
11.3139 	EXP 	window1: mouseVisible = True
11.3143 	EXP 	window1: mouseVisible = True
11.3175 	EXP 	window1: mouseVisible = True
11.3177 	EXP 	window1: mouseVisible = True
11.3180 	EXP 	window1: mouseVisible = True
11.3184 	EXP 	window1: mouseVisible = True
11.3218 	EXP 	window1: mouseVisible = True
11.3219 	EXP 	window1: mouseVisible = True
11.3223 	EXP 	window1: mouseVisible = True
11.3226 	EXP 	window1: mouseVisible = True
11.3257 	EXP 	window1: mouseVisible = True
11.3258 	EXP 	window1: mouseVisible = True
11.3262 	EXP 	window1: mouseVisible = True
11.3265 	EXP 	window1: mouseVisible = True
11.3297 	EXP 	window1: mouseVisible = True
11.3335 	EXP 	Created instrText_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-3.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_3', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.3336 	EXP 	window1: mouseVisible = True
11.3459 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.3475 	EXP 	Sound exp_end_bip set volume 1.000
11.3477 	EXP 	window1: mouseVisible = True
11.3536 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.3538 	EXP 	window1: mouseVisible = True
11.3596 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.3612 	EXP 	Sound exp_end_bip set volume 1.000
11.3613 	EXP 	window1: mouseVisible = True
11.3666 	EXP 	Created text = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.3670 	EXP 	window1: mouseVisible = True
11.3712 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
11.3714 	EXP 	window1: mouseVisible = True
11.3812 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.3828 	EXP 	Sound exp_end_bip set volume 1.000
11.3830 	EXP 	window1: mouseVisible = True
11.3886 	EXP 	Created EndMessage = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='EndMessage', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='Thank you!', units='height', win=Window(...), wrapWidth=1)
0.0042 	EXP 	video: autoLog = True
0.0042 	EXP 	trial_counter: autoLog = True
0.0042 	EXP 	fbtxt: autoLog = True
0.0042 	EXP 	trial_counter_2: autoLog = True
0.0151 	EXP 	text: autoDraw = True
0.7184 	DATA 	Keypress: f1
0.7315 	EXP 	text: autoDraw = False
0.7315 	EXP 	cross: autoDraw = True
11.7405 	EXP 	Sound exp_end_bip started
11.7396 	EXP 	cross: autoDraw = False
11.7396 	EXP 	cross: autoDraw = False
11.7396 	EXP 	text_q_stress: autoDraw = True
12.5200 	EXP 	Sound exp_end_bip stopped
12.5200 	EXP 	Sound exp_end_bip paused
12.5229 	DATA 	Keypress: f1
12.5269 	EXP 	text_q_stress: autoDraw = False
12.5269 	EXP 	text_2: autoDraw = True
13.9904 	DATA 	Keypress: f1
14.1290 	EXP 	text_2: autoDraw = False
14.1290 	EXP 	video: autoDraw = True
16.3928 	EXP 	Sound exp_end_bip reached end of file
16.4041 	EXP 	Sound exp_end_bip started
16.3991 	EXP 	video: autoDraw = False
16.3991 	EXP 	video: autoDraw = False
16.3991 	EXP 	text_q_stress: autoDraw = True
17.0796 	EXP 	Sound exp_end_bip stopped
17.0797 	EXP 	Sound exp_end_bip paused
17.0839 	DATA 	Keypress: f1
17.0885 	EXP 	text_q_stress: autoDraw = False
17.0885 	EXP 	text_3: autoDraw = True
17.7394 	EXP 	window1: mouseVisible = True
17.7433 	EXP 	Created unnamed ShapeStim = ShapeStim(__class__=<class 'psychopy.visual.shape.ShapeStim'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=False, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0, 0, 0]), fillColorSpace='rgb', fillRGB=array([0, 0, 0]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=3, name='unnamed ShapeStim', opacity=0.35, ori=0.0, pos=array([0., 0.]), size=array([1., 1.]), units='height', vertices=ndarray(...), win=Window(...), windingRule=None)
17.7504 	EXP 	window1: mouseVisible = True
17.7558 	EXP 	Created unnamed Circle = Circle(__class__=<class 'psychopy.visual.circle.Circle'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=2, name='unnamed Circle', opacity=1.0, ori=0.0, pos=array([0., 0.]), size=array([1., 1.]), units='height', vertices=ndarray(...), win=Window(...))
17.7559 	EXP 	window1: mouseVisible = True
17.7572 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.05882353, 0.61568627, 0.84313725]), fillColorSpace='rgb', fillRGB=array([0.05882353, 0.61568627, 0.84313725]), interpolate=True, lineColor=array([0., 0., 0.]), lineColorSpace='rgb', lineRGB=array([0, 0, 0]), lineWidth=1.5, name='unnamed Rect', opacity=0.1, ori=0.0, pos=array([0., 0.]), size=array([1.77777778, 0.5       ]), units='height', vertices=ndarray(...), win=Window(...))
17.7573 	EXP 	window1: mouseVisible = True
17.7631 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([ 0.  , -0.35]), rgb=UNKNOWN, text='Inhale', units='height', win=Window(...), wrapWidth=1)
17.7633 	EXP 	window1: mouseVisible = True
17.7644 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([0. , 0.4]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
17.7729 	DATA 	Keypress: f1
17.7769 	EXP 	text_3: autoDraw = False
17.7769 	EXP 	unnamed TextStim: text = 'Inhale'
17.7769 	EXP 	unnamed TextStim: height = 0.03
17.7769 	EXP 	unnamed TextStim: height = 0.03
17.7769 	EXP 	unnamed TextStim: height = 0.03
17.7769 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
17.7849 	EXP 	unnamed TextStim: text = 'Inhale'
17.7849 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
17.8008 	EXP 	unnamed TextStim: text = 'Inhale'
17.8008 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
17.8178 	EXP 	unnamed TextStim: text = 'Inhale'
17.8178 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
17.8344 	EXP 	unnamed TextStim: text = 'Inhale'
17.8344 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
17.8510 	EXP 	unnamed TextStim: text = 'Inhale'
17.8510 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
17.8676 	EXP 	unnamed TextStim: text = 'Inhale'
17.8676 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
17.8846 	EXP 	unnamed TextStim: text = 'Inhale'
17.8846 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
17.9010 	EXP 	unnamed TextStim: text = 'Inhale'
17.9010 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
17.9183 	EXP 	unnamed TextStim: text = 'Inhale'
17.9183 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
17.9347 	EXP 	unnamed TextStim: text = 'Inhale'
17.9347 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
17.9506 	EXP 	unnamed TextStim: text = 'Inhale'
17.9506 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
17.9672 	EXP 	unnamed TextStim: text = 'Inhale'
17.9672 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
17.9846 	EXP 	unnamed TextStim: text = 'Inhale'
17.9846 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.0005 	EXP 	unnamed TextStim: text = 'Inhale'
18.0005 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.0170 	EXP 	unnamed TextStim: text = 'Inhale'
18.0170 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.0340 	EXP 	unnamed TextStim: text = 'Inhale'
18.0340 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.0507 	EXP 	unnamed TextStim: text = 'Inhale'
18.0507 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.0677 	EXP 	unnamed TextStim: text = 'Inhale'
18.0677 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.0848 	EXP 	unnamed TextStim: text = 'Inhale'
18.0848 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.1010 	EXP 	unnamed TextStim: text = 'Inhale'
18.1010 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.1176 	EXP 	unnamed TextStim: text = 'Inhale'
18.1176 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.1354 	EXP 	unnamed TextStim: text = 'Inhale'
18.1354 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.1507 	EXP 	unnamed TextStim: text = 'Inhale'
18.1507 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.1677 	EXP 	unnamed TextStim: text = 'Inhale'
18.1677 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.1844 	EXP 	unnamed TextStim: text = 'Inhale'
18.1844 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.2010 	EXP 	unnamed TextStim: text = 'Inhale'
18.2010 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.2176 	EXP 	unnamed TextStim: text = 'Inhale'
18.2176 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.2346 	EXP 	unnamed TextStim: text = 'Inhale'
18.2346 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.2507 	EXP 	unnamed TextStim: text = 'Inhale'
18.2507 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.2676 	EXP 	unnamed TextStim: text = 'Inhale'
18.2676 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.2841 	EXP 	unnamed TextStim: text = 'Inhale'
18.2841 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.3005 	EXP 	unnamed TextStim: text = 'Inhale'
18.3005 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.3172 	EXP 	unnamed TextStim: text = 'Inhale'
18.3172 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.3336 	EXP 	unnamed TextStim: text = 'Inhale'
18.3336 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.3508 	EXP 	unnamed TextStim: text = 'Inhale'
18.3508 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.3672 	EXP 	unnamed TextStim: text = 'Inhale'
18.3672 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.3841 	EXP 	unnamed TextStim: text = 'Inhale'
18.3841 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.4007 	EXP 	unnamed TextStim: text = 'Inhale'
18.4007 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.4176 	EXP 	unnamed TextStim: text = 'Inhale'
18.4176 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.4343 	EXP 	unnamed TextStim: text = 'Inhale'
18.4343 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.4507 	EXP 	unnamed TextStim: text = 'Inhale'
18.4507 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.4672 	EXP 	unnamed TextStim: text = 'Inhale'
18.4672 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.4841 	EXP 	unnamed TextStim: text = 'Inhale'
18.4841 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.5006 	EXP 	unnamed TextStim: text = 'Inhale'
18.5006 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.5185 	EXP 	unnamed TextStim: text = 'Inhale'
18.5185 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.5339 	EXP 	unnamed TextStim: text = 'Inhale'
18.5339 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.5498 	EXP 	unnamed TextStim: text = 'Inhale'
18.5498 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.5668 	EXP 	unnamed TextStim: text = 'Inhale'
18.5668 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.5833 	EXP 	unnamed TextStim: text = 'Inhale'
18.5833 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.6003 	EXP 	unnamed TextStim: text = 'Inhale'
18.6003 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.6167 	EXP 	unnamed TextStim: text = 'Inhale'
18.6167 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.6342 	EXP 	unnamed TextStim: text = 'Inhale'
18.6342 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.6508 	EXP 	unnamed TextStim: text = 'Inhale'
18.6508 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.6677 	EXP 	unnamed TextStim: text = 'Inhale'
18.6677 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.6843 	EXP 	unnamed TextStim: text = 'Inhale'
18.6843 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.7007 	EXP 	unnamed TextStim: text = 'Inhale'
18.7007 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.7172 	EXP 	unnamed TextStim: text = 'Inhale'
18.7172 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.7338 	EXP 	unnamed TextStim: text = 'Inhale'
18.7338 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.7507 	EXP 	unnamed TextStim: text = 'Inhale'
18.7507 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.7670 	EXP 	unnamed TextStim: text = 'Inhale'
18.7670 	EXP 	unnamed TextStim: text = 'Time remaining: 10s'
18.7986 	EXP 	unnamed TextStim: text = 'Inhale'
18.7986 	EXP 	unnamed TextStim: height = 0.03
18.7986 	EXP 	unnamed TextStim: height = 0.03
18.7986 	EXP 	unnamed TextStim: height = 0.03
18.7986 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
18.8168 	EXP 	unnamed TextStim: text = 'Inhale'
18.8168 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
18.8346 	EXP 	unnamed TextStim: text = 'Inhale'
18.8346 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
18.8501 	EXP 	unnamed TextStim: text = 'Inhale'
18.8501 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
18.8670 	EXP 	unnamed TextStim: text = 'Inhale'
18.8670 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
18.8839 	EXP 	unnamed TextStim: text = 'Inhale'
18.8839 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
18.9009 	EXP 	unnamed TextStim: text = 'Inhale'
18.9009 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
18.9169 	EXP 	unnamed TextStim: text = 'Inhale'
18.9169 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
18.9336 	EXP 	unnamed TextStim: text = 'Inhale'
18.9336 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
18.9501 	EXP 	unnamed TextStim: text = 'Inhale'
18.9501 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
18.9671 	EXP 	unnamed TextStim: text = 'Inhale'
18.9671 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
18.9837 	EXP 	unnamed TextStim: text = 'Inhale'
18.9837 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
18.9996 	EXP 	unnamed TextStim: text = 'Inhale'
18.9996 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.0163 	EXP 	unnamed TextStim: text = 'Inhale'
19.0163 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.0332 	EXP 	unnamed TextStim: text = 'Inhale'
19.0332 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.0500 	EXP 	unnamed TextStim: text = 'Inhale'
19.0500 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.0666 	EXP 	unnamed TextStim: text = 'Inhale'
19.0666 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.0833 	EXP 	unnamed TextStim: text = 'Inhale'
19.0833 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.1006 	EXP 	unnamed TextStim: text = 'Inhale'
19.1006 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.1166 	EXP 	unnamed TextStim: text = 'Inhale'
19.1166 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.1331 	EXP 	unnamed TextStim: text = 'Inhale'
19.1331 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.1496 	EXP 	unnamed TextStim: text = 'Inhale'
19.1496 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.1663 	EXP 	unnamed TextStim: text = 'Inhale'
19.1663 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.1827 	EXP 	unnamed TextStim: text = 'Inhale'
19.1827 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.1997 	EXP 	unnamed TextStim: text = 'Inhale'
19.1997 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.2160 	EXP 	unnamed TextStim: text = 'Inhale'
19.2160 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.2335 	EXP 	unnamed TextStim: text = 'Inhale'
19.2335 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.2499 	EXP 	unnamed TextStim: text = 'Inhale'
19.2499 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.2670 	EXP 	unnamed TextStim: text = 'Inhale'
19.2670 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.2839 	EXP 	unnamed TextStim: text = 'Inhale'
19.2839 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.2999 	EXP 	unnamed TextStim: text = 'Inhale'
19.2999 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.3167 	EXP 	unnamed TextStim: text = 'Inhale'
19.3167 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.3335 	EXP 	unnamed TextStim: text = 'Inhale'
19.3335 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.3495 	EXP 	unnamed TextStim: text = 'Inhale'
19.3495 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.3658 	EXP 	unnamed TextStim: text = 'Inhale'
19.3658 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.3830 	EXP 	unnamed TextStim: text = 'Inhale'
19.3830 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.4001 	EXP 	unnamed TextStim: text = 'Inhale'
19.4001 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.4163 	EXP 	unnamed TextStim: text = 'Inhale'
19.4163 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.4330 	EXP 	unnamed TextStim: text = 'Inhale'
19.4330 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.4497 	EXP 	unnamed TextStim: text = 'Inhale'
19.4497 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.4661 	EXP 	unnamed TextStim: text = 'Inhale'
19.4661 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.4838 	EXP 	unnamed TextStim: text = 'Inhale'
19.4838 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.4997 	EXP 	unnamed TextStim: text = 'Inhale'
19.4997 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.5167 	EXP 	unnamed TextStim: text = 'Inhale'
19.5167 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.5332 	EXP 	unnamed TextStim: text = 'Inhale'
19.5332 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.5497 	EXP 	unnamed TextStim: text = 'Inhale'
19.5497 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.5666 	EXP 	unnamed TextStim: text = 'Inhale'
19.5666 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.5833 	EXP 	unnamed TextStim: text = 'Inhale'
19.5833 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.6006 	EXP 	unnamed TextStim: text = 'Inhale'
19.6006 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.6165 	EXP 	unnamed TextStim: text = 'Inhale'
19.6165 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.6349 	EXP 	unnamed TextStim: text = 'Inhale'
19.6349 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.6502 	EXP 	unnamed TextStim: text = 'Inhale'
19.6502 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.6664 	EXP 	unnamed TextStim: text = 'Inhale'
19.6664 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.6832 	EXP 	unnamed TextStim: text = 'Inhale'
19.6832 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.6996 	EXP 	unnamed TextStim: text = 'Inhale'
19.6996 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.7161 	EXP 	unnamed TextStim: text = 'Inhale'
19.7161 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.7325 	EXP 	unnamed TextStim: text = 'Inhale'
19.7325 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.7490 	EXP 	unnamed TextStim: text = 'Inhale'
19.7490 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.7657 	EXP 	unnamed TextStim: text = 'Inhale'
19.7657 	EXP 	unnamed TextStim: text = 'Time remaining: 9s'
19.7822 	EXP 	unnamed TextStim: text = 'Inhale'
19.7822 	EXP 	unnamed TextStim: height = 0.03
19.7822 	EXP 	unnamed TextStim: height = 0.03
19.7822 	EXP 	unnamed TextStim: height = 0.03
19.7822 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
19.7989 	EXP 	unnamed TextStim: text = 'Inhale'
19.7989 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
19.8159 	EXP 	unnamed TextStim: text = 'Inhale'
19.8159 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
19.8332 	EXP 	unnamed TextStim: text = 'Inhale'
19.8332 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
19.8492 	EXP 	unnamed TextStim: text = 'Inhale'
19.8492 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
19.8659 	EXP 	unnamed TextStim: text = 'Inhale'
19.8659 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
19.8833 	EXP 	unnamed TextStim: text = 'Inhale'
19.8833 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
19.8994 	EXP 	unnamed TextStim: text = 'Inhale'
19.8994 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
19.9163 	EXP 	unnamed TextStim: text = 'Inhale'
19.9163 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
19.9328 	EXP 	unnamed TextStim: text = 'Inhale'
19.9328 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
19.9492 	EXP 	unnamed TextStim: text = 'Inhale'
19.9492 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
19.9665 	EXP 	unnamed TextStim: text = 'Inhale'
19.9665 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
19.9829 	EXP 	unnamed TextStim: text = 'Inhale'
19.9829 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
19.9987 	EXP 	unnamed TextStim: text = 'Inhale'
19.9987 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.0152 	EXP 	unnamed TextStim: text = 'Inhale'
20.0152 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.0323 	EXP 	unnamed TextStim: text = 'Inhale'
20.0323 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.0485 	EXP 	unnamed TextStim: text = 'Inhale'
20.0485 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.0653 	EXP 	unnamed TextStim: text = 'Inhale'
20.0653 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.0820 	EXP 	unnamed TextStim: text = 'Inhale'
20.0820 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.0989 	EXP 	unnamed TextStim: text = 'Inhale'
20.0989 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.1154 	EXP 	unnamed TextStim: text = 'Inhale'
20.1154 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.1320 	EXP 	unnamed TextStim: text = 'Inhale'
20.1320 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.1487 	EXP 	unnamed TextStim: text = 'Inhale'
20.1487 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.1652 	EXP 	unnamed TextStim: text = 'Inhale'
20.1652 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.1823 	EXP 	unnamed TextStim: text = 'Inhale'
20.1823 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.1988 	EXP 	unnamed TextStim: text = 'Inhale'
20.1988 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.2186 	EXP 	unnamed TextStim: text = 'Inhale'
20.2186 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.2326 	EXP 	unnamed TextStim: text = 'Inhale'
20.2326 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.2488 	EXP 	unnamed TextStim: text = 'Inhale'
20.2488 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.2656 	EXP 	unnamed TextStim: text = 'Inhale'
20.2656 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.2818 	EXP 	unnamed TextStim: text = 'Inhale'
20.2818 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.2987 	EXP 	unnamed TextStim: text = 'Inhale'
20.2987 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.3149 	EXP 	unnamed TextStim: text = 'Inhale'
20.3149 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.3320 	EXP 	unnamed TextStim: text = 'Inhale'
20.3320 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.3485 	EXP 	unnamed TextStim: text = 'Inhale'
20.3485 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.3653 	EXP 	unnamed TextStim: text = 'Inhale'
20.3653 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.3817 	EXP 	unnamed TextStim: text = 'Inhale'
20.3817 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.3987 	EXP 	unnamed TextStim: text = 'Inhale'
20.3987 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.4158 	EXP 	unnamed TextStim: text = 'Inhale'
20.4158 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.4326 	EXP 	unnamed TextStim: text = 'Inhale'
20.4326 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.4491 	EXP 	unnamed TextStim: text = 'Inhale'
20.4491 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.4661 	EXP 	unnamed TextStim: text = 'Inhale'
20.4661 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.4826 	EXP 	unnamed TextStim: text = 'Inhale'
20.4826 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.4989 	EXP 	unnamed TextStim: text = 'Inhale'
20.4989 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.5161 	EXP 	unnamed TextStim: text = 'Inhale'
20.5161 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.5323 	EXP 	unnamed TextStim: text = 'Inhale'
20.5323 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.5484 	EXP 	unnamed TextStim: text = 'Inhale'
20.5484 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.5658 	EXP 	unnamed TextStim: text = 'Inhale'
20.5658 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.5822 	EXP 	unnamed TextStim: text = 'Inhale'
20.5822 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.5987 	EXP 	unnamed TextStim: text = 'Inhale'
20.5987 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.6164 	EXP 	unnamed TextStim: text = 'Inhale'
20.6164 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.6319 	EXP 	unnamed TextStim: text = 'Inhale'
20.6319 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.6484 	EXP 	unnamed TextStim: text = 'Inhale'
20.6484 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.6656 	EXP 	unnamed TextStim: text = 'Inhale'
20.6656 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.6830 	EXP 	unnamed TextStim: text = 'Inhale'
20.6830 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.6989 	EXP 	unnamed TextStim: text = 'Inhale'
20.6989 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.7159 	EXP 	unnamed TextStim: text = 'Inhale'
20.7159 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.7323 	EXP 	unnamed TextStim: text = 'Inhale'
20.7323 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.7488 	EXP 	unnamed TextStim: text = 'Inhale'
20.7488 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.7657 	EXP 	unnamed TextStim: text = 'Inhale'
20.7657 	EXP 	unnamed TextStim: text = 'Time remaining: 8s'
20.7825 	EXP 	unnamed TextStim: text = 'Inhale'
20.7825 	EXP 	unnamed TextStim: height = 0.03
20.7825 	EXP 	unnamed TextStim: height = 0.03
20.7825 	EXP 	unnamed TextStim: height = 0.03
20.7825 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
20.7985 	EXP 	unnamed TextStim: text = 'Inhale'
20.7985 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
20.8160 	EXP 	unnamed TextStim: text = 'Inhale'
20.8160 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
20.8322 	EXP 	unnamed TextStim: text = 'Inhale'
20.8322 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
20.8484 	EXP 	unnamed TextStim: text = 'Inhale'
20.8484 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
20.8665 	EXP 	unnamed TextStim: text = 'Inhale'
20.8665 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
20.8819 	EXP 	unnamed TextStim: text = 'Inhale'
20.8819 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
20.8988 	EXP 	unnamed TextStim: text = 'Inhale'
20.8988 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
20.9149 	EXP 	unnamed TextStim: text = 'Inhale'
20.9149 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
20.9314 	EXP 	unnamed TextStim: text = 'Inhale'
20.9314 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
20.9479 	EXP 	unnamed TextStim: text = 'Inhale'
20.9479 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
20.9648 	EXP 	unnamed TextStim: text = 'Inhale'
20.9648 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
20.9815 	EXP 	unnamed TextStim: text = 'Inhale'
20.9815 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
20.9985 	EXP 	unnamed TextStim: text = 'Inhale'
20.9985 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.0153 	EXP 	unnamed TextStim: text = 'Inhale'
21.0153 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.0314 	EXP 	unnamed TextStim: text = 'Inhale'
21.0314 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.0483 	EXP 	unnamed TextStim: text = 'Inhale'
21.0483 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.0661 	EXP 	unnamed TextStim: text = 'Inhale'
21.0661 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.0821 	EXP 	unnamed TextStim: text = 'Inhale'
21.0821 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.0983 	EXP 	unnamed TextStim: text = 'Inhale'
21.0983 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.1148 	EXP 	unnamed TextStim: text = 'Inhale'
21.1148 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.1318 	EXP 	unnamed TextStim: text = 'Inhale'
21.1318 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.1486 	EXP 	unnamed TextStim: text = 'Inhale'
21.1486 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.1657 	EXP 	unnamed TextStim: text = 'Inhale'
21.1657 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.1817 	EXP 	unnamed TextStim: text = 'Inhale'
21.1817 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.1982 	EXP 	unnamed TextStim: text = 'Inhale'
21.1982 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.2148 	EXP 	unnamed TextStim: text = 'Inhale'
21.2148 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.2315 	EXP 	unnamed TextStim: text = 'Inhale'
21.2315 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.2483 	EXP 	unnamed TextStim: text = 'Inhale'
21.2483 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.2657 	EXP 	unnamed TextStim: text = 'Inhale'
21.2657 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.2833 	EXP 	unnamed TextStim: text = 'Inhale'
21.2833 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.2982 	EXP 	unnamed TextStim: text = 'Inhale'
21.2982 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.3153 	EXP 	unnamed TextStim: text = 'Inhale'
21.3153 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.3316 	EXP 	unnamed TextStim: text = 'Inhale'
21.3316 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.3481 	EXP 	unnamed TextStim: text = 'Inhale'
21.3481 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.3656 	EXP 	unnamed TextStim: text = 'Inhale'
21.3656 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.3813 	EXP 	unnamed TextStim: text = 'Inhale'
21.3813 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.3981 	EXP 	unnamed TextStim: text = 'Inhale'
21.3981 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.4150 	EXP 	unnamed TextStim: text = 'Inhale'
21.4150 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.4312 	EXP 	unnamed TextStim: text = 'Inhale'
21.4312 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.4483 	EXP 	unnamed TextStim: text = 'Inhale'
21.4483 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.4656 	EXP 	unnamed TextStim: text = 'Inhale'
21.4656 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.4813 	EXP 	unnamed TextStim: text = 'Inhale'
21.4813 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.4975 	EXP 	unnamed TextStim: text = 'Inhale'
21.4975 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.5147 	EXP 	unnamed TextStim: text = 'Inhale'
21.5147 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.5308 	EXP 	unnamed TextStim: text = 'Inhale'
21.5308 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.5475 	EXP 	unnamed TextStim: text = 'Inhale'
21.5475 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.5647 	EXP 	unnamed TextStim: text = 'Inhale'
21.5647 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.5814 	EXP 	unnamed TextStim: text = 'Inhale'
21.5814 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.5983 	EXP 	unnamed TextStim: text = 'Inhale'
21.5983 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.6155 	EXP 	unnamed TextStim: text = 'Inhale'
21.6155 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.6323 	EXP 	unnamed TextStim: text = 'Inhale'
21.6323 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.6475 	EXP 	unnamed TextStim: text = 'Inhale'
21.6475 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.6647 	EXP 	unnamed TextStim: text = 'Inhale'
21.6647 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.6811 	EXP 	unnamed TextStim: text = 'Inhale'
21.6811 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.6982 	EXP 	unnamed TextStim: text = 'Inhale'
21.6982 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.7152 	EXP 	unnamed TextStim: text = 'Inhale'
21.7152 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.7313 	EXP 	unnamed TextStim: text = 'Inhale'
21.7313 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.7481 	EXP 	unnamed TextStim: text = 'Inhale'
21.7481 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.7657 	EXP 	unnamed TextStim: text = 'Inhale'
21.7657 	EXP 	unnamed TextStim: text = 'Time remaining: 7s'
21.7837 	EXP 	unnamed TextStim: text = 'Inhale'
21.7837 	EXP 	unnamed TextStim: height = 0.03
21.7837 	EXP 	unnamed TextStim: height = 0.03
21.7837 	EXP 	unnamed TextStim: height = 0.03
21.7837 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
21.7978 	EXP 	unnamed TextStim: text = 'Inhale'
21.7978 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
21.8148 	EXP 	unnamed TextStim: text = 'Inhale'
21.8148 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
21.8327 	EXP 	unnamed TextStim: text = 'Inhale'
21.8327 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
21.8477 	EXP 	unnamed TextStim: text = 'Inhale'
21.8477 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
21.8645 	EXP 	unnamed TextStim: text = 'Inhale'
21.8645 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
21.8811 	EXP 	unnamed TextStim: text = 'Inhale'
21.8811 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
21.8978 	EXP 	unnamed TextStim: text = 'Inhale'
21.8978 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
21.9153 	EXP 	unnamed TextStim: text = 'Inhale'
21.9153 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
21.9311 	EXP 	unnamed TextStim: text = 'Inhale'
21.9311 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
21.9476 	EXP 	unnamed TextStim: text = 'Inhale'
21.9476 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
21.9646 	EXP 	unnamed TextStim: text = 'Inhale'
21.9646 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
21.9810 	EXP 	unnamed TextStim: text = 'Inhale'
21.9810 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
21.9976 	EXP 	unnamed TextStim: text = 'Inhale'
21.9976 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
22.0152 	EXP 	unnamed TextStim: text = 'Inhale'
22.0152 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
22.0306 	EXP 	unnamed TextStim: text = 'Inhale'
22.0306 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
22.0474 	EXP 	unnamed TextStim: text = 'Inhale'
22.0474 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
22.0643 	EXP 	unnamed TextStim: text = 'Inhale'
22.0643 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
22.0809 	EXP 	unnamed TextStim: text = 'Inhale'
22.0809 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
22.0974 	EXP 	unnamed TextStim: text = 'Inhale'
22.0974 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
22.1144 	EXP 	unnamed TextStim: text = 'Inhale'
22.1144 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
22.1310 	EXP 	unnamed TextStim: text = 'Inhale'
22.1310 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
22.1475 	EXP 	unnamed TextStim: text = 'Inhale'
22.1475 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
22.1641 	EXP 	unnamed TextStim: text = 'Inhale'
22.1641 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
22.1807 	EXP 	unnamed TextStim: text = 'Inhale'
22.1807 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
22.1971 	EXP 	unnamed TextStim: text = 'Inhale'
22.1971 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
22.2139 	EXP 	unnamed TextStim: text = 'Inhale'
22.2139 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
22.2309 	EXP 	unnamed TextStim: text = 'Inhale'
22.2309 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
22.2476 	EXP 	unnamed TextStim: text = 'Inhale'
22.2476 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
22.2650 	EXP 	unnamed TextStim: text = 'Inhale'
22.2650 	EXP 	unnamed TextStim: text = 'Time remaining: 6s'
22.5028 	EXP 	window1: mouseVisible = True
