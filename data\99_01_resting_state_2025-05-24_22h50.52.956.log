6.9019 	WARNING 	User requested fullscreen with size [1536  864], but screen is actually [1920, 1080]. Using actual size
7.6570 	EXP 	Created window1 = Window(allowGUI=True, allowStencil=True, autoLog=True, backendConf=UNKNOWN, backgroundFit=<method-wrapper '__getattribute__' of attributeSetter object at 0x000001E36B8A1BE0>, backgroundImage=<method-wrapper '__getattribute__' of attributeSetter object at 0x000001E36B8A1B80>, bitsMode=UNKNOWN, blendMode='avg', bpc=(8, 8, 8), color=array([0, 0, 0]), colorSpace='rgb', depthBits=8, fullscr=<method-wrapper '__getattribute__' of attributeSetter object at 0x000001E36B8A19D0>, gamma=None, gammaErrorPolicy='raise', lms=UNKNOWN, monitor=<psychopy.monitors.calibTools.Monitor object at 0x000001E376977430>, multiSample=False, name='window1', numSamples=2, pos=(0, 0), screen=0, size=array([1920, 1080]), stencilBits=8, stereo=False, title='PsychoPy', units='height', useFBO=True, useRetina=False, viewOri=0.0, viewPos=None, viewScale=None, waitBlanking=True, winType='pyglet')
7.6571 	EXP 	window1: mouseVisible = True
7.6571 	EXP 	window1: backgroundImage = ''
7.6571 	EXP 	window1: backgroundFit = 'none'
7.6590 	EXP 	window1: Attempting to measure frame rate of screen (0) ...
7.6590 	EXP 	window1: recordFrameIntervals = False
7.8216 	EXP 	window1: recordFrameIntervals = True
8.0043 	EXP 	Screen (0) actual frame rate measured at 60.22Hz
8.0044 	EXP 	window1: recordFrameIntervals = False
8.0045 	EXP 	window1: mouseVisible = False
10.5017 	EXP 	01_resting_state: status = STARTED
10.6760 	EXP 	window1: mouseVisible = True
10.7476 	EXP 	Created text = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.7477 	EXP 	window1: mouseVisible = True
10.7531 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
10.7533 	EXP 	window1: mouseVisible = True
10.7600 	EXP 	Created text_question_number = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_question_number', opacity=1.0, ori=0.0, pos=array([-0.5, -0.5]), rgb=UNKNOWN, text='Questionnaire #', units='height', win=Window(...), wrapWidth=1)
10.7601 	EXP 	window1: mouseVisible = True
10.7735 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-2.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.7757 	EXP 	Sound exp_end_bip set volume 1.000
10.7759 	EXP 	window1: mouseVisible = True
10.7867 	EXP 	Created text_2 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_2', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.7908 	EXP 	window1: mouseVisible = True
10.8181 	EXP 	window1: mouseVisible = True
10.8216 	EXP 	Created text_question_number = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_question_number', opacity=1.0, ori=0.0, pos=array([-0.5, -0.5]), rgb=UNKNOWN, text='Questionnaire #', units='height', win=Window(...), wrapWidth=1)
10.8218 	EXP 	window1: mouseVisible = True
10.8294 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-2.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.8315 	EXP 	Sound exp_end_bip set volume 1.000
10.8318 	EXP 	window1: mouseVisible = True
10.8397 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.8399 	EXP 	window1: mouseVisible = True
10.8430 	EXP 	Created text_question_number = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_question_number', opacity=1.0, ori=0.0, pos=array([-0.5, -0.5]), rgb=UNKNOWN, text='Questionnaire #', units='height', win=Window(...), wrapWidth=1)
10.8433 	EXP 	window1: mouseVisible = True
10.8513 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-2.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.8532 	EXP 	Sound exp_end_bip set volume 1.000
10.8534 	EXP 	window1: mouseVisible = True
10.8567 	EXP 	Created text_question_number = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_question_number', opacity=1.0, ori=0.0, pos=array([-0.5, -0.5]), rgb=UNKNOWN, text='Questionnaire #', units='height', win=Window(...), wrapWidth=1)
10.8569 	EXP 	window1: mouseVisible = True
10.8659 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-2.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.8680 	EXP 	Sound exp_end_bip set volume 1.000
10.8682 	EXP 	window1: mouseVisible = True
10.8735 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.8737 	EXP 	window1: mouseVisible = True
10.8764 	EXP 	Created text_question_number = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_question_number', opacity=1.0, ori=0.0, pos=array([-0.5, -0.5]), rgb=UNKNOWN, text='Questionnaire #', units='height', win=Window(...), wrapWidth=1)
10.8767 	EXP 	window1: mouseVisible = True
10.8839 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-2.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.8858 	EXP 	Sound exp_end_bip set volume 1.000
10.8861 	EXP 	window1: mouseVisible = True
10.9021 	EXP 	Created stroop_instr = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='stroop_instr', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.9023 	EXP 	window1: mouseVisible = True
10.9110 	EXP 	Created instrText_4 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-2.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_4', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.9111 	EXP 	window1: mouseVisible = True
10.9127 	EXP 	Created stim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.15, italic=False, languageStyle='LTR', name='stim', opacity=1.0, ori=0, pos=array([0., 0.]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
10.9129 	EXP 	window1: mouseVisible = True
10.9131 	EXP 	window1: mouseVisible = True
10.9135 	EXP 	window1: mouseVisible = True
10.9142 	EXP 	window1: mouseVisible = True
10.9191 	EXP 	window1: mouseVisible = True
10.9193 	EXP 	window1: mouseVisible = True
10.9197 	EXP 	window1: mouseVisible = True
10.9201 	EXP 	window1: mouseVisible = True
10.9542 	EXP 	window1: mouseVisible = True
10.9592 	EXP 	Created instrText_2 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-4.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_2', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.9594 	EXP 	window1: mouseVisible = True
10.9596 	EXP 	window1: mouseVisible = True
10.9600 	EXP 	window1: mouseVisible = True
10.9604 	EXP 	window1: mouseVisible = True
10.9642 	EXP 	window1: mouseVisible = True
10.9645 	EXP 	window1: mouseVisible = True
10.9652 	EXP 	window1: mouseVisible = True
10.9657 	EXP 	window1: mouseVisible = True
10.9696 	EXP 	window1: mouseVisible = True
10.9697 	EXP 	window1: mouseVisible = True
10.9701 	EXP 	window1: mouseVisible = True
10.9706 	EXP 	window1: mouseVisible = True
10.9741 	EXP 	window1: mouseVisible = True
10.9742 	EXP 	window1: mouseVisible = True
10.9746 	EXP 	window1: mouseVisible = True
10.9750 	EXP 	window1: mouseVisible = True
10.9788 	EXP 	window1: mouseVisible = True
10.9845 	EXP 	Created instrText_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-3.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_3', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
10.9847 	EXP 	window1: mouseVisible = True
10.9932 	EXP 	Created text_question_number = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_question_number', opacity=1.0, ori=0.0, pos=array([-0.5, -0.5]), rgb=UNKNOWN, text='Questionnaire #', units='height', win=Window(...), wrapWidth=1)
10.9934 	EXP 	window1: mouseVisible = True
11.0088 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-2.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.0107 	EXP 	Sound exp_end_bip set volume 1.000
11.0109 	EXP 	window1: mouseVisible = True
11.0203 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.0206 	EXP 	window1: mouseVisible = True
11.0239 	EXP 	Created text_question_number = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_question_number', opacity=1.0, ori=0.0, pos=array([-0.5, -0.5]), rgb=UNKNOWN, text='Questionnaire #', units='height', win=Window(...), wrapWidth=1)
11.0241 	EXP 	window1: mouseVisible = True
11.0330 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-2.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.0351 	EXP 	Sound exp_end_bip set volume 1.000
11.0353 	EXP 	window1: mouseVisible = True
11.0426 	EXP 	Created text = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.0428 	EXP 	window1: mouseVisible = True
11.0469 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
11.0472 	EXP 	window1: mouseVisible = True
11.0547 	EXP 	Created text_question_number = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_question_number', opacity=1.0, ori=0.0, pos=array([-0.5, -0.5]), rgb=UNKNOWN, text='Questionnaire #', units='height', win=Window(...), wrapWidth=1)
11.0550 	EXP 	window1: mouseVisible = True
11.0696 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-2.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
11.0719 	EXP 	Sound exp_end_bip set volume 1.000
11.0721 	EXP 	window1: mouseVisible = True
11.0813 	EXP 	Created EndMessage = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='EndMessage', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='Thank you!', units='height', win=Window(...), wrapWidth=1)
0.0050 	EXP 	video: autoLog = True
0.0050 	EXP 	trial_counter: autoLog = True
0.0050 	EXP 	fbtxt: autoLog = True
0.0050 	EXP 	trial_counter_2: autoLog = True
0.0221 	EXP 	text: autoDraw = True
0.8576 	DATA 	Keypress: f1
0.8700 	EXP 	text: autoDraw = False
0.8700 	EXP 	cross: autoDraw = True
5.8841 	EXP 	Sound exp_end_bip started
5.8837 	EXP 	cross: autoDraw = False
5.8837 	EXP 	cross: autoDraw = False
5.8837 	EXP 	text_question_number: autoDraw = True
5.8837 	EXP 	text_q_stress: autoDraw = True
7.0699 	EXP 	Sound exp_end_bip stopped
15.8465 	DATA 	Keypress: f1
15.8763 	EXP 	text_question_number: autoDraw = False
15.8763 	EXP 	text_q_stress: autoDraw = False
15.8763 	EXP 	text_2: autoDraw = True
17.1610 	DATA 	Keypress: f1
17.1692 	EXP 	text_2: autoDraw = False
17.1692 	EXP 	video: autoDraw = True
19.4083 	EXP 	Sound exp_end_bip started
19.4074 	EXP 	video: autoDraw = False
19.4074 	EXP 	video: autoDraw = False
19.4074 	EXP 	text_question_number: autoDraw = True
19.4074 	EXP 	text_q_stress: autoDraw = True
20.5940 	EXP 	Sound exp_end_bip stopped
23.9225 	DATA 	Keypress: f1
23.9343 	EXP 	text_question_number: autoDraw = False
23.9343 	EXP 	text_q_stress: autoDraw = False
23.9343 	EXP 	text_3: autoDraw = True
25.5369 	DATA 	Keypress: f1
25.5522 	EXP 	window1: mouseVisible = True
25.5542 	EXP 	Created unnamed ShapeStim = ShapeStim(__class__=<class 'psychopy.visual.shape.ShapeStim'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=False, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0, 0, 0]), fillColorSpace='rgb', fillRGB=array([0, 0, 0]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=3, name='unnamed ShapeStim', opacity=0.35, ori=0.0, pos=array([0., 0.]), size=array([1., 1.]), units='height', vertices=ndarray(...), win=Window(...), windingRule=None)
25.5586 	EXP 	window1: mouseVisible = True
25.5622 	EXP 	Created unnamed Circle = Circle(__class__=<class 'psychopy.visual.circle.Circle'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=2, name='unnamed Circle', opacity=1.0, ori=0.0, pos=array([0., 0.]), size=array([1., 1.]), units='height', vertices=ndarray(...), win=Window(...))
25.5623 	EXP 	window1: mouseVisible = True
25.5639 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.05882353, 0.61568627, 0.84313725]), fillColorSpace='rgb', fillRGB=array([0.05882353, 0.61568627, 0.84313725]), interpolate=True, lineColor=array([0., 0., 0.]), lineColorSpace='rgb', lineRGB=array([0, 0, 0]), lineWidth=1.5, name='unnamed Rect', opacity=0.1, ori=0.0, pos=array([0., 0.]), size=array([1.77777778, 0.5       ]), units='height', vertices=ndarray(...), win=Window(...))
25.5642 	EXP 	window1: mouseVisible = True
25.5700 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([ 0.  , -0.35]), rgb=UNKNOWN, text='Inhale', units='height', win=Window(...), wrapWidth=1)
25.5701 	EXP 	window1: mouseVisible = True
25.5716 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([0. , 0.4]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
25.5824 	EXP 	text_3: autoDraw = False
25.5824 	EXP 	unnamed TextStim: height = 0.03
25.5824 	EXP 	unnamed TextStim: height = 0.03
25.5824 	EXP 	unnamed TextStim: height = 0.03
25.5824 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.5824 	EXP 	unnamed TextStim: text = 'Inhale'
25.6005 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.6005 	EXP 	unnamed TextStim: text = 'Inhale'
25.6168 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.6168 	EXP 	unnamed TextStim: text = 'Inhale'
25.6337 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.6337 	EXP 	unnamed TextStim: text = 'Inhale'
25.6508 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.6508 	EXP 	unnamed TextStim: text = 'Inhale'
25.6675 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.6675 	EXP 	unnamed TextStim: text = 'Inhale'
25.6837 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.6837 	EXP 	unnamed TextStim: text = 'Inhale'
25.7006 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.7006 	EXP 	unnamed TextStim: text = 'Inhale'
25.7169 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.7169 	EXP 	unnamed TextStim: text = 'Inhale'
25.7333 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.7333 	EXP 	unnamed TextStim: text = 'Inhale'
25.7504 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.7504 	EXP 	unnamed TextStim: text = 'Inhale'
25.7670 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.7670 	EXP 	unnamed TextStim: text = 'Inhale'
25.7836 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.7836 	EXP 	unnamed TextStim: text = 'Inhale'
25.8004 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.8004 	EXP 	unnamed TextStim: text = 'Inhale'
25.8169 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.8169 	EXP 	unnamed TextStim: text = 'Inhale'
25.8336 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.8336 	EXP 	unnamed TextStim: text = 'Inhale'
25.8507 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.8507 	EXP 	unnamed TextStim: text = 'Inhale'
25.8673 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.8673 	EXP 	unnamed TextStim: text = 'Inhale'
25.8840 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.8840 	EXP 	unnamed TextStim: text = 'Inhale'
25.9025 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.9025 	EXP 	unnamed TextStim: text = 'Inhale'
25.9172 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.9172 	EXP 	unnamed TextStim: text = 'Inhale'
25.9339 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.9339 	EXP 	unnamed TextStim: text = 'Inhale'
25.9507 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.9507 	EXP 	unnamed TextStim: text = 'Inhale'
25.9669 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.9669 	EXP 	unnamed TextStim: text = 'Inhale'
25.9837 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
25.9837 	EXP 	unnamed TextStim: text = 'Inhale'
26.0000 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.0000 	EXP 	unnamed TextStim: text = 'Inhale'
26.0168 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.0168 	EXP 	unnamed TextStim: text = 'Inhale'
26.0334 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.0334 	EXP 	unnamed TextStim: text = 'Inhale'
26.0504 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.0504 	EXP 	unnamed TextStim: text = 'Inhale'
26.0666 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.0666 	EXP 	unnamed TextStim: text = 'Inhale'
26.0833 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.0833 	EXP 	unnamed TextStim: text = 'Inhale'
26.1006 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.1006 	EXP 	unnamed TextStim: text = 'Inhale'
26.1166 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.1166 	EXP 	unnamed TextStim: text = 'Inhale'
26.1336 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.1336 	EXP 	unnamed TextStim: text = 'Inhale'
26.1504 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.1504 	EXP 	unnamed TextStim: text = 'Inhale'
26.1676 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.1676 	EXP 	unnamed TextStim: text = 'Inhale'
26.1833 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.1833 	EXP 	unnamed TextStim: text = 'Inhale'
26.2002 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.2002 	EXP 	unnamed TextStim: text = 'Inhale'
26.2173 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.2173 	EXP 	unnamed TextStim: text = 'Inhale'
26.2336 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.2336 	EXP 	unnamed TextStim: text = 'Inhale'
26.2503 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.2503 	EXP 	unnamed TextStim: text = 'Inhale'
26.2669 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.2669 	EXP 	unnamed TextStim: text = 'Inhale'
26.2833 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.2833 	EXP 	unnamed TextStim: text = 'Inhale'
26.2996 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.2996 	EXP 	unnamed TextStim: text = 'Inhale'
26.3163 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.3163 	EXP 	unnamed TextStim: text = 'Inhale'
26.3332 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.3332 	EXP 	unnamed TextStim: text = 'Inhale'
26.3500 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.3500 	EXP 	unnamed TextStim: text = 'Inhale'
26.3666 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.3666 	EXP 	unnamed TextStim: text = 'Inhale'
26.3833 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.3833 	EXP 	unnamed TextStim: text = 'Inhale'
26.3999 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.3999 	EXP 	unnamed TextStim: text = 'Inhale'
26.4166 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.4166 	EXP 	unnamed TextStim: text = 'Inhale'
26.4333 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.4333 	EXP 	unnamed TextStim: text = 'Inhale'
26.4504 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.4504 	EXP 	unnamed TextStim: text = 'Inhale'
26.4667 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.4667 	EXP 	unnamed TextStim: text = 'Inhale'
26.4841 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.4841 	EXP 	unnamed TextStim: text = 'Inhale'
26.5006 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.5006 	EXP 	unnamed TextStim: text = 'Inhale'
26.5165 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.5165 	EXP 	unnamed TextStim: text = 'Inhale'
26.5332 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.5332 	EXP 	unnamed TextStim: text = 'Inhale'
26.5499 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.5499 	EXP 	unnamed TextStim: text = 'Inhale'
26.5666 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.5666 	EXP 	unnamed TextStim: text = 'Inhale'
26.5833 	EXP 	unnamed TextStim: text = 'Time remaining: 4s'
26.5833 	EXP 	unnamed TextStim: text = 'Inhale'
26.6001 	EXP 	unnamed TextStim: height = 0.03
26.6001 	EXP 	unnamed TextStim: height = 0.03
26.6001 	EXP 	unnamed TextStim: height = 0.03
26.6001 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.6001 	EXP 	unnamed TextStim: text = 'Inhale'
26.6170 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.6170 	EXP 	unnamed TextStim: text = 'Inhale'
26.6332 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.6332 	EXP 	unnamed TextStim: text = 'Inhale'
26.6497 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.6497 	EXP 	unnamed TextStim: text = 'Inhale'
26.6662 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.6662 	EXP 	unnamed TextStim: text = 'Inhale'
26.6830 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.6830 	EXP 	unnamed TextStim: text = 'Inhale'
26.6996 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.6996 	EXP 	unnamed TextStim: text = 'Inhale'
26.7164 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.7164 	EXP 	unnamed TextStim: text = 'Inhale'
26.7333 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.7333 	EXP 	unnamed TextStim: text = 'Inhale'
26.7500 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.7500 	EXP 	unnamed TextStim: text = 'Inhale'
26.7665 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.7665 	EXP 	unnamed TextStim: text = 'Inhale'
26.7828 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.7828 	EXP 	unnamed TextStim: text = 'Inhale'
26.7996 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.7996 	EXP 	unnamed TextStim: text = 'Inhale'
26.8162 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.8162 	EXP 	unnamed TextStim: text = 'Inhale'
26.8327 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.8327 	EXP 	unnamed TextStim: text = 'Inhale'
26.8492 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.8492 	EXP 	unnamed TextStim: text = 'Inhale'
26.8659 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.8659 	EXP 	unnamed TextStim: text = 'Inhale'
26.8826 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.8826 	EXP 	unnamed TextStim: text = 'Inhale'
26.9000 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.9000 	EXP 	unnamed TextStim: text = 'Inhale'
26.9161 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.9161 	EXP 	unnamed TextStim: text = 'Inhale'
26.9327 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.9327 	EXP 	unnamed TextStim: text = 'Inhale'
26.9495 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.9495 	EXP 	unnamed TextStim: text = 'Inhale'
26.9659 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.9659 	EXP 	unnamed TextStim: text = 'Inhale'
26.9830 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.9830 	EXP 	unnamed TextStim: text = 'Inhale'
26.9998 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
26.9998 	EXP 	unnamed TextStim: text = 'Inhale'
27.0165 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.0165 	EXP 	unnamed TextStim: text = 'Inhale'
27.0326 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.0326 	EXP 	unnamed TextStim: text = 'Inhale'
27.0492 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.0492 	EXP 	unnamed TextStim: text = 'Inhale'
27.0661 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.0661 	EXP 	unnamed TextStim: text = 'Inhale'
27.0829 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.0829 	EXP 	unnamed TextStim: text = 'Inhale'
27.1007 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.1007 	EXP 	unnamed TextStim: text = 'Inhale'
27.1161 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.1161 	EXP 	unnamed TextStim: text = 'Inhale'
27.1324 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.1324 	EXP 	unnamed TextStim: text = 'Inhale'
27.1495 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.1495 	EXP 	unnamed TextStim: text = 'Inhale'
27.1657 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.1657 	EXP 	unnamed TextStim: text = 'Inhale'
27.1825 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.1825 	EXP 	unnamed TextStim: text = 'Inhale'
27.1996 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.1996 	EXP 	unnamed TextStim: text = 'Inhale'
27.2160 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.2160 	EXP 	unnamed TextStim: text = 'Inhale'
27.2332 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.2332 	EXP 	unnamed TextStim: text = 'Inhale'
27.2499 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.2499 	EXP 	unnamed TextStim: text = 'Inhale'
27.2659 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.2659 	EXP 	unnamed TextStim: text = 'Inhale'
27.2824 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.2824 	EXP 	unnamed TextStim: text = 'Inhale'
27.2996 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.2996 	EXP 	unnamed TextStim: text = 'Inhale'
27.3158 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.3158 	EXP 	unnamed TextStim: text = 'Inhale'
27.3326 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.3326 	EXP 	unnamed TextStim: text = 'Inhale'
27.3497 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.3497 	EXP 	unnamed TextStim: text = 'Inhale'
27.3660 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.3660 	EXP 	unnamed TextStim: text = 'Inhale'
27.3823 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.3823 	EXP 	unnamed TextStim: text = 'Inhale'
27.3998 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.3998 	EXP 	unnamed TextStim: text = 'Inhale'
27.4162 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.4162 	EXP 	unnamed TextStim: text = 'Inhale'
27.4323 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.4323 	EXP 	unnamed TextStim: text = 'Inhale'
27.4495 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.4495 	EXP 	unnamed TextStim: text = 'Inhale'
27.4658 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.4658 	EXP 	unnamed TextStim: text = 'Inhale'
27.4827 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.4827 	EXP 	unnamed TextStim: text = 'Inhale'
27.4994 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.4994 	EXP 	unnamed TextStim: text = 'Inhale'
27.5156 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.5156 	EXP 	unnamed TextStim: text = 'Inhale'
27.5324 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.5324 	EXP 	unnamed TextStim: text = 'Inhale'
27.5492 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.5492 	EXP 	unnamed TextStim: text = 'Inhale'
27.5657 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.5657 	EXP 	unnamed TextStim: text = 'Inhale'
27.5825 	EXP 	unnamed TextStim: text = 'Time remaining: 3s'
27.5825 	EXP 	unnamed TextStim: text = 'Inhale'
27.5991 	EXP 	unnamed TextStim: height = 0.03
27.5991 	EXP 	unnamed TextStim: height = 0.03
27.5991 	EXP 	unnamed TextStim: height = 0.03
27.5991 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.5991 	EXP 	unnamed TextStim: text = 'Inhale'
27.6151 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.6151 	EXP 	unnamed TextStim: text = 'Inhale'
27.6322 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.6322 	EXP 	unnamed TextStim: text = 'Inhale'
27.6492 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.6492 	EXP 	unnamed TextStim: text = 'Inhale'
27.6654 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.6654 	EXP 	unnamed TextStim: text = 'Inhale'
27.6819 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.6819 	EXP 	unnamed TextStim: text = 'Inhale'
27.6984 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.6984 	EXP 	unnamed TextStim: text = 'Inhale'
27.7155 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.7155 	EXP 	unnamed TextStim: text = 'Inhale'
27.7327 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.7327 	EXP 	unnamed TextStim: text = 'Inhale'
27.7492 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.7492 	EXP 	unnamed TextStim: text = 'Inhale'
27.7655 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.7655 	EXP 	unnamed TextStim: text = 'Inhale'
27.7818 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.7818 	EXP 	unnamed TextStim: text = 'Inhale'
27.7988 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.7988 	EXP 	unnamed TextStim: text = 'Inhale'
27.8154 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.8154 	EXP 	unnamed TextStim: text = 'Inhale'
27.8321 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.8321 	EXP 	unnamed TextStim: text = 'Inhale'
27.8475 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.8475 	EXP 	unnamed TextStim: text = 'Inhale'
27.8641 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.8641 	EXP 	unnamed TextStim: text = 'Inhale'
27.8810 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.8810 	EXP 	unnamed TextStim: text = 'Inhale'
27.8979 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.8979 	EXP 	unnamed TextStim: text = 'Inhale'
27.9140 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.9140 	EXP 	unnamed TextStim: text = 'Inhale'
27.9311 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.9311 	EXP 	unnamed TextStim: text = 'Inhale'
27.9480 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.9480 	EXP 	unnamed TextStim: text = 'Inhale'
27.9651 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.9651 	EXP 	unnamed TextStim: text = 'Inhale'
27.9816 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.9816 	EXP 	unnamed TextStim: text = 'Inhale'
27.9983 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
27.9983 	EXP 	unnamed TextStim: text = 'Inhale'
28.0149 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.0149 	EXP 	unnamed TextStim: text = 'Inhale'
28.0313 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.0313 	EXP 	unnamed TextStim: text = 'Inhale'
28.0474 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.0474 	EXP 	unnamed TextStim: text = 'Inhale'
28.0642 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.0642 	EXP 	unnamed TextStim: text = 'Inhale'
28.0810 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.0810 	EXP 	unnamed TextStim: text = 'Inhale'
28.0979 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.0979 	EXP 	unnamed TextStim: text = 'Inhale'
28.1142 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.1142 	EXP 	unnamed TextStim: text = 'Inhale'
28.1309 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.1309 	EXP 	unnamed TextStim: text = 'Inhale'
28.1477 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.1477 	EXP 	unnamed TextStim: text = 'Inhale'
28.1641 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.1641 	EXP 	unnamed TextStim: text = 'Inhale'
28.1807 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.1807 	EXP 	unnamed TextStim: text = 'Inhale'
28.1974 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.1974 	EXP 	unnamed TextStim: text = 'Inhale'
28.2143 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.2143 	EXP 	unnamed TextStim: text = 'Inhale'
28.2311 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.2311 	EXP 	unnamed TextStim: text = 'Inhale'
28.2476 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.2476 	EXP 	unnamed TextStim: text = 'Inhale'
28.2645 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.2645 	EXP 	unnamed TextStim: text = 'Inhale'
28.2807 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.2807 	EXP 	unnamed TextStim: text = 'Inhale'
28.2972 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.2972 	EXP 	unnamed TextStim: text = 'Inhale'
28.3141 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.3141 	EXP 	unnamed TextStim: text = 'Inhale'
28.3306 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.3306 	EXP 	unnamed TextStim: text = 'Inhale'
28.3474 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.3474 	EXP 	unnamed TextStim: text = 'Inhale'
28.3640 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.3640 	EXP 	unnamed TextStim: text = 'Inhale'
28.3807 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.3807 	EXP 	unnamed TextStim: text = 'Inhale'
28.3972 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.3972 	EXP 	unnamed TextStim: text = 'Inhale'
28.4138 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.4138 	EXP 	unnamed TextStim: text = 'Inhale'
28.4307 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.4307 	EXP 	unnamed TextStim: text = 'Inhale'
28.4476 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.4476 	EXP 	unnamed TextStim: text = 'Inhale'
28.4638 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.4638 	EXP 	unnamed TextStim: text = 'Inhale'
28.4807 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.4807 	EXP 	unnamed TextStim: text = 'Inhale'
28.4974 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.4974 	EXP 	unnamed TextStim: text = 'Inhale'
28.5140 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.5140 	EXP 	unnamed TextStim: text = 'Inhale'
28.5307 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.5307 	EXP 	unnamed TextStim: text = 'Inhale'
28.5478 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.5478 	EXP 	unnamed TextStim: text = 'Inhale'
28.5639 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.5639 	EXP 	unnamed TextStim: text = 'Inhale'
28.5804 	EXP 	unnamed TextStim: text = 'Time remaining: 2s'
28.5804 	EXP 	unnamed TextStim: text = 'Inhale'
28.5974 	EXP 	unnamed TextStim: height = 0.03
28.5974 	EXP 	unnamed TextStim: height = 0.03
28.5974 	EXP 	unnamed TextStim: height = 0.03
28.5974 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.5974 	EXP 	unnamed TextStim: text = 'Inhale'
28.6136 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.6136 	EXP 	unnamed TextStim: text = 'Inhale'
28.6306 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.6306 	EXP 	unnamed TextStim: text = 'Inhale'
28.6471 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.6471 	EXP 	unnamed TextStim: text = 'Inhale'
28.6640 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.6640 	EXP 	unnamed TextStim: text = 'Inhale'
28.6806 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.6806 	EXP 	unnamed TextStim: text = 'Inhale'
28.6971 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.6971 	EXP 	unnamed TextStim: text = 'Inhale'
28.7137 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.7137 	EXP 	unnamed TextStim: text = 'Inhale'
28.7310 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.7310 	EXP 	unnamed TextStim: text = 'Inhale'
28.7476 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.7476 	EXP 	unnamed TextStim: text = 'Inhale'
28.7643 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.7643 	EXP 	unnamed TextStim: text = 'Inhale'
28.7806 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.7806 	EXP 	unnamed TextStim: text = 'Inhale'
28.7970 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.7970 	EXP 	unnamed TextStim: text = 'Inhale'
28.8136 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.8136 	EXP 	unnamed TextStim: text = 'Inhale'
28.8304 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.8304 	EXP 	unnamed TextStim: text = 'Inhale'
28.8470 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.8470 	EXP 	unnamed TextStim: text = 'Inhale'
28.8639 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.8639 	EXP 	unnamed TextStim: text = 'Inhale'
28.8804 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.8804 	EXP 	unnamed TextStim: text = 'Inhale'
28.8970 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.8970 	EXP 	unnamed TextStim: text = 'Inhale'
28.9137 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.9137 	EXP 	unnamed TextStim: text = 'Inhale'
28.9304 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.9304 	EXP 	unnamed TextStim: text = 'Inhale'
28.9472 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.9472 	EXP 	unnamed TextStim: text = 'Inhale'
28.9637 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.9637 	EXP 	unnamed TextStim: text = 'Inhale'
28.9809 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.9809 	EXP 	unnamed TextStim: text = 'Inhale'
28.9971 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
28.9971 	EXP 	unnamed TextStim: text = 'Inhale'
29.0133 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.0133 	EXP 	unnamed TextStim: text = 'Inhale'
29.0305 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.0305 	EXP 	unnamed TextStim: text = 'Inhale'
29.0466 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.0466 	EXP 	unnamed TextStim: text = 'Inhale'
29.0634 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.0634 	EXP 	unnamed TextStim: text = 'Inhale'
29.0806 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.0806 	EXP 	unnamed TextStim: text = 'Inhale'
29.0975 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.0975 	EXP 	unnamed TextStim: text = 'Inhale'
29.1143 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.1143 	EXP 	unnamed TextStim: text = 'Inhale'
29.1308 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.1308 	EXP 	unnamed TextStim: text = 'Inhale'
29.1476 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.1476 	EXP 	unnamed TextStim: text = 'Inhale'
29.1641 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.1641 	EXP 	unnamed TextStim: text = 'Inhale'
29.1813 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.1813 	EXP 	unnamed TextStim: text = 'Inhale'
29.1976 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.1976 	EXP 	unnamed TextStim: text = 'Inhale'
29.2144 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.2144 	EXP 	unnamed TextStim: text = 'Inhale'
29.2307 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.2307 	EXP 	unnamed TextStim: text = 'Inhale'
29.2480 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.2480 	EXP 	unnamed TextStim: text = 'Inhale'
29.2645 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.2645 	EXP 	unnamed TextStim: text = 'Inhale'
29.2834 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.2834 	EXP 	unnamed TextStim: text = 'Inhale'
29.2978 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.2978 	EXP 	unnamed TextStim: text = 'Inhale'
29.3144 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.3144 	EXP 	unnamed TextStim: text = 'Inhale'
29.3307 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.3307 	EXP 	unnamed TextStim: text = 'Inhale'
29.3476 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.3476 	EXP 	unnamed TextStim: text = 'Inhale'
29.3639 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.3639 	EXP 	unnamed TextStim: text = 'Inhale'
29.3807 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.3807 	EXP 	unnamed TextStim: text = 'Inhale'
29.3975 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.3975 	EXP 	unnamed TextStim: text = 'Inhale'
29.4143 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.4143 	EXP 	unnamed TextStim: text = 'Inhale'
29.4309 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.4309 	EXP 	unnamed TextStim: text = 'Inhale'
29.4479 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.4479 	EXP 	unnamed TextStim: text = 'Inhale'
29.4640 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.4640 	EXP 	unnamed TextStim: text = 'Inhale'
29.4807 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.4807 	EXP 	unnamed TextStim: text = 'Inhale'
29.4976 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.4976 	EXP 	unnamed TextStim: text = 'Inhale'
29.5142 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.5142 	EXP 	unnamed TextStim: text = 'Inhale'
29.5311 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.5311 	EXP 	unnamed TextStim: text = 'Inhale'
29.5480 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.5480 	EXP 	unnamed TextStim: text = 'Inhale'
29.5648 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.5648 	EXP 	unnamed TextStim: text = 'Inhale'
29.5818 	EXP 	unnamed TextStim: text = 'Time remaining: 1s'
29.5818 	EXP 	unnamed TextStim: text = 'Inhale'
29.5977 	EXP 	unnamed TextStim: height = 0.03
29.5977 	EXP 	unnamed TextStim: height = 0.03
29.5977 	EXP 	unnamed TextStim: height = 0.03
29.5977 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.5977 	EXP 	unnamed TextStim: text = 'Inhale'
29.6139 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.6139 	EXP 	unnamed TextStim: text = 'Inhale'
29.6306 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.6306 	EXP 	unnamed TextStim: text = 'Inhale'
29.6472 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.6472 	EXP 	unnamed TextStim: text = 'Inhale'
29.6639 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.6639 	EXP 	unnamed TextStim: text = 'Inhale'
29.6807 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.6807 	EXP 	unnamed TextStim: text = 'Inhale'
29.6970 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.6970 	EXP 	unnamed TextStim: text = 'Inhale'
29.7137 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.7137 	EXP 	unnamed TextStim: text = 'Inhale'
29.7307 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.7307 	EXP 	unnamed TextStim: text = 'Inhale'
29.7474 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.7474 	EXP 	unnamed TextStim: text = 'Inhale'
29.7640 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.7640 	EXP 	unnamed TextStim: text = 'Inhale'
29.7809 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.7809 	EXP 	unnamed TextStim: text = 'Inhale'
29.7978 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.7978 	EXP 	unnamed TextStim: text = 'Inhale'
29.8142 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.8142 	EXP 	unnamed TextStim: text = 'Inhale'
29.8315 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.8315 	EXP 	unnamed TextStim: text = 'Inhale'
29.8493 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.8493 	EXP 	unnamed TextStim: text = 'Inhale'
29.8638 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.8638 	EXP 	unnamed TextStim: text = 'Inhale'
29.8816 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.8816 	EXP 	unnamed TextStim: text = 'Inhale'
29.8972 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.8972 	EXP 	unnamed TextStim: text = 'Inhale'
29.9137 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.9137 	EXP 	unnamed TextStim: text = 'Inhale'
29.9307 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.9307 	EXP 	unnamed TextStim: text = 'Inhale'
29.9473 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.9473 	EXP 	unnamed TextStim: text = 'Inhale'
29.9637 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.9637 	EXP 	unnamed TextStim: text = 'Inhale'
29.9810 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.9810 	EXP 	unnamed TextStim: text = 'Inhale'
29.9976 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
29.9976 	EXP 	unnamed TextStim: text = 'Inhale'
30.0137 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.0137 	EXP 	unnamed TextStim: text = 'Inhale'
30.0312 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.0312 	EXP 	unnamed TextStim: text = 'Inhale'
30.0467 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.0467 	EXP 	unnamed TextStim: text = 'Inhale'
30.0632 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.0632 	EXP 	unnamed TextStim: text = 'Inhale'
30.0806 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.0806 	EXP 	unnamed TextStim: text = 'Inhale'
30.0973 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.0973 	EXP 	unnamed TextStim: height = 0.05
30.0973 	EXP 	unnamed TextStim: height = 0.05
30.0973 	EXP 	unnamed TextStim: height = 0.05
30.0973 	EXP 	unnamed TextStim: text = 'Hold'
30.1134 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.1134 	EXP 	unnamed TextStim: text = 'Hold'
30.1308 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.1308 	EXP 	unnamed TextStim: text = 'Hold'
30.1471 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.1471 	EXP 	unnamed TextStim: text = 'Hold'
30.1637 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.1637 	EXP 	unnamed TextStim: text = 'Hold'
30.1809 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.1809 	EXP 	unnamed TextStim: text = 'Hold'
30.1970 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.1970 	EXP 	unnamed TextStim: text = 'Hold'
30.2130 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.2130 	EXP 	unnamed TextStim: text = 'Hold'
30.2303 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.2303 	EXP 	unnamed TextStim: text = 'Hold'
30.2463 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.2463 	EXP 	unnamed TextStim: text = 'Hold'
30.2634 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.2634 	EXP 	unnamed TextStim: text = 'Hold'
30.2808 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.2808 	EXP 	unnamed TextStim: text = 'Hold'
30.2970 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.2970 	EXP 	unnamed TextStim: text = 'Hold'
30.3137 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.3137 	EXP 	unnamed TextStim: text = 'Hold'
30.3307 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.3307 	EXP 	unnamed TextStim: text = 'Hold'
30.3470 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.3470 	EXP 	unnamed TextStim: text = 'Hold'
30.3635 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.3635 	EXP 	unnamed TextStim: text = 'Hold'
30.3819 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.3819 	EXP 	unnamed TextStim: text = 'Hold'
30.3970 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.3970 	EXP 	unnamed TextStim: text = 'Hold'
30.4133 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.4133 	EXP 	unnamed TextStim: text = 'Hold'
30.4303 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.4303 	EXP 	unnamed TextStim: text = 'Hold'
30.4465 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.4465 	EXP 	unnamed TextStim: text = 'Hold'
30.4635 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.4635 	EXP 	unnamed TextStim: text = 'Hold'
30.4807 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.4807 	EXP 	unnamed TextStim: text = 'Hold'
30.4971 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.4971 	EXP 	unnamed TextStim: text = 'Hold'
30.5134 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.5134 	EXP 	unnamed TextStim: text = 'Hold'
30.5302 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.5302 	EXP 	unnamed TextStim: text = 'Hold'
30.5473 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.5473 	EXP 	unnamed TextStim: text = 'Hold'
30.5634 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.5634 	EXP 	unnamed TextStim: text = 'Hold'
30.5800 	EXP 	unnamed TextStim: text = 'Time remaining: 0s'
30.5800 	EXP 	unnamed TextStim: text = 'Hold'
32.6499 	EXP 	Sound exp_end_bip started
32.6523 	EXP 	text_question_number: autoDraw = True
32.6523 	EXP 	text_q_stress: autoDraw = True
33.8344 	EXP 	Sound exp_end_bip stopped
35.3789 	DATA 	Keypress: escape
35.5440 	EXP 	01_resting_state: status = STOPPED
35.5597 	EXP 	text_question_number: autoDraw = False
35.5597 	EXP 	text_q_stress: autoDraw = False
35.5598 	EXP 	01_resting_state: status = STOPPED
35.6464 	EXP 	window1: mouseVisible = True
