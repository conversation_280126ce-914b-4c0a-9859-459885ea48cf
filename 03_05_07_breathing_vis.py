# Paced Breathing Visualization for PsychoPy (height units)
# To be pasted into a code component in a PsychoPy routine

from psychopy import visual, core, event
import numpy as np

# Default breathing pattern settings (in seconds)
inhale_time = 4.5
inhale_hold_time = 0.5
exhale_time = 4.5
exhale_hold_time = 0.5

# Calculate total cycle time
total_cycle_time = inhale_time + inhale_hold_time + exhale_time + exhale_hold_time

# Use breath_duration from experiment variables for total task duration
# This variable should be defined in your experiment
total_duration = breath_duration  # in seconds

# Get window dimensions in height units
# In height units, the height is always 1.0 (from -0.5 to 0.5)
# The width depends on the aspect ratio
win_height = 1.0
win_width = win.size[0] / win.size[1]  # This gives us the aspect ratio

# Normalize horizontal distance to window width
# Leave some margin on both sides (10% of width)
margin = 0.1 * win_width
usable_width = win_width - 2 * margin
x_start = -win_width/2 + margin
x_end = win_width/2 - margin

# Vertical parameters (in height units)
y_min = -0.25  # Bottom position for exhale
y_max = 0.25   # Top position for inhale
y_range = y_max - y_min

# Create the breathing path line segments
# Calculate segment widths based on their duration
inhale_width = (inhale_time / total_cycle_time) * usable_width
inhale_hold_width = (inhale_hold_time / total_cycle_time) * usable_width
exhale_width = (exhale_time / total_cycle_time) * usable_width
exhale_hold_width = (exhale_hold_time / total_cycle_time) * usable_width

# Create path vertices
vertices = [
    [x_start, y_min],                                  # Start point
    [x_start + inhale_width, y_max],                   # End of inhale
    [x_start + inhale_width + inhale_hold_width, y_max],  # End of inhale hold
    [x_start + inhale_width + inhale_hold_width + exhale_width, y_min],  # End of exhale
    [x_end, y_min]                                     # End of exhale hold
]

# Create the path line
path_line = visual.ShapeStim(
    win=win,
    vertices=vertices,
    closeShape=False,
    lineWidth=3,
    lineColor='white',
    opacity=0.7
)

# Create the ball that will travel along the path
ball = visual.Circle(
    win=win,
    radius=0.015,  # Adjusted for height units
    fillColor='lightblue',
    lineColor='white',
    lineWidth=2
)

# Create background shading rectangle
background = visual.Rect(
    win=win,
    width=win_width,
    height=y_range,
    fillColor='skyblue',
    opacity=0.2,
    pos=[0, y_min + y_range/2]  # Start at the bottom
)

# Function to calculate ball position at a given time
def get_ball_position(current_time):
    cycle_time = current_time % total_cycle_time
    
    # Determine which segment we're in
    if cycle_time < inhale_time:
        # Inhale segment - moving up
        progress = cycle_time / inhale_time
        x = x_start + progress * inhale_width
        y = y_min + progress * y_range
        phase = "Inhale"
    
    elif cycle_time < inhale_time + inhale_hold_time:
        # Inhale hold segment - staying at the top
        progress = (cycle_time - inhale_time) / inhale_hold_time
        x = x_start + inhale_width + progress * inhale_hold_width
        y = y_max
        phase = "Hold"
    
    elif cycle_time < inhale_time + inhale_hold_time + exhale_time:
        # Exhale segment - moving down
        progress = (cycle_time - inhale_time - inhale_hold_time) / exhale_time
        x = x_start + inhale_width + inhale_hold_width + progress * exhale_width
        y = y_max - progress * y_range
        phase = "Exhale"
    
    else:
        # Exhale hold segment - staying at the bottom
        progress = (cycle_time - inhale_time - inhale_hold_time - exhale_time) / exhale_hold_time
        x = x_start + inhale_width + inhale_hold_width + exhale_width + progress * exhale_hold_width
        y = y_min
        phase = "Hold"
    
    return x, y, phase

# Create text display for the breathing phase
phase_text = visual.TextStim(
    win=win,
    text="Inhale",
    pos=[0, -0.35],
    color='white',
    height=0.05  # Adjusted for height units
)

# Create a timer display
timer_text = visual.TextStim(
    win=win,
    text="",
    pos=[0, 0.4],
    color='white',
    height=0.03  # Adjusted for height units
)

# Main animation loop
timer = core.Clock()
continue_routine = True

while continue_routine:
    # Get current time
    t = timer.getTime()
    
    # Check if we've reached the total duration
    if t >= total_duration:
        continue_routine = False
        break
    
    # Calculate current ball position
    x, y, phase = get_ball_position(t)
    
    # Update ball position
    ball.pos = [x, y]
    
    # Update background height and position
    background.height = y - y_min
    background.pos = [0, y_min + (y - y_min)/2]
    
    # Update phase text
    phase_text.text = phase
    
    # Update timer text - show remaining time
    time_left = int(total_duration - t)
    timer_text.text = f"Time remaining: {time_left}s"
    
    # Draw stimuli
    background.draw()
    path_line.draw()
    ball.draw()
    phase_text.draw()
    timer_text.draw()
    
    # Check for quit (the Esc key)
    keys = event.getKeys(keyList=["escape"])
    if 'escape' in keys:
        continueRoutine = False
        core.quit()  # This will exit the experiment
    
    # Refresh the screen
    win.flip()
#Clean up
import gc
gc.collect()  # Force garbage collection

# Just clear the screen and wait
win.flip()
core.wait(2.0)