# This code is intended for a PsychoPy Builder Code Component.
# It is assumed that the following variables are defined by PsychoPy Builder
# or in a preceding Code Component:
# - win: The PsychoPy window object.
# - stress_duration: The total duration of the stress task in seconds.
# - outlet: An LSL outlet object (or similar) with a .push_sample() method for triggers.
# - wrong_sound: A pre-loaded PsychoPy sound object for incorrect feedback.
#   (Ensure 'from psychopy import sound' is in the component where wrong_sound is defined)

# Import necessary PsychoPy modules and standard Python libraries
from psychopy import visual, core, event, sound # sound is imported here for completeness,
                                                # but wrong_sound object is expected from prior component
import random

# --- Initialize variables ---
current_number = random.randint(1000, 1999) # Starting number for subtraction
duration = stress_duration  # Total duration from Builder variable
last_response_time = 0  # To keep track of the previous trial's response time for the speed warning
clock = core.Clock()
start_time = clock.getTime()

# --- Define Screen Layout Parameters ---
screen_width_norm = 2.0
subject_view_width_ratio = 2/3
experimenter_view_width_ratio = 1/3
divider_x_pos = 1/3
subject_section_center_x = -1/3
experimenter_section_center_x = 2/3

# --- Create Stimuli ---
divider_line = visual.Line(
    win,
    start=(divider_x_pos, 1),
    end=(divider_x_pos, -1),
    lineColor="white",
    lineWidth=5
)
text_4digit_subject = visual.TextStim(
    win, 
    text=str(current_number), 
    pos=(subject_section_center_x, 0.2), 
    height=0.1, 
    color="white",
    font='Arial'
)
text_instruction_subject = visual.TextStim(
    win, 
    text="Keep subtracting 13", 
    pos=(subject_section_center_x, 0.1), 
    height=0.05, 
    color="white",
    font='Arial'
)
speed_warning_subject = visual.TextStim(
    win, 
    text="FASTER! You're Slow!", 
    pos=(subject_section_center_x, 0.3),
    height=0.08, 
    color="red", 
    bold=True, 
    italic=False, 
    font='Arial'
)
text_correct_answer_exp = visual.TextStim(
    win, 
    text="",
    pos=(experimenter_section_center_x, 0.2), 
    height=0.1, 
    color="lime",
    font='Arial'
)
text_exp_feedback = visual.TextStim(
    win, 
    text="", 
    pos=(experimenter_section_center_x, 0.0), 
    height=0.07, 
    color="white",
    font='Arial'
)
MAX_PROGRESS_BAR_WIDTH = 0.8 
progress_bar = visual.Rect(
    win, 
    width=MAX_PROGRESS_BAR_WIDTH, 
    height=0.05, 
    fillColor="white", 
    lineColor=None, 
    pos=(0, -0.4)
)
time_left_text = visual.TextStim(
    win, 
    text="Time left:", 
    pos=(-0.7, -0.45), 
    height=0.04, 
    color="white", 
    alignHoriz='left',
    font='Arial'
)
time_value_text = visual.TextStim(
    win,
    text="",
    pos=(-0.45, -0.45),
    height=0.04,
    color="white",
    alignHoriz='left',
    font='Arial'
)

# --- Main Experiment Loop ---
print("DEBUG: Starting main experiment loop for serial subtraction task.")
if 'wrong_sound' in globals() and wrong_sound is not None:
    print(f"DEBUG: At start of task, wrong_sound object: {wrong_sound}, Path: {getattr(wrong_sound, 'fileName', 'N/A')}, Volume: {getattr(wrong_sound, 'volume', 'N/A')}")
else:
    print("DEBUG: At start of task, wrong_sound is not available or is None.")


while True:
    current_loop_time = clock.getTime()
    time_elapsed = current_loop_time - start_time
    time_left = duration - time_elapsed
    
    if time_left <= 0:
        time_left = 0 
        break 
    
    trial_start_time = current_loop_time
    
    if 'outlet' in globals() and outlet is not None:
        win.callOnFlip(outlet.push_sample, x=[40])
    
    correct_answer_for_this_trial = current_number - 13
    
    text_4digit_subject.text = str(current_number)
    text_correct_answer_exp.text = f"Ans: {correct_answer_for_this_trial}"
    text_exp_feedback.text = ""

    progress_ratio = time_left / duration
    current_bar_width = progress_ratio * MAX_PROGRESS_BAR_WIDTH
    progress_bar.width = current_bar_width
    progress_bar.pos = ( (current_bar_width / 2) - (MAX_PROGRESS_BAR_WIDTH / 2) , -0.4)
    time_value_text.text = f"{max(0, int(time_left))}s"

    responded_this_trial = False
    experimenter_response_key = None
    event.clearEvents()

    while not responded_this_trial:
        current_input_phase_time = clock.getTime()
        time_elapsed_input = current_input_phase_time - start_time
        time_left_input = duration - time_elapsed_input

        if time_left_input <= 0:
            time_left = 0
            break 

        progress_ratio_input = time_left_input / duration
        current_bar_width_input = progress_ratio_input * MAX_PROGRESS_BAR_WIDTH
        progress_bar.width = current_bar_width_input
        progress_bar.pos = ( (current_bar_width_input / 2) - (MAX_PROGRESS_BAR_WIDTH / 2) , -0.4)
        time_value_text.text = f"{max(0, int(time_left_input))}s"

        text_4digit_subject.draw()
        text_instruction_subject.draw()
        if last_response_time >= 3: 
            speed_warning_subject.draw()
        
        text_correct_answer_exp.draw()
        
        divider_line.draw()
        progress_bar.draw()
        time_left_text.draw()
        time_value_text.draw()
        
        win.flip()

        keys = event.getKeys(keyList=['left', 'right', 'escape'])
        if 'escape' in keys:
            if 'outlet' in globals() and outlet is not None: 
                outlet.push_sample(x=[2])
            core.quit()
        
        if 'left' in keys:
            experimenter_response_key = 'left'
            responded_this_trial = True
        elif 'right' in keys:
            experimenter_response_key = 'right'
            responded_this_trial = True
        
        if time_left <= 0 : break 

    if time_left <= 0: break 

    response_time_for_this_trial = clock.getTime() - trial_start_time

    if experimenter_response_key == 'left': 
        text_exp_feedback.text = "WRONG"
        text_exp_feedback.color = "red"
        
        # --- Debugging Sound ---
        if 'wrong_sound' in globals() and wrong_sound is not None:
            print(f"DEBUG: 'left' key pressed. wrong_sound object found in globals: {wrong_sound}")
            # Check fileName attribute, which stores the path if loaded
            print(f"DEBUG: wrong_sound path: {getattr(wrong_sound, 'fileName', 'N/A')}")
            # Check volume attribute
            print(f"DEBUG: wrong_sound volume: {getattr(wrong_sound, 'volume', 'N/A')}")
            # Check status before attempting to play
            print(f"DEBUG: wrong_sound status before play: {getattr(wrong_sound, 'status', 'N/A')}")
            
            if hasattr(wrong_sound, 'play'):
                try:
                    wrong_sound.play()
                    # core.wait(0.01) # Optional: tiny wait, usually not needed for sound.play()
                    print(f"DEBUG: wrong_sound.play() called. Status after play call: {getattr(wrong_sound, 'status', 'N/A')}")
                except Exception as e:
                    print(f"DEBUG: Error during wrong_sound.play(): {e}")
            else:
                print("DEBUG: wrong_sound object does not have a 'play' method.")
        else:
            print("DEBUG: 'left' key pressed. wrong_sound object not found in globals or is None.")
            if 'wrong_sound' not in globals():
                print("DEBUG: 'wrong_sound' is not in globals().")
            # else: # This part is redundant if wrong_sound is None
            # print(f"DEBUG: 'wrong_sound' in globals(), but its value is: {globals().get('wrong_sound')}")
        # --- End Debugging Sound ---

    elif experimenter_response_key == 'right': 
        text_exp_feedback.text = "CORRECT"
        text_exp_feedback.color = "green"
    
    text_4digit_subject.draw() 
    text_instruction_subject.draw()
    if last_response_time >= 3: 
        speed_warning_subject.draw()
        
    text_correct_answer_exp.draw() 
    text_exp_feedback.draw() 
    
    divider_line.draw()
    progress_bar.draw() 
    time_left_text.draw()
    time_value_text.draw()
    
    win.flip()
    core.wait(0.75) 

    current_number = correct_answer_for_this_trial
    last_response_time = response_time_for_this_trial

# --- End of Experiment ---
print("DEBUG: Exiting main experiment loop for serial subtraction task.")
if 'outlet' in globals() and outlet is not None:
    outlet.push_sample(x=[2])

#final_message = visual.TextStim(win, text="Task finished.", pos=(0,0), height=0.1, color="white")
#final_message.draw()
win.flip()
core.wait(2.0)
