8.0430 	WARNING 	User requested fullscreen with size [1536  864], but screen is actually [1920, 1080]. Using actual size
8.7528 	EXP 	Created window1 = Window(allowGUI=True, allowStencil=True, autoLog=True, backendConf=UNKNOWN, backgroundFit=<method-wrapper '__getattribute__' of attributeSetter object at 0x000001AC70297CA0>, backgroundImage=<method-wrapper '__getattribute__' of attributeSetter object at 0x000001AC70297C40>, bitsMode=UNKNOWN, blendMode='avg', bpc=(8, 8, 8), color=array([0, 0, 0]), colorSpace='rgb', depthBits=8, fullscr=<method-wrapper '__getattribute__' of attributeSetter object at 0x000001AC70297A90>, gamma=None, gammaErrorPolicy='raise', lms=UNKNOWN, monitor=<psychopy.monitors.calibTools.Monitor object at 0x000001AC708AE760>, multiSample=False, name='window1', numSamples=2, pos=(0, 0), screen=0, size=array([1920, 1080]), stencilBits=8, stereo=False, title='PsychoPy', units='height', useFBO=True, useRetina=False, viewOri=0.0, viewPos=None, viewScale=None, waitBlanking=True, winType='pyglet')
8.7530 	EXP 	window1: mouseVisible = True
8.7530 	EXP 	window1: backgroundImage = ''
8.7531 	EXP 	window1: backgroundFit = 'none'
8.7564 	EXP 	window1: Attempting to measure frame rate of screen (0) ...
8.7565 	EXP 	window1: recordFrameIntervals = False
8.9182 	EXP 	window1: recordFrameIntervals = True
9.1017 	EXP 	Screen (0) actual frame rate measured at 60.04Hz
9.1018 	EXP 	window1: recordFrameIntervals = False
9.1022 	EXP 	window1: mouseVisible = False
12.6128 	EXP 	01_resting_state: status = STARTED
13.0440 	EXP 	window1: mouseVisible = True
13.1421 	EXP 	Created text = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.1423 	EXP 	window1: mouseVisible = True
13.1496 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
13.1497 	EXP 	window1: mouseVisible = True
13.1655 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.1677 	EXP 	Sound exp_end_bip set volume 1.000
13.1679 	EXP 	window1: mouseVisible = True
13.1815 	EXP 	Created text_2 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_2', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.1861 	EXP 	window1: mouseVisible = True
13.2154 	EXP 	window1: mouseVisible = True
13.2237 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.2264 	EXP 	Sound exp_end_bip set volume 1.000
13.2269 	EXP 	window1: mouseVisible = True
13.2352 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.2354 	EXP 	window1: mouseVisible = True
13.2429 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.2452 	EXP 	Sound exp_end_bip set volume 1.000
13.2454 	EXP 	window1: mouseVisible = True
13.2531 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.2551 	EXP 	Sound exp_end_bip set volume 1.000
13.2553 	EXP 	window1: mouseVisible = True
13.2609 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.2612 	EXP 	window1: mouseVisible = True
13.2684 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.2703 	EXP 	Sound exp_end_bip set volume 1.000
13.2705 	EXP 	window1: mouseVisible = True
13.2863 	EXP 	Created stroop_instr = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='stroop_instr', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.2865 	EXP 	window1: mouseVisible = True
13.2980 	EXP 	Created instrText_4 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-2.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_4', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.2981 	EXP 	window1: mouseVisible = True
13.2999 	EXP 	Created stim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.15, italic=False, languageStyle='LTR', name='stim', opacity=1.0, ori=0, pos=array([0., 0.]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
13.3002 	EXP 	window1: mouseVisible = True
13.3003 	EXP 	window1: mouseVisible = True
13.3013 	EXP 	window1: mouseVisible = True
13.3018 	EXP 	window1: mouseVisible = True
13.3063 	EXP 	window1: mouseVisible = True
13.3064 	EXP 	window1: mouseVisible = True
13.3069 	EXP 	window1: mouseVisible = True
13.3073 	EXP 	window1: mouseVisible = True
13.3305 	EXP 	window1: mouseVisible = True
13.3354 	EXP 	Created instrText_2 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-4.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_2', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.3356 	EXP 	window1: mouseVisible = True
13.3358 	EXP 	window1: mouseVisible = True
13.3362 	EXP 	window1: mouseVisible = True
13.3366 	EXP 	window1: mouseVisible = True
13.3405 	EXP 	window1: mouseVisible = True
13.3406 	EXP 	window1: mouseVisible = True
13.3414 	EXP 	window1: mouseVisible = True
13.3419 	EXP 	window1: mouseVisible = True
13.3468 	EXP 	window1: mouseVisible = True
13.3470 	EXP 	window1: mouseVisible = True
13.3477 	EXP 	window1: mouseVisible = True
13.3484 	EXP 	window1: mouseVisible = True
13.3529 	EXP 	window1: mouseVisible = True
13.3531 	EXP 	window1: mouseVisible = True
13.3535 	EXP 	window1: mouseVisible = True
13.3539 	EXP 	window1: mouseVisible = True
13.3577 	EXP 	window1: mouseVisible = True
13.3632 	EXP 	Created instrText_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-3.0, draggable=False, flipHoriz=False, flipVert=False, font='Arial', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='instrText_3', opacity=1.0, ori=0, pos=array([0. , 0.4]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.3633 	EXP 	window1: mouseVisible = True
13.3802 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.3824 	EXP 	Sound exp_end_bip set volume 1.000
13.3826 	EXP 	window1: mouseVisible = True
13.3927 	EXP 	Created text_3 = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_3', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.3932 	EXP 	window1: mouseVisible = True
13.4010 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.4030 	EXP 	Sound exp_end_bip set volume 1.000
13.4032 	EXP 	window1: mouseVisible = True
13.4116 	EXP 	Created text = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.4118 	EXP 	window1: mouseVisible = True
13.4159 	EXP 	Created cross = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='cross', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='+', units='height', win=Window(...), wrapWidth=1)
13.4162 	EXP 	window1: mouseVisible = True
13.4309 	EXP 	Created text_q_stress = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='text_q_stress', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text=str(...), units='height', win=Window(...), wrapWidth=1)
13.4329 	EXP 	Sound exp_end_bip set volume 1.000
13.4331 	EXP 	window1: mouseVisible = True
13.4411 	EXP 	Created EndMessage = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=-1.0, draggable=False, flipHoriz=False, flipVert=False, font='Open Sans', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='EndMessage', opacity=1.0, ori=0.0, pos=array([0., 0.]), rgb=UNKNOWN, text='Thank you!', units='height', win=Window(...), wrapWidth=1)
0.0041 	EXP 	video: autoLog = True
0.0041 	EXP 	trial_counter: autoLog = True
0.0041 	EXP 	fbtxt: autoLog = True
0.0041 	EXP 	trial_counter_2: autoLog = True
0.0224 	EXP 	text: autoDraw = True
1.7120 	DATA 	Keypress: f1
1.7217 	EXP 	text: autoDraw = False
1.7217 	EXP 	cross: autoDraw = True
12.7297 	EXP 	Sound exp_end_bip started
12.7291 	EXP 	cross: autoDraw = False
12.7291 	EXP 	cross: autoDraw = False
12.7291 	EXP 	text_q_stress: autoDraw = True
13.9176 	EXP 	Sound exp_end_bip stopped
16.8991 	DATA 	Keypress: f1
16.9091 	EXP 	text_q_stress: autoDraw = False
16.9091 	EXP 	text_2: autoDraw = True
17.8156 	DATA 	Keypress: f1
17.8236 	EXP 	text_2: autoDraw = False
17.8236 	EXP 	video: autoDraw = True
20.0557 	EXP 	Sound exp_end_bip started
20.0582 	EXP 	video: autoDraw = False
20.0582 	EXP 	video: autoDraw = False
20.0582 	EXP 	text_q_stress: autoDraw = True
20.9241 	EXP 	Sound exp_end_bip stopped
20.9242 	EXP 	Sound exp_end_bip paused
20.9269 	DATA 	Keypress: f1
20.9312 	EXP 	text_q_stress: autoDraw = False
20.9312 	EXP 	text_3: autoDraw = True
21.4418 	EXP 	window1: mouseVisible = True
21.4460 	EXP 	Created unnamed ShapeStim = ShapeStim(__class__=<class 'psychopy.visual.shape.ShapeStim'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=False, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0, 0, 0]), fillColorSpace='rgb', fillRGB=array([0, 0, 0]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=3, name='unnamed ShapeStim', opacity=0.35, ori=0.0, pos=array([0., 0.]), size=array([1., 1.]), units='height', vertices=ndarray(...), win=Window(...), windingRule=None)
21.4552 	EXP 	window1: mouseVisible = True
21.4634 	EXP 	Created unnamed Circle = Circle(__class__=<class 'psychopy.visual.circle.Circle'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=2, name='unnamed Circle', opacity=1.0, ori=0.0, pos=array([0., 0.]), size=array([1., 1.]), units='height', vertices=ndarray(...), win=Window(...))
21.4635 	EXP 	window1: mouseVisible = True
21.4655 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.05882353, 0.61568627, 0.84313725]), fillColorSpace='rgb', fillRGB=array([0.05882353, 0.61568627, 0.84313725]), interpolate=True, lineColor=array([0., 0., 0.]), lineColorSpace='rgb', lineRGB=array([0, 0, 0]), lineWidth=1.5, name='unnamed Rect', opacity=0.1, ori=0.0, pos=array([0., 0.]), size=array([1.77777778, 0.5       ]), units='height', vertices=ndarray(...), win=Window(...))
21.4657 	EXP 	window1: mouseVisible = True
21.4731 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([ 0.  , -0.35]), rgb=UNKNOWN, text='Inhale', units='height', win=Window(...), wrapWidth=1)
21.4733 	EXP 	window1: mouseVisible = True
21.4751 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.03, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([0. , 0.4]), rgb=UNKNOWN, text='', units='height', win=Window(...), wrapWidth=1)
21.4752 	EXP 	window1: mouseVisible = True
21.4774 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([-0.0462963, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.4776 	EXP 	window1: mouseVisible = True
21.4799 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([-0.0212963, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.4800 	EXP 	window1: mouseVisible = True
21.4821 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.0037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.4822 	EXP 	window1: mouseVisible = True
21.4848 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.0287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.4849 	EXP 	window1: mouseVisible = True
21.4870 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.0537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.4871 	EXP 	window1: mouseVisible = True
21.4892 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.0787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.4895 	EXP 	window1: mouseVisible = True
21.4914 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.1037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.4915 	EXP 	window1: mouseVisible = True
21.4932 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.1287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.4934 	EXP 	window1: mouseVisible = True
21.4950 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.1537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.4951 	EXP 	window1: mouseVisible = True
21.4967 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.1787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.4968 	EXP 	window1: mouseVisible = True
21.4983 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.2037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.4984 	EXP 	window1: mouseVisible = True
21.5003 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.2287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.5004 	EXP 	window1: mouseVisible = True
21.5025 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.2537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.5026 	EXP 	window1: mouseVisible = True
21.5043 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.2787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.5044 	EXP 	window1: mouseVisible = True
21.5062 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.3037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.5063 	EXP 	window1: mouseVisible = True
21.5082 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.3287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.5083 	EXP 	window1: mouseVisible = True
21.5103 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.3537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.5104 	EXP 	window1: mouseVisible = True
21.5125 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.3787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.5126 	EXP 	window1: mouseVisible = True
21.5146 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.4037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.5147 	EXP 	window1: mouseVisible = True
21.5166 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.4287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.5168 	EXP 	window1: mouseVisible = True
21.5198 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.4537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.5199 	EXP 	window1: mouseVisible = True
21.5218 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.4787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.5219 	EXP 	window1: mouseVisible = True
21.5234 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.5037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.5235 	EXP 	window1: mouseVisible = True
21.5251 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.5287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.5252 	EXP 	window1: mouseVisible = True
21.5270 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.5537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.5271 	EXP 	window1: mouseVisible = True
21.5286 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.5787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.5288 	EXP 	window1: mouseVisible = True
21.5308 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.6037037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.5309 	EXP 	window1: mouseVisible = True
21.5324 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.6287037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.5325 	EXP 	window1: mouseVisible = True
21.5347 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.6537037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.5349 	EXP 	window1: mouseVisible = True
21.5366 	EXP 	Created unnamed Rect = Rect(__class__=<class 'psychopy.visual.rect.Rect'>, anchor=('center', 'center'), autoDraw=False, autoLog=True, closeShape=True, color=method-wrapper(...), colorSpace='rgb', contrast=None, depth=0, draggable=False, fillColor=array([0.35686275, 0.69411765, 0.80392157]), fillColorSpace='rgb', fillRGB=array([0.35686275, 0.69411765, 0.80392157]), interpolate=True, lineColor=array([1., 1., 1.]), lineColorSpace='rgb', lineRGB=array([1, 1, 1]), lineWidth=1, name='unnamed Rect', opacity=1.0, ori=0.0, pos=array([ 0.6787037, -0.4      ]), size=array([0.02, 0.05]), units='height', vertices=ndarray(...), win=Window(...))
21.5367 	EXP 	window1: mouseVisible = True
21.5448 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz=method-wrapper(...), alignText='center', alignVert=method-wrapper(...), anchorHoriz='center', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([-0.49444444, -0.4       ]), rgb=UNKNOWN, text='Round: 1/3', units='height', win=Window(...), wrapWidth=1)
21.5450 	EXP 	window1: mouseVisible = True
21.5451 	WARNING 	TextStim.alignHoriz is deprecated. Use alignText and anchorHoriz attributes instead
21.5513 	EXP 	Created unnamed TextStim = TextStim(__class__=<class 'psychopy.visual.text.TextStim'>, alignHoriz='left', alignText='left', alignVert=method-wrapper(...), anchorHoriz='left', anchorVert='center', antialias=True, autoDraw=False, autoLog=True, bold=False, color=array([1., 1., 1.]), colorSpace='rgb', contrast=1.0, depth=0, draggable=False, flipHoriz=False, flipVert=False, font='', fontFiles=[], height=0.05, italic=False, languageStyle='LTR', name='unnamed TextStim', opacity=1.0, ori=0.0, pos=array([-0.2462963, -0.4      ]), rgb=UNKNOWN, text='Breaths left:', units='height', win=Window(...), wrapWidth=1)
21.5829 	DATA 	Keypress: f1
21.5876 	EXP 	text_3: autoDraw = False
21.5876 	EXP 	unnamed TextStim: height = 0.05
21.5876 	EXP 	unnamed TextStim: height = 0.05
21.5876 	EXP 	unnamed TextStim: height = 0.05
21.5876 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
21.5876 	EXP 	unnamed TextStim: height = 0.03
21.5876 	EXP 	unnamed TextStim: height = 0.03
21.5876 	EXP 	unnamed TextStim: height = 0.03
21.5876 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
21.5876 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.6098 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
21.6098 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
21.6098 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.6365 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
21.6365 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
21.6365 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.6569 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
21.6569 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
21.6569 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.6787 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
21.6787 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
21.6787 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.6985 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
21.6985 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
21.6985 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.7189 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
21.7189 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
21.7189 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.7398 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
21.7398 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
21.7398 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.7609 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
21.7609 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
21.7609 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.7831 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
21.7831 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
21.7831 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.8063 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
21.8063 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
21.8063 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.8285 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
21.8285 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
21.8285 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.8514 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
21.8514 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
21.8514 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.8726 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
21.8726 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
21.8726 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.8946 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
21.8946 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
21.8946 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.9159 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
21.9159 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
21.9159 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.9363 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
21.9363 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
21.9363 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.9578 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
21.9578 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
21.9578 	EXP 	unnamed TextStim: text = 'Round: 1/3'
21.9787 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
21.9787 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
21.9787 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.0008 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.0008 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.0008 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.0254 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.0254 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.0254 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.0473 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.0473 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.0473 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.0698 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.0698 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.0698 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.0901 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.0901 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.0901 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.1117 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.1117 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.1117 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.1327 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.1327 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.1327 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.1535 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.1535 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.1535 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.1732 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.1732 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.1732 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.1927 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.1927 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.1927 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.2137 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.2137 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.2137 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.2354 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.2354 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.2354 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.2561 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.2561 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.2561 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.2764 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.2764 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.2764 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.2969 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.2969 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.2969 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.3171 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.3171 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.3171 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.3384 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.3384 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.3384 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.3613 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.3613 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.3613 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.3867 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.3867 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.3867 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.4076 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.4076 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.4076 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.4307 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.4307 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.4307 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.4533 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.4533 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.4533 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.4739 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.4739 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.4739 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.4947 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.4947 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.4947 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.5147 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.5147 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.5147 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.5355 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.5355 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.5355 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.5571 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.5571 	EXP 	unnamed TextStim: text = 'Time remaining: 359s'
22.5571 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.5870 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.5870 	EXP 	unnamed TextStim: height = 0.03
22.5870 	EXP 	unnamed TextStim: height = 0.03
22.5870 	EXP 	unnamed TextStim: height = 0.03
22.5870 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
22.5870 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.6129 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.6129 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
22.6129 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.6399 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.6399 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
22.6399 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.6615 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.6615 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
22.6615 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.6819 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.6819 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
22.6819 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.7020 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.7020 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
22.7020 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.7260 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.7260 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
22.7260 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.7502 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.7502 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
22.7502 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.7748 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.7748 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
22.7748 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.8016 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.8016 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
22.8016 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.8263 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.8263 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
22.8263 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.8505 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.8505 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
22.8505 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.8764 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.8764 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
22.8764 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.8981 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.8981 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
22.8981 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.9216 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.9216 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
22.9216 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.9449 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.9449 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
22.9449 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.9679 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.9679 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
22.9679 	EXP 	unnamed TextStim: text = 'Round: 1/3'
22.9907 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
22.9907 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
22.9907 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.0158 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
23.0158 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.0158 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.0426 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
23.0426 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.0426 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.0690 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (1/30)'
23.0690 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.0690 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.0979 	EXP 	unnamed TextStim: height = 0.05
23.0979 	EXP 	unnamed TextStim: height = 0.05
23.0979 	EXP 	unnamed TextStim: height = 0.05
23.0979 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.0979 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.0979 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.1206 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.1206 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.1206 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.1417 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.1417 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.1417 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.1643 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.1643 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.1643 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.1867 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.1867 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.1867 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.2080 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.2080 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.2080 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.2296 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.2296 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.2296 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.2508 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.2508 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.2508 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.2754 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.2754 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.2754 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.3000 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.3000 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.3000 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.3267 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.3267 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.3267 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.3556 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.3556 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.3556 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.3811 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.3811 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.3811 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.4090 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.4090 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.4090 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.4364 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.4364 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.4364 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.4615 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.4615 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.4615 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.4806 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.4806 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.4806 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.5015 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.5015 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.5015 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.5221 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.5221 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.5221 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.5446 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.5446 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.5446 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.5743 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.5743 	EXP 	unnamed TextStim: text = 'Time remaining: 358s'
23.5743 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.6021 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.6021 	EXP 	unnamed TextStim: height = 0.03
23.6021 	EXP 	unnamed TextStim: height = 0.03
23.6021 	EXP 	unnamed TextStim: height = 0.03
23.6021 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
23.6021 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.6278 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.6278 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
23.6278 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.6485 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.6485 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
23.6485 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.6689 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.6689 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
23.6689 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.6890 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.6890 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
23.6890 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.7121 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.7121 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
23.7121 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.7356 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.7356 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
23.7356 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.7602 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.7602 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
23.7602 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.7815 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.7815 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
23.7815 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.8023 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.8023 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
23.8023 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.8270 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.8270 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
23.8270 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.8515 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.8515 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
23.8515 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.8748 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.8748 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
23.8748 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.9003 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.9003 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
23.9003 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.9234 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.9234 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
23.9234 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.9463 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.9463 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
23.9463 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.9701 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.9701 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
23.9701 	EXP 	unnamed TextStim: text = 'Round: 1/3'
23.9916 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
23.9916 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
23.9916 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.0145 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.0145 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.0145 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.0348 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.0348 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.0348 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.0567 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.0567 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.0567 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.0767 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.0767 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.0767 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.0971 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.0971 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.0971 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.1209 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.1209 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.1209 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.1404 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.1404 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.1404 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.1613 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.1613 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.1613 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.1840 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.1840 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.1840 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.2042 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.2042 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.2042 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.2249 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.2249 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.2249 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.2467 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.2467 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.2467 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.2682 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.2682 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.2682 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.2911 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.2911 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.2911 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.3102 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.3102 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.3102 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.3299 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.3299 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.3299 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.3501 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.3501 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.3501 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.3736 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.3736 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.3736 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.3965 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.3965 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.3965 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.4167 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.4167 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.4167 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.4364 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.4364 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.4364 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.4560 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.4560 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.4560 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.4754 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.4754 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.4754 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.4948 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.4948 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.4948 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.5134 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.5134 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.5134 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.5321 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.5321 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.5321 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.5519 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (1/30)'
24.5519 	EXP 	unnamed TextStim: text = 'Time remaining: 357s'
24.5519 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.5820 	EXP 	unnamed TextStim: height = 0.05
24.5820 	EXP 	unnamed TextStim: height = 0.05
24.5820 	EXP 	unnamed TextStim: height = 0.05
24.5820 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
24.5820 	EXP 	unnamed TextStim: height = 0.03
24.5820 	EXP 	unnamed TextStim: height = 0.03
24.5820 	EXP 	unnamed TextStim: height = 0.03
24.5820 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
24.5820 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.6064 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
24.6064 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
24.6064 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.6288 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
24.6288 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
24.6288 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.6478 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
24.6478 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
24.6478 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.6674 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
24.6674 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
24.6674 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.6869 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
24.6869 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
24.6869 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.7051 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
24.7051 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
24.7051 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.7246 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
24.7246 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
24.7246 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.7448 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
24.7448 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
24.7448 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.7681 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
24.7681 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
24.7681 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.7916 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
24.7916 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
24.7916 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.8112 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
24.8112 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
24.8112 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.8327 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
24.8327 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
24.8327 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.8534 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
24.8534 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
24.8534 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.8757 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
24.8757 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
24.8757 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.8969 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
24.8969 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
24.8969 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.9212 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
24.9212 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
24.9212 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.9415 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
24.9415 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
24.9415 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.9623 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
24.9623 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
24.9623 	EXP 	unnamed TextStim: text = 'Round: 1/3'
24.9827 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
24.9827 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
24.9827 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.0037 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.0037 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.0037 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.0253 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.0253 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.0253 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.0462 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.0462 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.0462 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.0674 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.0674 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.0674 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.0876 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.0876 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.0876 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.1120 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.1120 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.1120 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.1340 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.1340 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.1340 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.1577 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.1577 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.1577 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.1828 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.1828 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.1828 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.2090 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.2090 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.2090 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.2297 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.2297 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.2297 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.2550 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.2550 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.2550 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.2759 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.2759 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.2759 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.2976 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.2976 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.2976 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.3200 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.3200 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.3200 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.3437 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.3437 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.3437 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.3706 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.3706 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.3706 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.3937 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.3937 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.3937 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.4160 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.4160 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.4160 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.4424 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.4424 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.4424 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.4638 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.4638 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.4638 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.4906 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.4906 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.4906 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.5142 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.5142 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.5142 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.5367 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.5367 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.5367 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.5598 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.5598 	EXP 	unnamed TextStim: text = 'Time remaining: 356s'
25.5598 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.5862 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.5862 	EXP 	unnamed TextStim: height = 0.03
25.5862 	EXP 	unnamed TextStim: height = 0.03
25.5862 	EXP 	unnamed TextStim: height = 0.03
25.5862 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
25.5862 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.6097 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.6097 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
25.6097 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.6310 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.6310 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
25.6310 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.6538 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.6538 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
25.6538 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.6760 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.6760 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
25.6760 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.7015 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.7015 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
25.7015 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.7308 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.7308 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
25.7308 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.7542 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.7542 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
25.7542 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.7765 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.7765 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
25.7765 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.7997 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.7997 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
25.7997 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.8206 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.8206 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
25.8206 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.8417 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.8417 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
25.8417 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.8627 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.8627 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
25.8627 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.8839 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.8839 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
25.8839 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.9056 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.9056 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
25.9056 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.9272 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.9272 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
25.9272 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.9513 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.9513 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
25.9513 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.9737 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.9737 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
25.9737 	EXP 	unnamed TextStim: text = 'Round: 1/3'
25.9987 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
25.9987 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
25.9987 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.0207 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
26.0207 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.0207 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.0452 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
26.0452 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.0452 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.0676 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (2/30)'
26.0676 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.0676 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.0925 	EXP 	unnamed TextStim: height = 0.05
26.0925 	EXP 	unnamed TextStim: height = 0.05
26.0925 	EXP 	unnamed TextStim: height = 0.05
26.0925 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.0925 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.0925 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.1161 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.1161 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.1161 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.1377 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.1377 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.1377 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.1599 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.1599 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.1599 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.1917 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.1917 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.1917 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.2136 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.2136 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.2136 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.2381 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.2381 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.2381 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.2624 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.2624 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.2624 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.2866 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.2866 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.2866 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.3124 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.3124 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.3124 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.3381 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.3381 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.3381 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.3610 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.3610 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.3610 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.3845 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.3845 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.3845 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.4083 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.4083 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.4083 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.4308 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.4308 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.4308 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.4536 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.4536 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.4536 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.4746 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.4746 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.4746 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.4946 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.4946 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.4946 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.5152 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.5152 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.5152 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.5362 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.5362 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.5362 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.5562 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.5562 	EXP 	unnamed TextStim: text = 'Time remaining: 355s'
26.5562 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.5809 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.5809 	EXP 	unnamed TextStim: height = 0.03
26.5809 	EXP 	unnamed TextStim: height = 0.03
26.5809 	EXP 	unnamed TextStim: height = 0.03
26.5809 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
26.5809 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.6026 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.6026 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
26.6026 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.6228 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.6228 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
26.6228 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.6429 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.6429 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
26.6429 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.6625 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.6625 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
26.6625 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.6867 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.6867 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
26.6867 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.7076 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.7076 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
26.7076 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.7275 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.7275 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
26.7275 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.7500 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.7500 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
26.7500 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.7712 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.7712 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
26.7712 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.7961 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.7961 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
26.7961 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.8224 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.8224 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
26.8224 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.8439 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.8439 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
26.8439 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.8663 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.8663 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
26.8663 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.8870 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.8870 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
26.8870 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.9077 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.9077 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
26.9077 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.9296 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.9296 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
26.9296 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.9523 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.9523 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
26.9523 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.9721 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.9721 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
26.9721 	EXP 	unnamed TextStim: text = 'Round: 1/3'
26.9950 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
26.9950 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
26.9950 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.0159 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.0159 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.0159 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.0366 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.0366 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.0366 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.0569 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.0569 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.0569 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.0776 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.0776 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.0776 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.0979 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.0979 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.0979 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.1202 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.1202 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.1202 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.1481 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.1481 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.1481 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.1688 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.1688 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.1688 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.1898 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.1898 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.1898 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.2132 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.2132 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.2132 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.2335 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.2335 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.2335 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.2551 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.2551 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.2551 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.2779 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.2779 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.2779 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.3018 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.3018 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.3018 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.3268 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.3268 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.3268 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.3517 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.3517 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.3517 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.3771 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.3771 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.3771 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.4043 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.4043 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.4043 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.4298 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.4298 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.4298 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.4514 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.4514 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.4514 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.4710 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.4710 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.4710 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.4906 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.4906 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.4906 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.5107 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.5107 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.5107 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.5350 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.5350 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.5350 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.5573 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (2/30)'
27.5573 	EXP 	unnamed TextStim: text = 'Time remaining: 354s'
27.5573 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.5855 	EXP 	unnamed TextStim: height = 0.05
27.5855 	EXP 	unnamed TextStim: height = 0.05
27.5855 	EXP 	unnamed TextStim: height = 0.05
27.5855 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
27.5855 	EXP 	unnamed TextStim: height = 0.03
27.5855 	EXP 	unnamed TextStim: height = 0.03
27.5855 	EXP 	unnamed TextStim: height = 0.03
27.5855 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
27.5855 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.6070 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
27.6070 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
27.6070 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.6278 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
27.6278 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
27.6278 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.6474 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
27.6474 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
27.6474 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.6681 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
27.6681 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
27.6681 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.6899 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
27.6899 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
27.6899 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.7100 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
27.7100 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
27.7100 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.7324 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
27.7324 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
27.7324 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.7527 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
27.7527 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
27.7527 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.7731 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
27.7731 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
27.7731 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.7944 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
27.7944 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
27.7944 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.8147 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
27.8147 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
27.8147 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.8352 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
27.8352 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
27.8352 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.8570 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
27.8570 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
27.8570 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.8797 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
27.8797 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
27.8797 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.9014 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
27.9014 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
27.9014 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.9233 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
27.9233 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
27.9233 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.9442 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
27.9442 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
27.9442 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.9699 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
27.9699 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
27.9699 	EXP 	unnamed TextStim: text = 'Round: 1/3'
27.9903 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
27.9903 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
27.9903 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.0134 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.0134 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.0134 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.0368 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.0368 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.0368 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.0631 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.0631 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.0631 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.0845 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.0845 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.0845 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.1079 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.1079 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.1079 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.1310 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.1310 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.1310 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.1516 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.1516 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.1516 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.1759 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.1759 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.1759 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.1950 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.1950 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.1950 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.2163 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.2163 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.2163 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.2373 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.2373 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.2373 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.2574 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.2574 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.2574 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.2784 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.2784 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.2784 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.2993 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.2993 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.2993 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.3234 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.3234 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.3234 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.3426 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.3426 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.3426 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.3632 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.3632 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.3632 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.3846 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.3846 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.3846 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.4061 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.4061 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.4061 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.4260 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.4260 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.4260 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.4465 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.4465 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.4465 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.4679 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.4679 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.4679 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.4888 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.4888 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.4888 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.5099 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.5099 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.5099 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.5312 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.5312 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.5312 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.5518 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.5518 	EXP 	unnamed TextStim: text = 'Time remaining: 353s'
28.5518 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.5761 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.5761 	EXP 	unnamed TextStim: height = 0.03
28.5761 	EXP 	unnamed TextStim: height = 0.03
28.5761 	EXP 	unnamed TextStim: height = 0.03
28.5761 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
28.5761 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.5983 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.5983 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
28.5983 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.6198 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.6198 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
28.6198 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.6413 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.6413 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
28.6413 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.6619 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.6619 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
28.6619 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.6836 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.6836 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
28.6836 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.7085 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.7085 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
28.7085 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.7338 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.7338 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
28.7338 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.7565 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.7565 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
28.7565 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.7777 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.7777 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
28.7777 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.8004 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.8004 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
28.8004 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.8222 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.8222 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
28.8222 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.8432 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.8432 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
28.8432 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.8642 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.8642 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
28.8642 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.8851 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.8851 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
28.8851 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.9063 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.9063 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
28.9063 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.9270 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.9270 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
28.9270 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.9482 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.9482 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
28.9482 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.9704 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.9704 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
28.9704 	EXP 	unnamed TextStim: text = 'Round: 1/3'
28.9954 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
28.9954 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
28.9954 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.0206 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
29.0206 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.0206 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.0478 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
29.0478 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.0478 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.0687 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (3/30)'
29.0687 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.0687 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.0932 	EXP 	unnamed TextStim: height = 0.05
29.0932 	EXP 	unnamed TextStim: height = 0.05
29.0932 	EXP 	unnamed TextStim: height = 0.05
29.0932 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.0932 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.0932 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.1192 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.1192 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.1192 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.1421 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.1421 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.1421 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.1646 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.1646 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.1646 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.1868 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.1868 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.1868 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.2149 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.2149 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.2149 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.2399 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.2399 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.2399 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.2611 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.2611 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.2611 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.2853 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.2853 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.2853 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.3087 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.3087 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.3087 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.3315 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.3315 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.3315 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.3530 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.3530 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.3530 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.3750 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.3750 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.3750 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.3957 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.3957 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.3957 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.4188 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.4188 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.4188 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.4441 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.4441 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.4441 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.4728 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.4728 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.4728 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.4942 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.4942 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.4942 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.5187 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.5187 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.5187 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.5406 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.5406 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.5406 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.5663 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.5663 	EXP 	unnamed TextStim: text = 'Time remaining: 352s'
29.5663 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.5955 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.5955 	EXP 	unnamed TextStim: height = 0.03
29.5955 	EXP 	unnamed TextStim: height = 0.03
29.5955 	EXP 	unnamed TextStim: height = 0.03
29.5955 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
29.5955 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.6168 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.6168 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
29.6168 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.6404 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.6404 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
29.6404 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.6634 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.6634 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
29.6634 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.6842 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.6842 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
29.6842 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.7047 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.7047 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
29.7047 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.7253 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.7253 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
29.7253 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.7458 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.7458 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
29.7458 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.7666 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.7666 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
29.7666 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.7890 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.7890 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
29.7890 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.8098 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.8098 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
29.8098 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.8313 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.8313 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
29.8313 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.8518 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.8518 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
29.8518 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.8724 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.8724 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
29.8724 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.9030 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.9030 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
29.9030 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.9248 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.9248 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
29.9248 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.9477 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.9477 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
29.9477 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.9710 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.9710 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
29.9710 	EXP 	unnamed TextStim: text = 'Round: 1/3'
29.9926 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
29.9926 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
29.9926 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.0129 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.0129 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.0129 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.0340 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.0340 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.0340 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.0529 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.0529 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.0529 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.0724 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.0724 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.0724 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.0936 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.0936 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.0936 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.1166 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.1166 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.1166 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.1391 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.1391 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.1391 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.1609 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.1609 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.1609 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.1827 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.1827 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.1827 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.2029 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.2029 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.2029 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.2317 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.2317 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.2317 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.2520 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.2520 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.2520 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.2718 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.2718 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.2718 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.2907 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.2907 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.2907 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.3114 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.3114 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.3114 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.3330 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.3330 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.3330 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.3546 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.3546 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.3546 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.3756 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.3756 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.3756 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.3955 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.3955 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.3955 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.4160 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.4160 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.4160 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.4360 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.4360 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.4360 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.4554 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.4554 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.4554 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.4798 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.4798 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.4798 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.5027 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.5027 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.5027 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.5314 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.5314 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.5314 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.5510 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.5510 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.5510 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.5702 	EXP 	unnamed TextStim: text = 'Round 1/3: Exhale (3/30)'
30.5702 	EXP 	unnamed TextStim: text = 'Time remaining: 351s'
30.5702 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.5959 	EXP 	unnamed TextStim: height = 0.05
30.5959 	EXP 	unnamed TextStim: height = 0.05
30.5959 	EXP 	unnamed TextStim: height = 0.05
30.5959 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
30.5959 	EXP 	unnamed TextStim: height = 0.03
30.5959 	EXP 	unnamed TextStim: height = 0.03
30.5959 	EXP 	unnamed TextStim: height = 0.03
30.5959 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
30.5959 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.6199 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
30.6199 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
30.6199 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.6412 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
30.6412 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
30.6412 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.6610 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
30.6610 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
30.6610 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.6818 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
30.6818 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
30.6818 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.7016 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
30.7016 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
30.7016 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.7206 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
30.7206 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
30.7206 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.7396 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
30.7396 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
30.7396 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.7593 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
30.7593 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
30.7593 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.7788 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
30.7788 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
30.7788 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.8000 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
30.8000 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
30.8000 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.8241 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
30.8241 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
30.8241 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.8467 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
30.8467 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
30.8467 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.8675 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
30.8675 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
30.8675 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.8907 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
30.8907 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
30.8907 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.9113 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
30.9113 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
30.9113 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.9381 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
30.9381 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
30.9381 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.9602 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
30.9602 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
30.9602 	EXP 	unnamed TextStim: text = 'Round: 1/3'
30.9813 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
30.9813 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
30.9813 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.0018 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
31.0018 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
31.0018 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.0232 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
31.0232 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
31.0232 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.0499 	EXP 	unnamed TextStim: text = 'Round 1/3: Inhale (4/30)'
31.0499 	EXP 	unnamed TextStim: text = 'Time remaining: 350s'
31.0499 	EXP 	unnamed TextStim: text = 'Round: 1/3'
31.0709 	DATA 	Keypress: escape
32.0336 	EXP 	window1: mouseVisible = True
