import random

# Initialize variables
current_number = random.randint(1000, 1999)
duration = stress_duration  # 3 minutes in seconds
last_response_time = 0
clock = core.Clock()
start_time = clock.getTime()

# Create stimuli with relative positioning
text_4digit = visual.TextStim(win, pos=(0, 0.2), height=0.1, color="white")
text_2digit = visual.TextStim(win, text="keep subtracting 13", pos=(0, 0.1), height=0.05, color="white")
text_feedback = visual.TextStim(win, pos=(0, 0), height=0.1, color="white")
input_box = visual.TextBox2(win, text='', font='Arial', pos=(0, -0.1), size=(0.5, 0.1), 
                            letterHeight=0.05, borderColor="white", fillColor="gray", 
                            editable=True, color='white')
speed_warning = visual.TextStim(win, text="FASTER! You're Slow!", pos=(0, 0.3), height=0.08, color="red", bold=True, italic=False, font='Arial')

# Create progress bar and time left text
progress_bar = visual.Rect(win, width=0.8, height=0.05, fillColor="white", lineColor=None, pos=(0, -0.4))
time_left_text = visual.TextStim(win, text="Time left", pos=(-0.8, -0.4), height=0.05, color="white", alignHoriz='left')

# Main experiment loop
while True:
    current_time = clock.getTime()
    time_left = duration - (current_time - start_time)
    
    if time_left <= 0:
        break  # Exit the loop when time is up
    
    trial_start_time = current_time
    
    # Send trigger for start of trial
    win.callOnFlip(outlet.push_sample, x=[40])
    
    # Update progress bar
    progress_bar.width = (time_left / duration) * 0.8
    progress_bar.pos = (progress_bar.width/2 - 0.4, -0.4)
    
    # Trial routine
    text_4digit.text = str(current_number)
    text_4digit.draw()
    text_2digit.draw()
    input_box.draw()
    progress_bar.draw()
    time_left_text.draw()
    if last_response_time >= 3:
        speed_warning.draw()
    
    win.flip()

    # Wait for user input
    input_box.text = ''  # Clear previous input
    user_input = None
    while user_input is None and time_left > 0:
        keys = event.getKeys(keyList=['return', 'escape'])
        if 'return' in keys and input_box.text:
            try:
                user_input = int(input_box.text)
            except ValueError:
                continue  # If input is not a valid number, continue waiting
        if 'escape' in keys:
            core.quit()
        
        current_time = clock.getTime()
        time_left = duration - (current_time - start_time)
        
        # Update progress bar
        progress_bar.width = (time_left / duration) * 0.8
        progress_bar.pos = (progress_bar.width/2 - 0.4, -0.4)
        
        text_4digit.draw()
        text_2digit.draw()
        input_box.draw()
        progress_bar.draw()
        time_left_text.draw()
        if last_response_time >= 3:
            speed_warning.draw()
        
        win.flip()
    
    if time_left <= 0:
        break  # Exit the main loop if time is up

    # Calculate correct answer and response time
    correct_answer = current_number - 13
    last_response_time = current_time - trial_start_time

    # Feedback routine
    if user_input == correct_answer:
        text_feedback.text = "Correct."
        text_feedback.color = "green"
    else:
        text_feedback.text = "WRONG!!!"
        text_feedback.color = "red"
        current_number = correct_answer  # Update for next trial if wrong
        wrong_sound.play()  # Play the wrong answer sound

    text_feedback.draw()
    progress_bar.draw()
    time_left_text.draw()
    if last_response_time >= 3:
        speed_warning.draw()
    
    win.flip()
    core.wait(0.5)

    current_number = correct_answer

# Send trigger for end of experiment
outlet.push_sample(x=[2])